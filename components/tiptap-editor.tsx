'use client';

import React from 'react';
import { useE<PERSON><PERSON>, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import Code from '@tiptap/extension-code';
import Blockquote from '@tiptap/extension-blockquote';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import Heading from '@tiptap/extension-heading';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Image from '@tiptap/extension-image';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Link as LinkIcon,
  Undo,
  Redo,
  Code as CodeIcon,
  Quote,
  Minus,
  Heading1,
  Heading2,
  Heading3,
  Table as TableIcon,
  Image as ImageIcon,
  Subscript as SubscriptIcon,
  Superscript as SuperscriptIcon,
  Palette,
  Highlighter,
} from 'lucide-react';

interface TipTapEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  editable?: boolean;
}

interface Variable {
  id: string;
  label: string;
  value: string;
}

interface TipTapEditorWithVariablesProps extends TipTapEditorProps {
  variables?: Variable[];
  showVariables?: boolean;
}

export function TipTapEditor({
  content,
  onChange,
  placeholder = 'Start typing...',
  className,
  editable = true,
}: TipTapEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: false, // We'll use the separate Heading extension
      }),
      Heading.configure({
        levels: [1, 2, 3, 4, 5, 6],
      }),
      Placeholder.configure({
        placeholder,
      }),
      TextStyle,
      Color,
      Highlight,
      Underline,
      Subscript,
      Superscript,
      Code,
      Blockquote,
      HorizontalRule,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline hover:text-blue-800',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto',
        },
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  if (!editor) {
    return null;
  }

  const ToolbarButton = ({
    onClick,
    isActive = false,
    disabled = false,
    children,
    title,
  }: {
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title: string;
  }) => (
    <Button
      type='button'
      variant={isActive ? 'default' : 'ghost'}
      size='sm'
      onClick={onClick}
      disabled={disabled}
      title={title}
      className='h-8 w-8 p-0'
    >
      {children}
    </Button>
  );

  return (
    <div className={cn('border rounded-lg', className)}>
      {editable && (
        <div className='border-b p-2 space-y-2'>
          {/* First Row - Text Formatting */}
          <div className='flex flex-wrap gap-1'>
            <ToolbarButton
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 1 }).run()
              }
              isActive={editor.isActive('heading', { level: 1 })}
              title='Heading 1'
            >
              <Heading1 className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 2 }).run()
              }
              isActive={editor.isActive('heading', { level: 2 })}
              title='Heading 2'
            >
              <Heading2 className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() =>
                editor.chain().focus().toggleHeading({ level: 3 }).run()
              }
              isActive={editor.isActive('heading', { level: 3 })}
              title='Heading 3'
            >
              <Heading3 className='h-4 w-4' />
            </ToolbarButton>

            <div className='w-px h-6 bg-border mx-1' />

            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              isActive={editor.isActive('bold')}
              title='Bold'
            >
              <Bold className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              isActive={editor.isActive('italic')}
              title='Italic'
            >
              <Italic className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              isActive={editor.isActive('underline')}
              title='Underline'
            >
              <UnderlineIcon className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().toggleStrike().run()}
              isActive={editor.isActive('strike')}
              title='Strikethrough'
            >
              <Strikethrough className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().toggleCode().run()}
              isActive={editor.isActive('code')}
              title='Inline Code'
            >
              <CodeIcon className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().toggleSubscript().run()}
              isActive={editor.isActive('subscript')}
              title='Subscript'
            >
              <SubscriptIcon className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().toggleSuperscript().run()}
              isActive={editor.isActive('superscript')}
              title='Superscript'
            >
              <SuperscriptIcon className='h-4 w-4' />
            </ToolbarButton>
          </div>

          {/* Second Row - Lists, Alignment, and Special Elements */}
          <div className='flex flex-wrap gap-1'>
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              isActive={editor.isActive('bulletList')}
              title='Bullet List'
            >
              <List className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              isActive={editor.isActive('orderedList')}
              title='Numbered List'
            >
              <ListOrdered className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              isActive={editor.isActive('blockquote')}
              title='Quote'
            >
              <Quote className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().setHorizontalRule().run()}
              title='Horizontal Rule'
            >
              <Minus className='h-4 w-4' />
            </ToolbarButton>

            <div className='w-px h-6 bg-border mx-1' />

            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
              isActive={editor.isActive({ textAlign: 'left' })}
              title='Align Left'
            >
              <AlignLeft className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() =>
                editor.chain().focus().setTextAlign('center').run()
              }
              isActive={editor.isActive({ textAlign: 'center' })}
              title='Align Center'
            >
              <AlignCenter className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
              isActive={editor.isActive({ textAlign: 'right' })}
              title='Align Right'
            >
              <AlignRight className='h-4 w-4' />
            </ToolbarButton>

            <div className='w-px h-6 bg-border mx-1' />

            <ToolbarButton
              onClick={() => {
                editor
                  .chain()
                  .focus()
                  .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
                  .run();
              }}
              title='Insert Table'
            >
              <TableIcon className='h-4 w-4' />
            </ToolbarButton>

            <div className='w-px h-6 bg-border mx-1' />

            <ToolbarButton
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              title='Undo'
            >
              <Undo className='h-4 w-4' />
            </ToolbarButton>

            <ToolbarButton
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              title='Redo'
            >
              <Redo className='h-4 w-4' />
            </ToolbarButton>
          </div>
        </div>
      )}

      <EditorContent
        editor={editor}
        className='prose prose-sm max-w-none p-4 min-h-[200px] focus-within:outline-none'
      />
    </div>
  );
}

export function TipTapEditorWithVariables({
  content,
  onChange,
  placeholder = 'Start typing...',
  className,
  editable = true,
  variables = [],
  showVariables = true,
}: TipTapEditorWithVariablesProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: false, // We'll use the separate Heading extension
      }),
      Heading.configure({
        levels: [1, 2, 3, 4, 5, 6],
      }),
      Placeholder.configure({
        placeholder,
      }),
      TextStyle,
      Color,
      Highlight,
      Underline,
      Subscript,
      Superscript,
      Code,
      Blockquote,
      HorizontalRule,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline hover:text-blue-800',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto',
        },
      }),
    ],
    content,
    editable,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  const insertVariable = (variable: Variable) => {
    if (editor) {
      editor
        .chain()
        .focus()
        .insertContent(variable.value + ' ')
        .run();
    }
  };

  if (!editor) {
    return null;
  }

  const ToolbarButton = ({
    onClick,
    isActive = false,
    disabled = false,
    children,
    title,
  }: {
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title: string;
  }) => (
    <Button
      type='button'
      variant={isActive ? 'default' : 'ghost'}
      size='sm'
      onClick={onClick}
      disabled={disabled}
      title={title}
      className='h-8 w-8 p-0'
    >
      {children}
    </Button>
  );

  return (
    <div className='flex gap-4'>
      <div className='flex-1'>
        <div className={cn('border rounded-lg', className)}>
          {editable && (
            <div className='border-b p-2 space-y-2'>
              {/* First Row - Text Formatting */}
              <div className='flex flex-wrap gap-1'>
                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleHeading({ level: 1 }).run()
                  }
                  isActive={editor.isActive('heading', { level: 1 })}
                  title='Heading 1'
                >
                  <Heading1 className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleHeading({ level: 2 }).run()
                  }
                  isActive={editor.isActive('heading', { level: 2 })}
                  title='Heading 2'
                >
                  <Heading2 className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleHeading({ level: 3 }).run()
                  }
                  isActive={editor.isActive('heading', { level: 3 })}
                  title='Heading 3'
                >
                  <Heading3 className='h-4 w-4' />
                </ToolbarButton>

                <div className='w-px h-6 bg-border mx-1' />

                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleBold().run()}
                  isActive={editor.isActive('bold')}
                  title='Bold'
                >
                  <Bold className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleItalic().run()}
                  isActive={editor.isActive('italic')}
                  title='Italic'
                >
                  <Italic className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleUnderline().run()}
                  isActive={editor.isActive('underline')}
                  title='Underline'
                >
                  <UnderlineIcon className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleStrike().run()}
                  isActive={editor.isActive('strike')}
                  title='Strikethrough'
                >
                  <Strikethrough className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleCode().run()}
                  isActive={editor.isActive('code')}
                  title='Inline Code'
                >
                  <CodeIcon className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleSubscript().run()}
                  isActive={editor.isActive('subscript')}
                  title='Subscript'
                >
                  <SubscriptIcon className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleSuperscript().run()
                  }
                  isActive={editor.isActive('superscript')}
                  title='Superscript'
                >
                  <SuperscriptIcon className='h-4 w-4' />
                </ToolbarButton>
              </div>

              {/* Second Row - Lists, Alignment, and Special Elements */}
              <div className='flex flex-wrap gap-1'>
                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleBulletList().run()
                  }
                  isActive={editor.isActive('bulletList')}
                  title='Bullet List'
                >
                  <List className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleOrderedList().run()
                  }
                  isActive={editor.isActive('orderedList')}
                  title='Numbered List'
                >
                  <ListOrdered className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleBlockquote().run()
                  }
                  isActive={editor.isActive('blockquote')}
                  title='Quote'
                >
                  <Quote className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().setHorizontalRule().run()
                  }
                  title='Horizontal Rule'
                >
                  <Minus className='h-4 w-4' />
                </ToolbarButton>

                <div className='w-px h-6 bg-border mx-1' />

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().setTextAlign('left').run()
                  }
                  isActive={editor.isActive({ textAlign: 'left' })}
                  title='Align Left'
                >
                  <AlignLeft className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().setTextAlign('center').run()
                  }
                  isActive={editor.isActive({ textAlign: 'center' })}
                  title='Align Center'
                >
                  <AlignCenter className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().setTextAlign('right').run()
                  }
                  isActive={editor.isActive({ textAlign: 'right' })}
                  title='Align Right'
                >
                  <AlignRight className='h-4 w-4' />
                </ToolbarButton>

                <div className='w-px h-6 bg-border mx-1' />

                <ToolbarButton
                  onClick={() => {
                    editor
                      .chain()
                      .focus()
                      .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
                      .run();
                  }}
                  title='Insert Table'
                >
                  <TableIcon className='h-4 w-4' />
                </ToolbarButton>

                <div className='w-px h-6 bg-border mx-1' />

                <ToolbarButton
                  onClick={() => editor.chain().focus().undo().run()}
                  disabled={!editor.can().undo()}
                  title='Undo'
                >
                  <Undo className='h-4 w-4' />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().redo().run()}
                  disabled={!editor.can().redo()}
                  title='Redo'
                >
                  <Redo className='h-4 w-4' />
                </ToolbarButton>
              </div>
            </div>
          )}

          <EditorContent
            editor={editor}
            className='prose prose-sm max-w-none p-4 min-h-[400px] focus-within:outline-none [&_.ProseMirror]:outline-none [&_.ProseMirror]:min-h-[350px] [&_.ProseMirror]:p-0 [&_.ProseMirror]:border-none [&_.ProseMirror]:focus:ring-0 [&_.ProseMirror]:focus:border-none [&_.ProseMirror]:focus:outline-none [&_table]:border-collapse [&_table]:border [&_table]:border-gray-300 [&_td]:border [&_td]:border-gray-300 [&_td]:p-2 [&_th]:border [&_th]:border-gray-300 [&_th]:p-2 [&_th]:bg-gray-50 [&_th]:font-semibold [&_blockquote]:border-l-4 [&_blockquote]:border-gray-300 [&_blockquote]:pl-4 [&_blockquote]:italic [&_hr]:border-gray-300 [&_hr]:my-4 [&_code]:bg-gray-100 [&_code]:px-1 [&_code]:py-0.5 [&_code]:rounded [&_code]:text-sm [&_code]:font-mono'
          />
        </div>
      </div>

      {showVariables && variables.length > 0 && (
        <div className='w-64 border rounded-lg p-4 bg-gray-50'>
          <h3 className='font-medium text-sm mb-3 text-[var(--custom-gray-dark)]'>
            Available Variables
          </h3>
          <div className='space-y-2 max-h-[400px] overflow-y-auto'>
            {variables.map(variable => (
              <Button
                key={variable.id}
                variant='outline'
                size='sm'
                onClick={() => insertVariable(variable)}
                className='w-full justify-start text-left h-auto py-2 px-3 bg-background hover:bg-blue-50'
              >
                <div className='w-full'>
                  <div className='font-medium text-xs text-[var(--custom-gray-dark)]'>
                    {variable.label}
                  </div>
                  <div className='text-xs text-[var(--custom-gray-medium)] font-mono'>
                    {variable.value}
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export { TipTapEditor as default };
