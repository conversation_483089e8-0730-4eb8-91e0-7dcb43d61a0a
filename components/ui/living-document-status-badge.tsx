import React, { FC } from 'react';
import { Badge } from '@/components/ui/badge';

export const StatusBadge: FC<{ status: string }> = ({ status }) => {
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Archived':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  return (
    <Badge className={getStatusBadgeColor(status || 'Draft')}>
      {status || 'Draft'}
    </Badge>
  );
};
