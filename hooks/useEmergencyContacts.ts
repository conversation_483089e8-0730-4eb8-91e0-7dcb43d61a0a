'use client';

import { useState, useEffect, useCallback } from 'react';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

// Generate the client
const client = generateClient<Schema>();

export interface EmergencyContact {
  id: string;
  fullName: string;
  relationship: string;
  phoneNumber: string;
  emailAddress: string;
  contactType: 'Medical' | 'Other';
  isPrimaryForType?: boolean;
}

export function useEmergencyContacts() {
  const [contacts, setContacts] = useState<EmergencyContact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchContacts = useCallback(async () => {
    try {
      setLoading(true);

      // Get the current authenticated user
      const user = await getCurrentUser();

      // Use GraphQL to fetch emergency contacts for the current user
      const { data, errors } = await client.models.EmergencyContact.list({
        filter: { userId: { eq: user.userId } },
      });

      if (errors) {
        console.error('GraphQL errors:', errors);
        throw new Error(errors.map(e => e.message).join(', '));
      }

      setContacts(
        data?.map(item => ({
          id: item.id!,
          fullName: item.fullName,
          relationship: item.relationship,
          phoneNumber: item.phoneNumber,
          emailAddress: item.emailAddress,
          contactType: item.contactType as 'Medical' | 'Other',
          isPrimaryForType: Boolean(item.isPrimaryForType),
        })) || []
      );

      setError(null);
    } catch (error) {
      console.error('Failed to fetch emergency contacts:', error);
      setError(
        error instanceof Error ? error.message : 'Failed to load contacts'
      );
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchContacts();
  }, [fetchContacts]);

  const addContact = async (contactData: Omit<EmergencyContact, 'id'>) => {
    try {
      const user = await getCurrentUser();

      // Check for duplicate email or phone
      const { data: existingContacts } =
        await client.models.EmergencyContact.list({
          filter: {
            userId: { eq: user.userId },
            or: [
              { emailAddress: { eq: contactData.emailAddress } },
              { phoneNumber: { eq: contactData.phoneNumber } },
            ],
          },
        });

      if (existingContacts && existingContacts.length > 0) {
        const duplicateEmail = existingContacts.find(
          c => c.emailAddress === contactData.emailAddress
        );
        const duplicatePhone = existingContacts.find(
          c => c.phoneNumber === contactData.phoneNumber
        );

        if (duplicateEmail) {
          throw new Error(
            `A contact with email ${contactData.emailAddress} already exists`
          );
        }
        if (duplicatePhone) {
          throw new Error(
            `A contact with phone number ${contactData.phoneNumber} already exists`
          );
        }
      }

      // If setting as primary for this type, first unset any existing primary contacts of this type
      if (contactData.isPrimaryForType) {
        const { data: existingPrimaryContacts } =
          await client.models.EmergencyContact.list({
            filter: {
              userId: { eq: user.userId },
              contactType: { eq: contactData.contactType },
              isPrimaryForType: { eq: true },
            },
          });

        if (existingPrimaryContacts && existingPrimaryContacts.length > 0) {
          for (const contact of existingPrimaryContacts) {
            await client.models.EmergencyContact.update({
              id: contact.id,
              isPrimaryForType: false,
            });
          }
        }
      }

      // Create the emergency contact
      const { data, errors } = await client.models.EmergencyContact.create({
        userId: user.userId,
        fullName: contactData.fullName,
        relationship: contactData.relationship,
        phoneNumber: contactData.phoneNumber,
        emailAddress: contactData.emailAddress,
        contactType: contactData.contactType,
        isPrimaryForType: contactData.isPrimaryForType || false,
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        // Send verification email
        // await sendVerificationEmail(
        //   data.id!,
        //   data.fullName,
        //   data.emailAddress,
        //   user.username || 'A Childfree Legacy member'
        // );

        setContacts([
          ...contacts,
          {
            id: data.id!,
            fullName: data.fullName,
            relationship: data.relationship,
            phoneNumber: data.phoneNumber,
            emailAddress: data.emailAddress,
            contactType: data.contactType as 'Medical' | 'Other',
            isPrimaryForType: Boolean(data.isPrimaryForType),
          },
        ]);
      }

      return data;
    } catch (error) {
      console.error('Error adding contact:', error);
      throw error;
    }
  };

  const updateContact = async (
    id: string,
    contactData: Omit<EmergencyContact, 'id'>
  ) => {
    try {
      const user = await getCurrentUser();

      // Check for duplicate email or phone
      const { data: existingContacts } =
        await client.models.EmergencyContact.list({
          filter: {
            userId: { eq: user.userId },
            id: { ne: id }, // Exclude the current contact
            or: [
              { emailAddress: { eq: contactData.emailAddress } },
              { phoneNumber: { eq: contactData.phoneNumber } },
            ],
          },
        });

      if (existingContacts && existingContacts.length > 0) {
        const duplicateEmail = existingContacts.find(
          c => c.emailAddress === contactData.emailAddress
        );
        const duplicatePhone = existingContacts.find(
          c => c.phoneNumber === contactData.phoneNumber
        );

        if (duplicateEmail) {
          throw new Error(
            `A contact with email ${contactData.emailAddress} already exists`
          );
        }
        if (duplicatePhone) {
          throw new Error(
            `A contact with phone number ${contactData.phoneNumber} already exists`
          );
        }
      }

      // If setting as primary for this type, unset any existing primary contacts of this type
      if (contactData.isPrimaryForType) {
        const { data: existingPrimaryContacts } =
          await client.models.EmergencyContact.list({
            filter: {
              userId: { eq: user.userId },
              contactType: { eq: contactData.contactType },
              isPrimaryForType: { eq: true },
              id: { ne: id }, // Exclude the current contact
            },
          });

        if (existingPrimaryContacts && existingPrimaryContacts.length > 0) {
          for (const contact of existingPrimaryContacts) {
            await client.models.EmergencyContact.update({
              id: contact.id,
              isPrimaryForType: false,
            });
          }
        }
      }

      // Update the emergency contact
      const { data, errors } = await client.models.EmergencyContact.update({
        id,
        fullName: contactData.fullName,
        relationship: contactData.relationship,
        phoneNumber: contactData.phoneNumber,
        emailAddress: contactData.emailAddress,
        contactType: contactData.contactType,
        isPrimaryForType: contactData.isPrimaryForType,
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        setContacts(
          contacts.map(contact =>
            contact.id === id
              ? {
                  ...contact,
                  fullName: data.fullName,
                  relationship: data.relationship,
                  phoneNumber: data.phoneNumber,
                  emailAddress: data.emailAddress,
                  contactType: data.contactType as 'Medical' | 'Other',
                  isPrimaryForType: Boolean(data.isPrimaryForType),
                }
              : contact
          )
        );
      }

      return data;
    } catch (error) {
      console.error('Error updating contact:', error);
      throw error;
    }
  };

  const deleteContact = async (id: string) => {
    try {
      // Delete the emergency contact
      const { data, errors } = await client.models.EmergencyContact.delete({
        id,
      });

      if (errors) {
        throw new Error(errors.map(e => e.message).join(', '));
      }

      if (data) {
        setContacts(contacts.filter(contact => contact.id !== id));
      }

      return data;
    } catch (error) {
      console.error('Error deleting contact:', error);
      throw error;
    }
  };

  return {
    contacts,
    loading,
    error,
    addContact,
    updateContact,
    deleteContact,
    refreshContacts: fetchContacts,
  };
}

// Helper function to generate a verification token
function generateVerificationToken() {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}
