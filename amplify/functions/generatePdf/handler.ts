// @ts-nocheck

import type { Hand<PERSON> } from 'aws-lambda';
// import chromium from 'chrome-aws-lambda';
// import puppeteer from 'puppeteer-core';

interface PDFGenerationRequest {
  html: string;
  options?: {
    filename?: string;
    format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
    orientation?: 'portrait' | 'landscape';
    margin?: {
      top?: string;
      right?: string;
      bottom?: string;
      left?: string;
    };
    printBackground?: boolean;
    scale?: number;
    displayHeaderFooter?: boolean;
    headerTemplate?: string;
    footerTemplate?: string;
  };
  css?: string;
}

export const handler: Handler = async (event, context) => {
  // try {
  //   console.log(
  //     'PDF Handler - Event received:',
  //     JSON.stringify(event, null, 2)
  //   );
  //
  //   // Handle both direct arguments (from Amplify GraphQL) and HTTP body format
  //   let body: PDFGenerationRequest;
  //
  //   if (event.arguments) {
  //     // Called via Amplify GraphQL API - arguments are passed directly
  //     console.log('Using event.arguments');
  //     body = event.arguments as PDFGenerationRequest;
  //   } else if (event.body) {
  //     // Called via HTTP API - parse body
  //     console.log('Using event.body');
  //     body =
  //       typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
  //   } else {
  //     // Fallback - treat event as the body itself
  //     console.log('Using event as body');
  //     body = event as PDFGenerationRequest;
  //   }
  //
  //   let { html, options = {}, css } = body;
  //
  //   console.log('PDF Handler - Parsed body:', {
  //     html: html?.substring(0, 100) + '...',
  //     options,
  //     css,
  //   });
  //
  //   // Parse options if it's a JSON string (from GraphQL)
  //   if (typeof options === 'string') {
  //     try {
  //       options = JSON.parse(options);
  //       console.log('PDF Handler - Parsed options:', options);
  //     } catch (e) {
  //       console.warn('Failed to parse options JSON, using empty object:', e);
  //       options = {};
  //     }
  //   }
  //
  //   // Validate required fields
  //   if (!html || typeof html !== 'string') {
  //     return {
  //       statusCode: 400,
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({
  //         error: 'HTML content is required and must be a string',
  //       }),
  //     };
  //   }
  //
  //   if (html.trim().length === 0) {
  //     return {
  //       statusCode: 400,
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({
  //         error: 'HTML content cannot be empty',
  //       }),
  //     };
  //   }
  //
  //   // Launch browser with chrome-aws-lambda
  //   const browser = await puppeteer.launch({
  //     args: chromium.args,
  //     defaultViewport: chromium.defaultViewport,
  //     executablePath: await chromium.executablePath,
  //     headless: chromium.headless,
  //     ignoreHTTPSErrors: true,
  //   });
  //
  //   const page = await browser.newPage();
  //
  //   // Set viewport for consistent rendering
  //   await page.setViewport({
  //     width: 1200,
  //     height: 800,
  //     deviceScaleFactor: 1,
  //   });
  //
  //   // Inject custom CSS if provided
  //   let finalHtml = html;
  //   if (css) {
  //     finalHtml = `
  //       <html>
  //         <head>
  //           <style>${css}</style>
  //         </head>
  //         <body>
  //           ${html}
  //         </body>
  //       </html>
  //     `;
  //   }
  //
  //   // Set content and wait for it to load
  //   await page.setContent(finalHtml, {
  //     waitUntil: 'networkidle0',
  //     timeout: 30000,
  //   });
  //
  //   // Configure PDF options
  //   const pdfOptions = {
  //     format: (options.format || 'A4').toLowerCase() as any,
  //     landscape: options.orientation === 'landscape',
  //     printBackground: options.printBackground !== false, // Default to true
  //     margin: {
  //       top: options.margin?.top || '1cm',
  //       right: options.margin?.right || '1cm',
  //       bottom: options.margin?.bottom || '1cm',
  //       left: options.margin?.left || '1cm',
  //     },
  //     scale: options.scale || 1,
  //     displayHeaderFooter: options.displayHeaderFooter || false,
  //     headerTemplate: options.headerTemplate || '',
  //     footerTemplate: options.footerTemplate || '',
  //   };
  //
  //   // Generate PDF
  //   const pdfBuffer = await page.pdf(pdfOptions);
  //
  //   await browser.close();
  //
  //   // Prepare response
  //   const filename = options.filename || 'document.pdf';
  //   const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
  //
  //   return {
  //     statusCode: 200,
  //     headers: {
  //       'Content-Type': 'application/pdf',
  //       'Content-Disposition': `attachment; filename="${sanitizedFilename}"`,
  //       'Content-Length': pdfBuffer.length.toString(),
  //       'Cache-Control': 'no-cache, no-store, must-revalidate',
  //       Pragma: 'no-cache',
  //       Expires: '0',
  //     },
  //     body: pdfBuffer.toString('base64'),
  //     isBase64Encoded: true,
  //   };
  // } catch (error) {
  //   console.error('PDF generation error:', error);
  //
  //   // Return appropriate error response
  //   let errorMessage =
  //     'Failed to generate PDF. Please check your HTML content and try again.';
  //   let statusCode = 500;
  //
  //   if (error instanceof Error) {
  //     if (error.message.includes('timeout')) {
  //       errorMessage =
  //         'PDF generation timed out. Please try with simpler content.';
  //       statusCode = 408;
  //     } else if (error.message.includes('memory')) {
  //       errorMessage =
  //         'Insufficient memory to generate PDF. Please try with smaller content.';
  //       statusCode = 507;
  //     }
  //   }
  //
  //   return {
  //     statusCode,
  //     headers: {
  //       'Content-Type': 'application/json',
  //     },
  //     body: JSON.stringify({
  //       error: errorMessage,
  //     }),
  //   };
  // }
};
