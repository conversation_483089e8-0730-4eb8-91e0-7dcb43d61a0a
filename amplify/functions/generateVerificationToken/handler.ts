// @ts-nocheck

import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/generateVerificationToken';
import crypto from 'crypto';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

export const handler = async event => {
  console.log(
    'Generate verification token request:',
    JSON.stringify(event, null, 2)
  );

  try {
    // Extract data from event - handle both direct Lambda calls and GraphQL/AppSync calls
    const { email, verificationType } = event.arguments || event;

    // Validate required fields
    if (!email) {
      throw new Error('Missing required field: email');
    }

    if (!verificationType) {
      throw new Error('Missing required field: verificationType');
    }

    // Validate verificationType
    if (!['accountConfirmation', 'passwordReset'].includes(verificationType)) {
      throw new Error(
        'Invalid verificationType. Must be "accountConfirmation" or "passwordReset"'
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }

    // Generate a secure random token
    const token = crypto.randomBytes(32).toString('hex');

    // Set expiration time (24 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);

    // Delete any existing verification tokens for this email and verification type
    try {
      console.log(
        'Attempting to delete existing tokens for email:',
        email,
        'and type:',
        verificationType
      );
      const existingTokens = await client.models.VerificationTokens.list({
        filter: {
          email: { eq: email },
          verificationType: { eq: verificationType },
        },
      });

      console.log('Found existing tokens:', existingTokens.data?.length || 0);

      if (existingTokens.data && existingTokens.data.length > 0) {
        for (const existingToken of existingTokens.data) {
          await client.models.VerificationTokens.delete({
            id: existingToken.id,
          });
          console.log('Deleted existing token:', existingToken.id);
        }
      }
    } catch (error) {
      console.log(
        'Error deleting existing tokens for email:',
        email,
        'Error:',
        error
      );
    }

    // Create new verification token
    console.log(
      'Creating new verification token for email:',
      email,
      'with token:',
      token,
      'and type:',
      verificationType
    );
    const verificationToken = await client.models.VerificationTokens.create({
      email,
      token,
      expiresAt: expiresAt.toISOString(),
      isUsed: false,
      createdAt: new Date().toISOString(),
      verificationType,
    });

    console.log('Verification token created successfully:', verificationToken);
    console.log('Token data saved:', {
      email,
      token,
      expiresAt: expiresAt.toISOString(),
      isUsed: false,
      createdAt: new Date().toISOString(),
    });

    // Immediately verify the token was saved by reading it back
    try {
      console.log('Verifying token was saved...');
      const verifyResult = await client.models.VerificationTokens.list({
        filter: { email: { eq: email } },
      });
      console.log(
        'Verification check - tokens found:',
        verifyResult.data?.length || 0
      );

      if (verifyResult.data && verifyResult.data.length > 0) {
        const savedToken = verifyResult.data.find(t => t.token === token);
        if (savedToken) {
          console.log('✅ Token successfully verified in database:', {
            id: savedToken.id,
            email: savedToken.email,
            tokenMatch: savedToken.token === token,
            isUsed: savedToken.isUsed,
          });
        } else {
          console.log('❌ Token not found in database after creation');
          console.log(
            'Available tokens:',
            verifyResult.data.map(t => ({
              id: t.id,
              token: t.token.substring(0, 8) + '...',
              isUsed: t.isUsed,
            }))
          );
        }
      } else {
        console.log('❌ No tokens found for email after creation');
      }
    } catch (verifyError) {
      console.log('Error verifying token was saved:', verifyError);
    }

    // Generate verification link
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const verificationLink = `${baseUrl}/auth/verify-email?token=${token}&email=${encodeURIComponent(email)}`;

    return {
      success: true,
      token,
      verificationLink,
      expiresAt: expiresAt.toISOString(),
      message: 'Verification token generated successfully',
    };
  } catch (error) {
    console.error('Error generating verification token:', error);

    return {
      success: false,
      error: error.message || 'Failed to generate verification token',
    };
  }
};
