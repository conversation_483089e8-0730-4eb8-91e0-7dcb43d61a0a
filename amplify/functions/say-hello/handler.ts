import type { Handler } from 'aws-lambda';

export const handler: Handler = async (event, context) => {
  // your function code goes here
  return 'Hello, World!';
};

// import chromium from 'chrome-aws-lambda';
// import puppeteer from 'puppeteer-core';

// export const handler: Handler = async (event, context) => {
//   try {
//     const body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
//     const html = body?.html;
//
//     if (!html) {
//       return {
//         statusCode: 400,
//         body: JSON.stringify({ error: 'CANT FIND HTML' }),
//       };
//     }
//
//     const browser = await puppeteer.launch({
//       args: chromium.args,
//       defaultViewport: chromium.defaultViewport,
//       executablePath: await chromium.executablePath,
//       headless: chromium.headless,
//     });
//
//     const page = await browser.newPage();
//     await page.setContent(html, { waitUntil: 'networkidle0' });
//
//     const pdfBuffer = await page.pdf({
//       format: 'a4',
//       printBackground: true,
//     });
//
//     await browser.close();
//
//     return {
//       statusCode: 200,
//       headers: {
//         'Content-Type': 'application/pdf',
//         'Content-Disposition': 'attachment; filename="document.pdf"',
//       },
//       body: pdfBuffer.toString('base64'),
//       isBase64Encoded: true,
//     };
//   } catch (error) {
//     console.error('PDF generation error:', error);
//     return {
//       statusCode: 500,
//       body: JSON.stringify({ error: 'PDF ERROR GENERATION' }),
//     };
//   }
// };
