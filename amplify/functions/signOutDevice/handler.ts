import type { AppSyncResolverEvent } from 'aws-lambda';
import {
  CognitoIdentityProviderClient,
  AdminGetDeviceCommand,
  AdminForgetDeviceCommand,
  AdminUserGlobalSignOutCommand,
  ListDevicesCommand,
} from '@aws-sdk/client-cognito-identity-provider';

const client = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

const USER_POOL_ID = process.env.AMPLIFY_AUTH_USERPOOL_ID;

export const handler = async (
  event: AppSyncResolverEvent<{ deviceId: string; username: string }>
) => {
  try {
    // Extract arguments from AppSync event
    const { deviceId, username } = event.arguments;

    if (!deviceId || !username) {
      throw new Error('deviceId and username are required');
    }

    if (!USER_POOL_ID) {
      throw new Error('User pool ID not configured');
    }

    // First, try to get device info to verify it exists
    try {
      await client.send(
        new AdminGetDeviceCommand({
          UserPoolId: USER_POOL_ID,
          Username: username,
          DeviceKey: deviceId,
        })
      );
    } catch (error: any) {
      if (error.name === 'ResourceNotFoundException') {
        throw new Error('Device not found');
      }
      throw error;
    }

    // Forget the device (this invalidates the device's tokens)
    await client.send(
      new AdminForgetDeviceCommand({
        UserPoolId: USER_POOL_ID,
        Username: username,
        DeviceKey: deviceId,
      })
    );

    return {
      success: true,
      message: 'Device signed out successfully',
    };
  } catch (error: any) {
    throw new Error(error.message || 'Failed to sign out device');
  }
};
