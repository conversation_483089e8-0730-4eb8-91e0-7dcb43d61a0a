// @ts-nocheck

import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import {
  CognitoIdentityProviderClient,
  AdminGetUserCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { env } from '$amplify/env/checkEmail';

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

export const handler: Schema['checkEmail']['functionHandler'] = async event => {
  const { email } = event.arguments;

  console.log('===> Checking email existence:', { email });

  try {
    // Validate email format
    if (!email) {
      return {
        exists: false,
        message: 'Email is required',
        error: 'Email is required',
      };
    }

    // Simple email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        exists: false,
        message: 'Invalid email format',
        error: 'Invalid email format',
      };
    }

    try {
      // Try to get user from Cognito User Pool
      await cognitoClient.send(
        new AdminGetUserCommand({
          UserPoolId: env.USER_POOL_ID,
          Username: email,
        })
      );

      // If we get here, user exists
      console.log('===> Email exists in Cognito:', email);
      return {
        exists: true,
        message: 'Email already exists',
      };
    } catch (error: any) {
      // If user doesn't exist, Cognito throws UserNotFoundException
      if (error.name === 'UserNotFoundException') {
        console.log('===> Email is available:', email);
        return {
          exists: false,
          message: 'Email is available',
        };
      }

      // Handle other Cognito errors
      console.error('===> Cognito error:', error);
      return {
        exists: false,
        message: 'Failed to check email availability',
        error: 'Failed to check email availability',
      };
    }
  } catch (error) {
    console.error('===> Error checking email:', error);
    return {
      exists: false,
      message: 'Internal server error',
      error: 'Internal server error',
    };
  }
};
