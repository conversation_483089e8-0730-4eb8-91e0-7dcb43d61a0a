import { a } from '@aws-amplify/backend';

/**
 * Dead Man's Switch related models
 */

// Check-in frequency enum
export const CheckInFrequency = a.enum([
  'WEEKLY',
  'BIWEEKLY',
  'MONTHLY',
  'CUSTOM',
]);

// Communication method enum
export const CommunicationMethod = a.enum([
  'EMAIL',
  'SMS',
  'BOTH',
]);

// Escalation protocol enum
export const EscalationProtocol = a.enum([
  'SENSITIVE',
  'RELAXED',
  'CUSTOM',
  'STANDARD',
]);


export const DMSStatus = a.enum(['ACTIVE', 'PAUSED', 'DISABLED']);


// Dead Man's Switch configuration model
export const DeadMansSwitch = a
  .model({
    id: a.id(),
    userId: a.string().required(),
    isEnable: a.boolean().required().default(false),
    frequency: a.enum(CheckInFrequency.values),
    customFrequencyDays: a.integer(),
    communicationMethod: a.enum(CommunicationMethod.values),
    escalationProtocol: a.enum(EscalationProtocol.values),
    customEscalationSteps: a.integer(),
    personalMessage: a.string(),
    status: a.string(),
    pauseReason: a.string(),
    pauseUntil: a.datetime(),
    nextCheckIn: a.datetime(),
    lastCheckIn: a.datetime(),
  })
  .authorization(allow => [allow.owner()]);

// Check-in history model
export const CheckInHistory = a
  .model({
    id: a.id(),
    userId: a.string().required(),
    deadMansSwitchId: a.string().required(),
    checkInTime: a.datetime().required(),
    method: a.string().required(), // 'Manual', 'Automatic', 'Admin'
    ipAddress: a.string(),
    userAgent: a.string(),
  })
  .authorization(allow => [allow.owner()]);

// Escalation event model
export const EscalationEvent = a
  .model({
    id: a.id(),
    userId: a.string().required(),
    deadMansSwitchId: a.string().required(),
    escalationLevel: a.integer().required(),
    eventTime: a.datetime().required(),
    notificationType: a.string().required(), // 'Reminder', 'Alert', 'Emergency'
    notificationMethod: a.string().required(), // 'Email', 'SMS', 'Both'
    recipientIds: a.string().array(),
    status: a.enum(['PENDING', 'SENT', 'FAILED', 'ACKNOWLEDGED']),
    acknowledgedAt: a.datetime(),
    acknowledgedBy: a.string(),
  })
  .authorization(allow => [allow.owner()]);

// DMS test event model
export const DMSTestEvent = a
  .model({
    id: a.id(),
    userId: a.string().required(),
    deadMansSwitchId: a.string().required(),
    testTime: a.datetime().required(),
    testType: a.string().required(), // 'Full', 'Notification', 'Escalation'
    recipientIds: a.string().array(),
    status: a.enum(['PENDING', 'SENT', 'FAILED', 'ACKNOWLEDGED']),
    acknowledgedAt: a.datetime(),
    acknowledgedBy: a.string(),
  })
  .authorization(allow => [allow.owner()]);