import { a } from '@aws-amplify/backend';

/**
 * Template management related models
 */

export const Template = a
  .model({
    id: a.string().required(),
    createdBy: a.string().required(),
    createdByEmail: a.string(),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    type: a.string().required(),
    templateState: a.string(),
    templateStates: a.string().array(),
    isGeneral: a.boolean().default(false),
    templateName: a.string().required(),
    isActive: a.boolean().default(true),
    isDraft: a.boolean().default(true),
    isAdditional: a.boolean().default(false),
    startPageTemplateId: a.string(),
    endPageTemplateId: a.string(),
  })
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ]);

export const TemplateVersion = a
  .model({
    templateId: a.string().required(),
    versionNumber: a.integer().required(),
    content: a.string().required(),
    createdAt: a.datetime().required(),
  })
  .identifier(['templateId', 'versionNumber'])
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ]);
