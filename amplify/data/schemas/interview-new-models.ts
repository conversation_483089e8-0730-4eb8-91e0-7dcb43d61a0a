import { a } from '@aws-amplify/backend';

// VALIDATION ENUM
export const QuestionValidation = a.enum(['number', 'email', 'phone']);

// === Custom Types ===
export const ConditionalLogic = a.customType({
  conditionType: a.enum(['equals', 'notEquals', 'contains']),
  expectedValue: a.string().required(),
  showQuestionId: a.id().required(),
});

export const Question = a.customType({
  questionId: a.id().required(),
  text: a.string().required(),
  type: a.enum(['text', 'radio', 'select', 'checkbox']),
  options: a.string().array(),
  order: a.integer().required(),
  conditionalLogic: a.ref('ConditionalLogic').array(),
  defaultNextQuestionId: a.string(),
  isHeadQuestion: a.boolean(),
  questionMapping: a.string(),
  questionValidation: QuestionValidation,
  educationalVideoId: a.string(),
});

// === Models ===
export const Interview = a
  .model({
    name: a.string().required(),
    description: a.string(),
    isActive: a.boolean().default(true),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
    versions: a.hasMany('InterviewVersion', 'interviewId'),
  })
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ]);

export const InterviewVersion = a
  .model({
    interviewId: a.id().required(),
    versionNumber: a.integer().required(),
    isActive: a.boolean().default(true),
    createdAt: a.datetime(),
    questions: a.ref('Question').array(),
    // Relationship
    interview: a.belongsTo('Interview', 'interviewId'),
  })
  .authorization(allow => [
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ]);
