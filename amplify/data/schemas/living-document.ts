import { a } from '@aws-amplify/backend';

const LivingDocumentsStatus = a.enum(['Draft', 'Active', 'Archived']);

// Question types for living document templates
const LivingDocumentQuestionType = a.enum(['text', 'select', 'radio', 'file']);

// Custom type for question options
export const LivingDocumentQuestionOption = a.customType({
  id: a.string().required(),
  label: a.string().required(),
  value: a.string().required(),
});

// Custom type for living document questions
export const LivingDocumentQuestion = a.customType({
  id: a.string().required(),
  text: a.string().required(),
  type: LivingDocumentQuestionType,
  required: a.boolean(),
  placeholder: a.string(),
  helpText: a.string(),
  options: a.ref('LivingDocumentQuestionOption').array(),
  allowMultiple: a.boolean(),
  order: a.integer().required(),
});

export const LivingDocumentTemplate = a
  .model({
    createdByEmail: a.string().required(),
    documentType: a.string().required(),
    title: a.string().required(),
    description: a.string(),
    content: a.string(),
    questions: a.ref('LivingDocumentQuestion').array(), // Structured questions
    version: a.integer().default(1),
    status: LivingDocumentsStatus,
    icon: a.string(),
    createdAt: a.datetime(),
    updatedAt: a.datetime(),
  })
  .authorization(allow => [
    allow.group('ADMINS'),
    allow.authenticated().to(['read']),
  ]);
