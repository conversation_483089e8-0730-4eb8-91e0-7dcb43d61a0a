import { a } from '@aws-amplify/backend';

/**
 * User management related models
 */

export const UserAnswer = a.customType({
  questionId: a.string().required(),
  answer: a.string(),
  answeredAt: a.datetime().required(),
});

export const UserInterviewProgressNew = a.customType({
  interviewVersionId: a.id().required(),
  isCompleted: a.boolean().required(),
  startedAt: a.datetime().required(),
  completedAt: a.datetime(),
  currentQuestionId: a.string(),
  answers: a.ref('UserAnswer').array(),
});

// User status enum
export const UserStatus = a.enum(['active', 'inactive', 'pending', 'deceased']);

// Master role enum
export const MasterRole = a.enum([
  'Member',
  'Administrator',
  'WelonTrust',
  'Professional',
]);

// Invite status enum
export const InviteStatus = a.enum(['pending', 'accepted', 'expired', 'cancelled']);

// User Invite model
export const UserInvite = a
  .model({
    email: a.string().required(),
    role: MasterRole,
    invitedBy: a.string().required(), // Admin user ID who sent the invite
    invitedByEmail: a.string().required(),
    token: a.string().required(),
    status: InviteStatus,
    expiresAt: a.datetime().required(),
    acceptedAt: a.datetime(),
    createdAt: a.datetime().required(),
  })
  .authorization(allow => [
    allow.group('ADMINS'),
    allow.guest().to(['read']), // Allow guest to read for invite validation
  ]);

// Link type enum
export const LinkType = a.enum([
  'primary',
  'secondary',
  'delegate',
  'emergency',
]);

// Link status enum
export const LinkStatus = a.enum(['pending', 'active', 'revoked']);

// WelonTrustAssignment status enum
export const WelonTrustAssignmentStatus = a.enum([
  'active',
  'pending',
  'revoked',
]);

// User Address model for shipping
export const UserAddress = a
  .model({
    userId: a.id().required(),
    addressLine1: a.string().required(),
    addressLine2: a.string(),
    city: a.string().required(),
    stateProvinceCode: a.string().required(),
    postalCode: a.string().required(),
    countryCode: a.string().required().default('US'),
    isDefault: a.boolean().default(false),
    createdAt: a.datetime().required(),
    updatedAt: a.datetime().required(),
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [allow.authenticated(), allow.group('ADMINS')]);

// Shipping Label model for tracking UPS labels
export const ShippingLabel = a
  .model({
    userId: a.id().required(),
    assignedWelonTrustId: a.id(), // ID of the WelonTrust user receiving the package
    documentId: a.string(),
    trackingNumber: a.string().required(),
    labelUrl: a.string().required(),
    fromAddress: a.string().required(), // JSON string of address
    toAddress: a.string().required(), // JSON string of address
    serviceCode: a.string().required(),
    cost: a.string().required(), // JSON string with amount and currency
    estimatedDelivery: a.string(),
    status: a.string().default('created'),
    createdAt: a.datetime().required(),
    // Relationships
    user: a.belongsTo('User', 'userId'),
    assignedWelonTrust: a.belongsTo('User', 'assignedWelonTrustId'),
  })
  .authorization(allow => [
    allow.authenticated(),
    allow.ownerDefinedIn('assignedWelonTrustId'), // Allow WelonTrust to see packages sent to them
    allow.group('ADMINS'),
  ]);

// Main User model
export const User = a
  .model({
    email: a.string().required(),
    firstName: a.string().required(),
    lastName: a.string().required(),
    phoneNumber: a.string().required(),
    birthdate: a.string().required(),
    state: a.string().required(),
    cognitoId: a.string().required(),
    role: MasterRole,
    subrole: a.string(),
    status: UserStatus,
    journeyStatus: a.string(),
    createdAt: a.datetime().required(),
    // Relationships
    linkedAccounts: a.hasMany('LinkedAccount', 'userId'),
    assignedWelonTrust: a.hasOne('WelonTrustAssignment', 'userId'),
    assignedWelonTrustId: a.string(),
    documents: a.hasMany('Document', 'userId'),
    subscriptions: a.hasMany('UserSubscription', 'userId'),
    interviewProgress: a.ref('UserInterviewProgressNew').array(),
    addresses: a.hasMany('UserAddress', 'userId'),
    shippingLabels: a.hasMany('ShippingLabel', 'userId'),
    assignedShippingLabels: a.hasMany('ShippingLabel', 'assignedWelonTrustId'),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('cognitoId'),
    allow.ownerDefinedIn('assignedWelonTrustId'),
    allow.authenticated().to(['read']),
    allow.group('ADMINS'),
  ]);

// Welon Trust assignment model
export const WelonTrustAssignment = a
  .model({
    userId: a.id().required(),
    welonTrustUserId: a.string().required(),
    welonTrustName: a.string().required(),
    welonTrustEmail: a.string().required(),
    welonTrustCognitoId: a.string().required().default(''),
    assignedAt: a.datetime().required(),
    assignedBy: a.string().required(), // Admin user ID who made the assignment
    status: WelonTrustAssignmentStatus,
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [
    allow.ownerDefinedIn('welonTrustCognitoId'),
    allow.group('ADMINS'),
  ]);

// Linked account model
export const LinkedAccount = a
  .model({
    userId: a.id().required(), // The ID of the user who owns this link
    linkedUserId: a.string().required(), // The ID of the user who is linked
    linkType: LinkType, // The type of link
    status: LinkStatus, // The status of the link
    permissions: a.string().array().required(), // Permissions granted to the linked user
    createdAt: a.datetime().required(), // When the link was created
    updatedAt: a.datetime().required(), // When the link was last updated
    expiresAt: a.datetime(), // Optional expiration date
    // Relationship
    user: a.belongsTo('User', 'userId'),
  })
  .authorization(allow => [allow.owner()]);
