'use client';

import React, { useState, useEffect } from 'react';
import { LivingDocumentList } from '@/components/living-documents/living-document-list';
import { LivingDocument } from '@/components/living-documents/types';

// Mock data for demonstration
const mockLivingDocuments: LivingDocument[] = [
  {
    id: '1',
    title: 'Emergency Contacts',
    type: 'emergency-contacts',
    description: 'List of emergency contacts and their information',
    content:
      'Emergency Contact 1:\nName: <PERSON>\nPhone: (*************\nRelationship: Friend\n\nEmergency Contact 2:\nName: <PERSON>\nPhone: (*************\nRelationship: Neighbor',
    createdAt: 'May 10, 2025',
    lastUpdated: 'June 15, 2025',
    nextReview: 'December 15, 2025',
    reviewFrequency: 'Every 6 months',
    status: 'current',
  },
  {
    id: '2',
    title: 'Pet Care Instructions',
    type: 'pet-care',
    description: 'Instructions for taking care of my pets',
    content:
      'Pet: <PERSON> (Dog)\nBreed: Golden Retriever\nAge: 5 years\nFeeding: 2 cups of dry food twice daily\nMedication: Heartworm pill on the 1st of each month\nVeterinarian: <PERSON><PERSON> <PERSON> at City Pet Clinic (*************\n\nPet: Whiskers (Cat)\nBreed: Siamese\nAge: 3 years\nFeeding: 1/2 cup of wet food twice daily\nLitter box: Clean daily\nVeterinarian: Dr. Jones at City Pet Clinic (*************',
    createdAt: 'April 5, 2025',
    lastUpdated: 'May 20, 2025',
    nextReview: 'August 20, 2025',
    reviewFrequency: 'Every 3 months',
    status: 'review-soon',
  },
  {
    id: '3',
    title: 'Digital Asset Management',
    type: 'digital-assets',
    description: 'Inventory and access information for digital assets',
    content:
      "Email Accounts:\n1. <EMAIL> - Password stored in password manager\n2. <EMAIL> - Password stored in password manager\n\nSocial Media:\n1. Facebook - Username: myusername\n2. Twitter - Username: @myhandle\n\nFinancial Accounts:\n1. Online Banking - Access through password manager\n2. Investment Account - Access through password manager\n\nPassword Manager:\nLastPass - Master password hint: First pet's name + year of birth",
    createdAt: 'March 15, 2025',
    lastUpdated: 'March 15, 2025',
    nextReview: 'June 15, 2025',
    reviewFrequency: 'Every 3 months',
    status: 'review-needed',
  },
  {
    id: '4',
    title: 'Medical Information',
    type: 'medical-info',
    description: 'Important medical information and preferences',
    content:
      'Blood Type: O+\nAllergies: Penicillin, Shellfish\nCurrent Medications: Lisinopril 10mg daily\nMedical Conditions: Hypertension\nPreferred Hospital: City General Hospital\nPrimary Care Doctor: Dr. Sarah Johnson (*************',
    createdAt: 'February 20, 2025',
    lastUpdated: 'April 10, 2025',
    nextReview: 'July 10, 2025',
    reviewFrequency: 'Every 3 months',
    status: 'current',
  },
  {
    id: '5',
    title: 'End-of-Life Wishes',
    type: 'end-of-life',
    description: 'Personal preferences for end-of-life care and arrangements',
    content:
      'Funeral Preferences: Cremation preferred\nMemorial Service: Small gathering with close friends and family\nDonation Preferences: Donate to local animal shelter\nSpecial Requests: Play my favorite music playlist\nBurial/Cremation: Scatter ashes in the ocean',
    createdAt: 'January 15, 2025',
    lastUpdated: 'January 15, 2025',
    nextReview: 'July 15, 2025',
    reviewFrequency: 'Every 6 months',
    status: 'current',
  },
  {
    id: '6',
    title: 'Home Security Information',
    type: 'custom',
    description: 'Security codes and important home information',
    content:
      'Alarm Code: 1234\nSafe Combination: 15-25-35\nSpare Key Location: Under the flower pot\nSecurity Company: SecureHome (555) 999-8888\nNeighbor Contact: Mrs. Wilson (555) 111-2222',
    createdAt: 'December 10, 2024',
    lastUpdated: 'February 5, 2025',
    nextReview: 'May 5, 2025',
    reviewFrequency: 'Every 3 months',
    status: 'review-soon',
  },
  {
    id: '7',
    title: 'Financial Account Information',
    type: 'custom',
    description: 'Important financial account details and contacts',
    content:
      'Bank: First National Bank\nAccount Manager: John Smith (555) 777-8888\nInvestment Advisor: Jane Doe (555) 666-7777\nInsurance Agent: Bob Johnson (555) 555-6666\nAccountant: Mary Wilson (555) 444-5555',
    createdAt: 'November 20, 2024',
    lastUpdated: 'January 30, 2025',
    nextReview: 'April 30, 2025',
    reviewFrequency: 'Every 3 months',
    status: 'review-needed',
  },
  {
    id: '8',
    title: 'Vehicle Information',
    type: 'custom',
    description: 'Vehicle details and maintenance information',
    content:
      "Vehicle 1: 2020 Honda Civic\nVIN: 1HGBH41JXMN109186\nInsurance: State Farm (555) 333-4444\nMechanic: Joe's Auto Shop (555) 222-3333\nLicense Plate: ABC123\n\nVehicle 2: 2018 Toyota Camry\nVIN: 4T1BF1FK5GU260429\nInsurance: State Farm (555) 333-4444",
    createdAt: 'October 15, 2024',
    lastUpdated: 'December 20, 2024',
    nextReview: 'March 20, 2025',
    reviewFrequency: 'Every 3 months',
    status: 'review-needed',
  },
];

export default function LivingDocumentsPage() {
  const [documents, setDocuments] = useState<LivingDocument[]>([]);

  useEffect(() => {
    // In a real implementation, this would fetch data from an API
    // For now, we'll use mock data
    setDocuments(mockLivingDocuments);
  }, []);

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold text-black-6c mb-2'>
          Living Documents
        </h1>
        <p className='text-[var(--custom-gray-medium)]'>
          Create and manage documents that require regular updates, such as
          emergency contacts, pet care instructions, and digital asset
          management.
        </p>
      </div>

      <LivingDocumentList documents={documents} />

      <div className='bg-blue-50 p-6 rounded-lg border border-blue-100 mt-8'>
        <h3 className='text-lg font-medium text-blue-800 mb-2'>
          About Living Documents
        </h3>
        <p className='text-blue-700 mb-4'>
          Living documents are designed to store information that needs regular
          updates to remain relevant and useful.
        </p>
        <ul className='list-disc list-inside text-blue-700 space-y-2'>
          <li>Documents are automatically flagged when they need review</li>
          <li>
            Set custom review frequencies based on how often the information
            changes
          </li>
          <li>Receive notifications when documents are due for review</li>
          <li>
            All documents are stored securely and can be accessed at any time
          </li>
        </ul>
      </div>
    </div>
  );
}
