'use client';

import React, { useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { fetchTemplates } from '@/utils/templates';
import { useAuth } from '@/app/context/AuthContext';
import { fetchUserProfileByCognitoId } from '@/lib/data/users';
import {
  getActiveInterviews,
  getUserInterviewProgress,
} from '@/lib/api/interview-new-user';

export default function DocumentCreatePage() {
  const router = useRouter();
  const { user } = useAuth();
  const [documentType, setDocumentType] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const { data: templates = [], isLoading } = useQuery({
    queryKey: ['templates'],
    queryFn: fetchTemplates,
  });

  // Fetch user profile to get real state data
  const { data: userProfile } = useQuery({
    queryKey: ['userProfile', user?.userId],
    queryFn: () => fetchUserProfileByCognitoId(user?.userId || ''),
    enabled: !!user?.userId,
  });

  // Get user state from profile or fallback to California
  const userState = userProfile?.state || 'California';

  const documentTypes = useMemo(() => {
    const filteredTemplatesByState = templates.filter(
      template => template.templateState === userState
    );
    return filteredTemplatesByState.map(template => {
      const templateType = template.type;
      return <SelectItem value={template.id}>{templateType}</SelectItem>;
    });
  }, [templates]);

  const handleDocumentTypeChange = (value: string) => {
    setDocumentType(value);
    setError(null);
  };

  const handleCreateDocument = async () => {
    if (!documentType) {
      setError('Please select a document type');
      return;
    }

    try {
      setIsCreating(true);
      setError(null);

      // Check if user has completed at least one interview
      const interviews = await getActiveInterviews();

      if (interviews.length === 0) {
        setError('No interviews available. Please contact support.');
        return;
      }

      // Check if user has completed any interview
      let hasCompletedInterview = false;

      for (const interview of interviews) {
        const progress = await getUserInterviewProgress(
          interview.latestVersion.id
        );
        if (progress?.isCompleted) {
          hasCompletedInterview = true;
          break;
        }
      }

      if (!hasCompletedInterview) {
        // Redirect to interview if no completed interviews found
        router.push('/dashboard/member/interview/new');
        return;
      }

      // If user has completed interview, proceed to document preview
      router.push(`/dashboard/member/documents/preview/${documentType}`);
    } catch (error) {
      console.error('Error checking interview completion:', error);
      setError('Failed to verify interview completion. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-black-6c mb-2'>
            Create Legal Documents
          </h1>
          <p className='text-[var(--custom-gray-medium)]'>
            Select the type of document you want to create. We'll use your
            interview responses to generate a personalized document.
          </p>
        </div>

        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Document Selection</CardTitle>
            <CardDescription>
              Choose the type of legal document you want to create
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-6'>
              <div className='space-y-2'>
                <label className='text-sm font-medium'>Document Type</label>
                <Select
                  value={documentType}
                  onValueChange={handleDocumentTypeChange}
                >
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Select document type' />
                  </SelectTrigger>
                  <SelectContent>{documentTypes}</SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <label className='text-sm font-medium'>State</label>
                <div className='p-3 border rounded-md bg-gray-50'>
                  <p>{userState}</p>
                  <p className='text-xs text-[var(--custom-gray-medium)] mt-1'>
                    This is your state of residence from your profile. Documents
                    will be created according to {userState} law.
                  </p>
                </div>
              </div>

              {error && (
                <Alert variant='destructive'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
          <CardFooter className='flex justify-end'>
            <Button
              onClick={handleCreateDocument}
              disabled={isCreating || !documentType}
            >
              {isCreating ? 'Checking Interview...' : 'Create Document'}
            </Button>
          </CardFooter>
        </Card>

        <div className='bg-blue-50 p-6 rounded-lg border border-blue-100'>
          <h3 className='text-lg font-medium text-blue-800 mb-2'>
            About Document Generation
          </h3>
          <p className='text-blue-700 mb-4'>
            Our document generation system creates legally compliant documents
            based on your state's requirements and the information you provided
            during the interview process.
          </p>
          <ul className='list-disc list-inside text-blue-700 space-y-2'>
            <li>
              Documents are tailored to your specific needs and state laws
            </li>
            <li>You'll have a chance to review before finalizing</li>
            <li>
              All documents are stored securely and can be accessed at any time
            </li>
            <li>
              You'll be notified if state laws change that affect your documents
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
