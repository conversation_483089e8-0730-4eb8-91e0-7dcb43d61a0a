'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Package, Search, History, Info, Download, Eye } from 'lucide-react';
import TrackingStatus from '@/app/components/ups/TrackingStatus';
import LabelPreview from '@/app/components/ups/LabelPreview';
import { ShippingLabelResult } from '@/app/types/ups';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import SaveUserAddress from '@/components/ups/SaveUserAddress';
import { toast } from 'sonner';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

export default function ShippingPage() {
  const [recentLabels, setRecentLabels] = useState<ShippingLabelResult[]>([]);
  const [selectedTrackingNumber, setSelectedTrackingNumber] =
    useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('create');
  const [isLoadingLabels, setIsLoadingLabels] = useState(false);

  // Load shipping labels from database
  useEffect(() => {
    loadShippingLabels();
  }, []);

  const loadShippingLabels = async () => {
    setIsLoadingLabels(true);
    try {
      // Get current user
      const currentUser = await getCurrentUser();

      // Get current user data
      const { data: userList } = await client.models.User.list({
        filter: { cognitoId: { eq: currentUser.userId } },
        selectionSet: ['id', 'role'],
      });

      if (!userList || userList.length === 0) {
        return;
      }

      const userData = userList[0];

      // Get user's shipping labels
      // For regular users: get labels they created (userId)
      // For WelonTrust users: get labels sent to them (assignedWelonTrustId) + labels they created
      let labelsFilter;
      if (userData.role === 'WelonTrust') {
        // WelonTrust users see both labels sent to them and labels they created
        labelsFilter = {
          or: [
            { userId: { eq: userData.id } },
            { assignedWelonTrustId: { eq: userData.id } },
          ],
        };
      } else {
        // Regular users see only labels they created
        labelsFilter = { userId: { eq: userData.id } };
      }

      const { data: labels } = await client.models.ShippingLabel.list({
        filter: labelsFilter,
        selectionSet: [
          'id',
          'userId',
          'assignedWelonTrustId',
          'trackingNumber',
          'labelUrl',
          'cost',
          'estimatedDelivery',
          'createdAt',
          'fromAddress',
          'toAddress',
        ],
      });

      if (labels && labels.length > 0) {
        // Convert to ShippingLabelResult format with additional info for WelonTrust users
        const convertedLabels: (ShippingLabelResult & {
          isIncoming?: boolean;
          fromAddress?: any;
          toAddress?: any;
        })[] = labels.map(label => ({
          trackingNumber: label.trackingNumber,
          labelUrl: label.labelUrl,
          cost: JSON.parse(label.cost),
          estimatedDelivery: label.estimatedDelivery || undefined,
          createdAt: label.createdAt,
          // For WelonTrust users, mark if this is an incoming package
          isIncoming:
            userData.role === 'WelonTrust' &&
            label.assignedWelonTrustId === userData.id &&
            label.userId !== userData.id,
          fromAddress: label.fromAddress
            ? JSON.parse(label.fromAddress)
            : undefined,
          toAddress: label.toAddress ? JSON.parse(label.toAddress) : undefined,
        }));

        setRecentLabels(convertedLabels);
      }
    } catch (error) {
      console.error('Error loading shipping labels:', error);
    } finally {
      setIsLoadingLabels(false);
    }
  };

  const handleTrackFromHistory = (trackingNumber: string) => {
    setSelectedTrackingNumber(trackingNumber);
    setActiveTab('track'); // Switch to track tab
  };

  const downloadLabelDirect = (labelUrl: string, trackingNumber: string) => {
    try {
      // Try direct download first
      if (labelUrl.startsWith('data:')) {
        const link = window.document.createElement('a');
        link.href = labelUrl;
        const fileExtension = labelUrl.startsWith('data:application/pdf')
          ? 'pdf'
          : 'gif';
        link.download = `ups-label-${trackingNumber}.${fileExtension}`;
        window.document.body.appendChild(link);
        link.click();
        window.document.body.removeChild(link);
      } else {
        // Fallback to API download
        downloadLabelViaAPI(labelUrl, trackingNumber);
      }
    } catch (error) {
      console.error('Direct download failed, trying API:', error);
      downloadLabelViaAPI(labelUrl, trackingNumber);
    }
  };

  const downloadLabelViaAPI = async (
    labelUrl: string,
    trackingNumber: string
  ) => {
    try {
      const response = await fetch('/api/ups/download-label', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          labelData: labelUrl,
          trackingNumber: trackingNumber,
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = window.document.createElement('a');
        link.href = url;
        const fileExtension = labelUrl.startsWith('data:application/pdf')
          ? 'pdf'
          : 'gif';
        link.download = `ups-label-${trackingNumber}.${fileExtension}`;
        window.document.body.appendChild(link);
        link.click();
        window.document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to download label via API:', error);
    }
  };

  // Default sender address (this would typically come from user settings or config)
  const defaultFromAddress = {
    name: 'Your Company Name',
    phone: '(*************',
    addressLine1: '123 Business St',
    addressLine2: 'Suite 100',
    city: 'Los Angeles',
    stateProvinceCode: 'CA',
    postalCode: '90210',
    countryCode: 'US',
  };

  return (
    <div className='container mx-auto py-6 space-y-6'>
      <div className='space-y-2'>
        <h1 className='text-3xl font-bold'>Shipping & Tracking</h1>
        <p className='text-muted-foreground'>
          Save your address, track your packages, and view active labels
        </p>
      </div>

      <Alert>
        <Info className='h-4 w-4' />
        <AlertDescription>
          Save your address for future shipping labels and track your package
          delivery status.
        </AlertDescription>
      </Alert>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className='space-y-6'
      >
        <TabsList className='grid w-full grid-cols-3'>
          <TabsTrigger value='create' className='flex items-center gap-2'>
            <Package className='h-4 w-4' />
            Save Address
          </TabsTrigger>
          <TabsTrigger value='track' className='flex items-center gap-2'>
            <Search className='h-4 w-4' />
            Track Package
          </TabsTrigger>
          <TabsTrigger value='history' className='flex items-center gap-2'>
            <History className='h-4 w-4' />
            Active Labels
          </TabsTrigger>
        </TabsList>

        <TabsContent value='create' className='space-y-6'>
          <SaveUserAddress
            onAddressSaved={() => {
              toast.success('Address saved successfully!');
              loadShippingLabels(); // Reload labels when address is saved
            }}
          />
        </TabsContent>

        <TabsContent value='track' className='space-y-6'>
          <TrackingStatus
            initialTrackingNumber={selectedTrackingNumber}
            autoRefresh={true}
            refreshInterval={300} // 5 minutes
          />
        </TabsContent>

        <TabsContent value='history' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <History className='h-5 w-5' />
                Active Shipping Labels
              </CardTitle>
              <CardDescription>
                View, download, and track your recently created shipping labels
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentLabels.length > 0 ? (
                <div className='space-y-4'>
                  {recentLabels.map((label, index) => (
                    <div key={index} className='border rounded-lg p-4'>
                      <div className='flex items-center justify-between mb-3'>
                        <div className='flex-1'>
                          <div className='flex items-center gap-2 mb-1'>
                            <p className='font-mono text-sm font-medium'>
                              {label.trackingNumber}
                            </p>
                            <Badge variant='outline' className='text-xs'>
                              {(label as any).isIncoming
                                ? 'Incoming Package'
                                : 'Label Created'}
                            </Badge>
                          </div>
                          {(label as any).isIncoming &&
                            (label as any).fromAddress && (
                              <div className='text-xs text-muted-foreground mb-1'>
                                <span className='font-medium'>From:</span>{' '}
                                {(label as any).fromAddress.name}
                              </div>
                            )}
                          <div className='flex items-center gap-3 text-xs text-muted-foreground'>
                            <span>
                              Cost: {label.cost.currency} ${label.cost.amount}
                            </span>
                            {label.createdAt && (
                              <span>
                                Created:{' '}
                                {new Date(label.createdAt).toLocaleDateString()}
                              </span>
                            )}
                            {label.estimatedDelivery && (
                              <span>
                                Est. Delivery:{' '}
                                {new Date(
                                  label.estimatedDelivery
                                ).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className='flex items-center gap-2'>
                        <LabelPreview
                          labelUrl={label.labelUrl}
                          trackingNumber={label.trackingNumber}
                          onDownload={() =>
                            downloadLabelDirect(
                              label.labelUrl,
                              label.trackingNumber
                            )
                          }
                        />
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() =>
                            downloadLabelDirect(
                              label.labelUrl,
                              label.trackingNumber
                            )
                          }
                        >
                          <Download className='h-4 w-4 mr-1' />
                          Download
                        </Button>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() =>
                            handleTrackFromHistory(label.trackingNumber)
                          }
                        >
                          <Eye className='h-4 w-4 mr-1' />
                          Track
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='text-center py-8 text-muted-foreground'>
                  <Package className='h-12 w-12 mx-auto mb-4 opacity-50' />
                  <p>No shipping labels created yet.</p>
                  <p className='text-sm'>
                    Create your first shipping label to see it here.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
