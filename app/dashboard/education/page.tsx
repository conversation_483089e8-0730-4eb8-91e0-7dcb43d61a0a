/**
 * Education Library Page
 *
 * Main page for browsing and searching educational content.
 */

import React from 'react';
import { ContentLibrary } from '@/components/education/content-library';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

export default function EducationPage() {
  return (
    <div className='container mx-auto py-8 space-y-8'>
      <div className='flex flex-col space-y-2'>
        <h1 className='text-3xl font-bold'>Educational Resources</h1>
        <p className='text-muted-foreground'>
          Browse our library of educational content to learn more about estate
          planning.
        </p>
      </div>

      {/* Featured Content */}
      <Card>
        <CardHeader>
          <CardTitle>Featured Resources</CardTitle>
          <CardDescription>
            Recommended content to help you understand estate planning.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue='getting-started'>
            <TabsList className='mb-4'>
              <TabsTrigger value='getting-started'>Getting Started</TabsTrigger>
              <TabsTrigger value='documents'>Documents</TabsTrigger>
              <TabsTrigger value='emergency'>Emergency Planning</TabsTrigger>
            </TabsList>

            <TabsContent value='getting-started'>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                <Card>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-lg'>
                      Understanding Estate Planning
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-sm text-muted-foreground'>
                      Learn the basics of estate planning and why it's
                      important.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-lg'>
                      Estate Planning Process
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-sm text-muted-foreground'>
                      A step-by-step guide to the estate planning process.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-lg'>
                      Estate Planning Assistant
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-sm text-muted-foreground'>
                      Get answers to common questions about estate planning.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value='documents'>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                <Card>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-lg'>
                      Understanding Wills
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-sm text-muted-foreground'>
                      Learn what a will is and why it's important.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-lg'>How Trusts Work</CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-sm text-muted-foreground'>
                      Learn about different types of trusts and how they can
                      benefit your estate plan.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-lg'>
                      Choosing an Executor
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-sm text-muted-foreground'>
                      Tips for selecting the best executor for your estate.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value='emergency'>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                <Card>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-lg'>
                      Setting Up Emergency Contacts
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-sm text-muted-foreground'>
                      A step-by-step guide to setting up and managing your
                      emergency contacts.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-lg'>Dead Man's Switch</CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-sm text-muted-foreground'>
                      Learn how the dead man's switch works and how to set it
                      up.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className='p-4'>
                    <CardTitle className='text-lg'>
                      Emergency Protocol
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-sm text-muted-foreground'>
                      Understand how emergency protocols work and when they're
                      activated.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Content Library */}
      <div>
        <h2 className='text-2xl font-bold mb-6'>Content Library</h2>
        <ContentLibrary />
      </div>
    </div>
  );
}
