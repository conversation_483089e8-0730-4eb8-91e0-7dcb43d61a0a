// @ts-nocheck

'use client';

import { generateClient } from 'aws-amplify/data';
import { type Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

export enum OnboardingStep {
  CHILD_STATUS = 'child_status',
  ESTATE = 'estate',
  COMPLETED = 'completed',
}

// Child status options
export enum ChildStatus {
  YES = 'yes',
  NO = 'no',
  PLANNING = 'planning',
}

// Estate size options
export enum EstateSize {
  UNDER_100K = 'under100k',
  BETWEEN_100K_500K = '100k-500k',
  BETWEEN_500K_1M = '500k-1m',
  OVER_1M = 'over1m',
}

// Interface for onboarding answers
export interface OnboardingAnswers {
  childStatus?: ChildStatus;
  estateSize?: EstateSize;
  signupCompleted?: boolean;
  signupDate?: string;
  lastUpdated?: string;
}

// Helper function to get the current user's onboarding status
export async function getUserOnboardingStatus(): Promise<
  Schema['UserOnboarding']['type']
> {
  try {
    // Get the current authenticated user
    const user = await getCurrentUser();

    // Generate the data client
    const client = generateClient<Schema>();

    // Query for the user's onboarding record
    const { data: onboardingRecord } = await client.models.UserOnboarding.get({
      userId: user.userId,
    });

    // If no record exists, create a new one with the initial step
    if (!onboardingRecord) {
      console.log('===> No onboarding record found, creating new one');
      const { data: newOnboarding } = await client.models.UserOnboarding.create(
        {
          userId: user.userId,
          currentStep: OnboardingStep.CHILD_STATUS,
          isComplete: false,
          metadata: null,
          lastUpdated: new Date().toISOString(),
        }
      );

      if (!newOnboarding) {
        throw new Error('Failed to create onboarding record');
      }

      return {
        currentStep: newOnboarding.currentStep,
        isComplete: newOnboarding.isComplete,
        metadata: newOnboarding.metadata,
      };
    }

    // Parse metadata if it exists
    let parsedMetadata = onboardingRecord.metadata;
    if (typeof parsedMetadata === 'string' && parsedMetadata) {
      try {
        parsedMetadata = JSON.parse(parsedMetadata);
      } catch (e) {
        console.error('Error parsing metadata:', e);
      }
    }

    // Return the existing onboarding record
    return {
      currentStep: onboardingRecord.currentStep,
      isComplete: onboardingRecord.isComplete,
      metadata: parsedMetadata,
    };
  } catch (error) {
    console.error('Error getting user onboarding status:', error);
    // Return default values if there's an error
    return {
      currentStep: OnboardingStep.CHILD_STATUS,
      isComplete: false,
      metadata: null,
    };
  }
}

// Helper function to update the user's onboarding step
export async function updateUserOnboardingStep(
  step: OnboardingStep,
  metadata?: any
) {
  try {
    // Get the current authenticated user
    const user = await getCurrentUser();

    // Generate the data client
    const client = generateClient<Schema>();

    // Query for the user's onboarding record
    const { data: onboardingRecord } = await client.models.UserOnboarding.get({
      userId: user.userId,
    });

    // Ensure metadata is properly formatted for JSON storage
    const formattedMetadata = metadata ? JSON.stringify(metadata) : null;

    // If no record exists, create a new one with the provided step
    if (!onboardingRecord) {
      console.log('===> Creating new onboarding record');
      const { data: newOnboarding } = await client.models.UserOnboarding.create(
        {
          userId: user.userId,
          currentStep: step,
          isComplete: step === OnboardingStep.COMPLETED,
          metadata: formattedMetadata,
          lastUpdated: new Date().toISOString(),
        }
      );

      if (!newOnboarding) {
        throw new Error('Failed to create onboarding record');
      }

      return newOnboarding;
    }

    // Update the existing onboarding record
    const { data: updatedOnboarding, errors } =
      await client.models.UserOnboarding.update({
        id: onboardingRecord.id,
        userId: user.userId,
        currentStep: step,
        isComplete: step === OnboardingStep.COMPLETED,
        metadata: formattedMetadata || onboardingRecord.metadata,
        lastUpdated: new Date().toISOString(),
      });

    console.log('===> Errors', errors);

    if (!updatedOnboarding) {
      throw new Error('Failed to update onboarding record');
    }

    return updatedOnboarding;
  } catch (error) {
    console.error('===> Error updating user onboarding step:', error);
    throw error;
  }
}

// Helper function to check if onboarding is complete
export async function isOnboardingComplete() {
  const status = await getUserOnboardingStatus();
  return status.isComplete;
}

// Helper function to save onboarding answers
export async function saveOnboardingAnswer(
  step: OnboardingStep,
  answer: string,
  existingAnswers?: OnboardingAnswers
): Promise<Schema['UserOnboarding']['type']> {
  try {
    // Get existing metadata or create a new object
    const metadata: OnboardingAnswers = existingAnswers || {};

    // Update the appropriate field based on the step
    switch (step) {
      case OnboardingStep.CHILD_STATUS:
        // Validate that answer is a valid ChildStatus value
        if (Object.values(ChildStatus).includes(answer as ChildStatus)) {
          metadata.childStatus = answer as ChildStatus;
        } else {
          console.error(`Invalid ChildStatus value: ${answer}`);
          throw new Error(`Invalid ChildStatus value: ${answer}`);
        }
        break;
      case OnboardingStep.ESTATE:
        // Validate that answer is a valid EstateSize value
        if (Object.values(EstateSize).includes(answer as EstateSize)) {
          metadata.estateSize = answer as EstateSize;
        } else {
          console.error(`Invalid EstateSize value: ${answer}`);
          throw new Error(`Invalid EstateSize value: ${answer}`);
        }
        break;
      default:
        console.warn(`Unhandled step: ${step}`);
        break;
    }

    // Determine next step
    let nextStep: OnboardingStep;
    if (step === OnboardingStep.CHILD_STATUS) {
      nextStep = OnboardingStep.ESTATE;
    } else if (step === OnboardingStep.ESTATE) {
      nextStep = OnboardingStep.COMPLETED;
    } else {
      nextStep = step;
    }

    // Update the onboarding record with the new metadata
    const updatedRecord = await updateUserOnboardingStep(nextStep, metadata);

    if (!updatedRecord) {
      throw new Error('Failed to update onboarding record');
    }

    return updatedRecord;
  } catch (error) {
    console.error('===> Error saving onboarding answer:', error);
    throw error;
  }
}

// Helper function to get onboarding answers
export async function getOnboardingAnswers(): Promise<OnboardingAnswers | null> {
  try {
    const status = await getUserOnboardingStatus();

    if (!status.metadata) {
      console.log('===> No metadata found in onboarding status');
      return null;
    }

    // Ensure metadata is properly parsed
    let metadata = status.metadata;
    if (typeof metadata === 'string') {
      try {
        metadata = JSON.parse(metadata);
      } catch (e) {
        console.error('Error parsing metadata string:', e);
        return null;
      }
    }

    // Validate the metadata structure
    const answers: OnboardingAnswers = {};

    // Copy valid properties
    if (
      metadata.childStatus &&
      Object.values(ChildStatus).includes(metadata.childStatus)
    ) {
      answers.childStatus = metadata.childStatus;
    }

    if (
      metadata.estateSize &&
      Object.values(EstateSize).includes(metadata.estateSize)
    ) {
      answers.estateSize = metadata.estateSize;
    }

    if (typeof metadata.signupCompleted === 'boolean') {
      answers.signupCompleted = metadata.signupCompleted;
    }

    if (metadata.signupDate) {
      answers.signupDate = metadata.signupDate;
    }

    if (metadata.lastUpdated) {
      answers.lastUpdated = metadata.lastUpdated;
    }

    return Object.keys(answers).length > 0 ? answers : null;
  } catch (error) {
    console.error('Error getting onboarding answers:', error);
    return null;
  }
}
