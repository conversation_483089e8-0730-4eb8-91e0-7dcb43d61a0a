import { signUp } from 'aws-amplify/auth';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';

export interface CustomSignUpRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  state: string;
  birthdayDate: Date;
  phone: string;
  inviteToken?: string; // Optional invite token for invited users
}

export interface CustomSignUpResponse {
  success: boolean;
  error?: string;
  message?: string;
}

/**
 * Custom sign-up function that creates user in Cognito but doesn't auto-confirm
 * Instead, it generates a verification token and sends a custom email
 */
export const customSignUp = async (
  request: CustomSignUpRequest
): Promise<CustomSignUpResponse> => {
  try {
    const client = generateClient<Schema>({
      authMode: 'iam',
    });

    // Format phone number to E.164 format for AWS Cognito
    const formattedPhone = request.phone.startsWith('+1')
      ? request.phone
      : `+1${request.phone.replace(/\D/g, '')}`;

    // Format date to YYYY-MM-DD for AWS Cognito
    const formattedBirthday = request.birthdayDate
      ? request.birthdayDate.toISOString().split('T')[0]
      : '';

    // If this is an invite signup, validate the invite by token
    if (request.inviteToken) {
      // Find the invite by token
      const { data: invites, errors } = await client.models.UserInvite.list({
        filter: { token: { eq: request.inviteToken } },
      });

      if (errors || !invites || invites.length === 0) {
        throw new Error('Invalid invitation token');
      }

      const inviteData = invites[0];

      // Check if invite is still valid
      if (inviteData.status !== 'pending') {
        throw new Error('This invitation has already been used or cancelled');
      }

      // Check if invite has expired
      if (new Date(inviteData.expiresAt) < new Date()) {
        throw new Error('This invitation has expired');
      }

      // Use email from invite (security check)
      if (inviteData.email !== request.email) {
        console.error(
          `Email mismatch: invite email ${inviteData.email} vs request email ${request.email}`
        );
        throw new Error('Email does not match the invitation');
      }

      console.log(
        `Validated invite: ${inviteData.email} with token ${request.inviteToken}`
      );
    }

    // Create user in Cognito (this will be unconfirmed)
    const signUpOptions: any = {
      userAttributes: {
        email: request.email,
        given_name: request.firstName,
        family_name: request.lastName,
        address: request.state,
        birthdate: formattedBirthday,
        phone_number: formattedPhone,
        gender: 'male',
      },
      autoSignIn: false, // Don't auto sign in
    };

    // Add invite token to clientMetadata if provided
    if (request.inviteToken) {
      signUpOptions.clientMetadata = {
        inviteToken: request.inviteToken,
      };
    }

    await signUp({
      username: request.email,
      password: request.password,
      options: signUpOptions,
    });

    // Send verification email (token will be generated automatically on backend)
    const emailResult = await client.mutations.sendEmail({
      to: request.email,
      subject: 'Verify Your Email - Childfree',
      message: `Welcome to Childfree, ${request.firstName}!\n\nPlease verify your email address by clicking the link below to complete your registration.\n\nThis link will expire in 24 hours.`,
      emailType: 'accountConfirmation',
      isNewAccount: true,
    });

    const emailData = emailResult.data as any;

    // Try to parse if it's a string
    let parsedEmailData = emailData;
    if (typeof emailData === 'string') {
      try {
        parsedEmailData = JSON.parse(emailData);
        console.log('Parsed email data:', parsedEmailData);
      } catch (e) {
        console.log('Failed to parse email data as JSON:', e);
      }
    }

    if (!parsedEmailData?.success) {
      throw new Error(
        parsedEmailData?.error || 'Failed to send verification email'
      );
    }

    // Note: Invite status will be updated to 'accepted' by the postSignUpTrigger

    return {
      success: true,
      message:
        'Account created successfully. Please check your email to verify your account.',
    };
  } catch (error) {
    console.error('Custom sign-up error:', error);

    let errorMessage = 'Failed to create account';
    if (typeof error === 'object' && error !== null && 'message' in error) {
      errorMessage = (error as { message: string }).message;
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
};
