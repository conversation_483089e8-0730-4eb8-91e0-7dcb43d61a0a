'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

const inviteSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  role: z.enum(['WelonTrust'], {
    required_error: 'Please select a role',
  }),
});

type InviteFormData = z.infer<typeof inviteSchema>;

interface InviteUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInviteSent?: () => void;
}

export function InviteUserModal({
  open,
  onOpenChange,
  onInviteSent,
}: InviteUserModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<InviteFormData>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: '',
      role: 'WelonTrust',
    },
  });

  const onSubmit = async (data: InviteFormData) => {
    setIsLoading(true);

    try {
      // 1. Check if user already exists in the system
      const { data: existingUsers, errors: userCheckErrors } =
        await client.models.User.list({
          filter: { email: { eq: data.email } },
        });

      if (userCheckErrors) {
        throw new Error(
          `Error checking existing users: ${userCheckErrors[0].message}`
        );
      }

      if (existingUsers && existingUsers.length > 0) {
        throw new Error(
          'This email is already registered in the system. The user can sign in directly.'
        );
      }

      // 2. Check if there's already a pending invite for this email
      const { data: existingInvites, errors: inviteCheckErrors } =
        await client.models.UserInvite.list({
          filter: {
            and: [{ email: { eq: data.email } }, { status: { eq: 'pending' } }],
          },
        });

      if (inviteCheckErrors) {
        throw new Error(
          `Error checking existing invites: ${inviteCheckErrors[0].message}`
        );
      }

      if (existingInvites && existingInvites.length > 0) {
        const existingInvite = existingInvites[0];
        const expiresAt = new Date(existingInvite.expiresAt);
        const isExpired = expiresAt < new Date();

        if (!isExpired) {
          throw new Error(
            `There is already a pending invitation for ${
              data.email
            }. The invitation expires on ${expiresAt.toLocaleDateString()}.`
          );
        } else {
          // Automatically cancel expired invite
          await client.models.UserInvite.update({
            id: existingInvite.id,
            status: 'expired',
          });
          console.log(
            `Automatically cancelled expired invite for ${data.email}`
          );
        }
      }

      // Get current user info
      const currentUser = await getCurrentUser();

      // Generate a unique token for the invite
      const token = crypto.randomUUID();

      // Set expiration to 7 days from now
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      // Create the invite in the database
      const { data: inviteData, errors } =
        await client.models.UserInvite.create({
          email: data.email,
          role: data.role,
          invitedBy: currentUser.userId,
          invitedByEmail: currentUser.signInDetails?.loginId || '',
          token,
          status: 'pending',
          expiresAt: expiresAt.toISOString(),
          createdAt: new Date().toISOString(),
        });

      if (errors) {
        throw new Error(errors[0].message);
      }

      // Send invitation email
      const emailResult = await client.mutations.sendEmail({
        to: data.email,
        subject: 'Invitation to join Childfree as Welon Trust',
        message: `You have been invited to join Childfree as a Welon Trust member.`,
        emailType: 'invitation',
        verificationLink: `${window.location.origin}/auth/accept-invite?token=${token}`,
      });

      if (emailResult.errors) {
        console.error('Email sending errors:', emailResult.errors);
        // Don't throw error here, invite was created successfully
        toast.warning('Invite created but email may not have been sent');
      }

      toast.success(`Invitation sent to ${data.email}`, {
        description:
          'The user will receive an email with instructions to join. The invitation will expire in 7 days.',
      });

      form.reset();
      onOpenChange(false);
      onInviteSent?.();
    } catch (error) {
      console.error('Failed to send invite:', error);
      toast.error('Failed to send invitation', {
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>Invite User</DialogTitle>
          <DialogDescription>
            Send an invitation to join as a Welon Trust member. We'll check if
            the email is already registered or has a pending invitation. They
            will receive an email with instructions to complete their
            registration.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='email'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter email address'
                      type='email'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='role'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select a role' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='WelonTrust'>Welon Trust</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex justify-end space-x-2 pt-4'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Sending...' : 'Send Invitation'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
