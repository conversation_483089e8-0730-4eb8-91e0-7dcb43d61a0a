'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Edit,
  Trash2,
  GripVertical,
  Type,
  List,
  CheckCircle,
  AlertTriangle,
  Copy,
  Upload,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { LivingDocumentQuestionFormDialog } from './question-form-dialog';
import type {
  LivingDocumentQuestion,
  LivingDocumentQuestionType,
} from '@/types/living-document-builder';

interface QuestionBuilderProps {
  templateId: string;
  questions: LivingDocumentQuestion[];
  onQuestionsUpdated: (questions: LivingDocumentQuestion[]) => void;
}

export function LivingDocumentQuestionBuilder({
  templateId,
  questions,
  onQuestionsUpdated,
}: QuestionBuilderProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingQuestion, setEditingQuestion] =
    useState<LivingDocumentQuestion | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleCreateQuestion = () => {
    setEditingQuestion(null);
    setShowCreateDialog(true);
  };

  const handleEditQuestion = (question: LivingDocumentQuestion) => {
    setEditingQuestion(question);
    setShowCreateDialog(true);
  };

  const handleDeleteQuestion = (questionId: string) => {
    const updatedQuestions = questions
      .filter(q => q.id !== questionId)
      .map((q, index) => ({ ...q, order: index + 1 })); // Reorder after deletion
    onQuestionsUpdated(updatedQuestions);
  };

  const handleDuplicateQuestion = (question: LivingDocumentQuestion) => {
    const newQuestion: LivingDocumentQuestion = {
      ...question,
      id: `question_${Date.now()}`,
      text: `${question.text} (Copy)`,
      order: questions.length + 1,
    };
    onQuestionsUpdated([...questions, newQuestion]);
  };

  const handleQuestionSaved = (
    questionData: Omit<LivingDocumentQuestion, 'id' | 'order'>
  ) => {
    if (editingQuestion) {
      // Update existing question
      const updatedQuestions = questions.map(q =>
        q.id === editingQuestion.id
          ? {
              ...questionData,
              id: editingQuestion.id,
              order: editingQuestion.order,
            }
          : q
      );
      onQuestionsUpdated(updatedQuestions);
    } else {
      // Create new question
      const newQuestion: LivingDocumentQuestion = {
        ...questionData,
        id: `question_${Date.now()}`,
        order: questions.length + 1,
      };
      onQuestionsUpdated([...questions, newQuestion]);
    }
    setShowCreateDialog(false);
    setEditingQuestion(null);
  };

  const getQuestionTypeIcon = (type: LivingDocumentQuestionType) => {
    switch (type) {
      case 'text':
        return <Type className='h-4 w-4' />;
      case 'select':
        return <List className='h-4 w-4' />;
      case 'radio':
        return <CheckCircle className='h-4 w-4' />;
      case 'file':
        return <Upload className='h-4 w-4' />;
      default:
        return <Type className='h-4 w-4' />;
    }
  };

  const getQuestionTypeLabel = (type: LivingDocumentQuestionType) => {
    switch (type) {
      case 'text':
        return 'Text Input';
      case 'select':
        return 'Dropdown';
      case 'radio':
        return 'Radio Buttons';
      case 'file':
        return 'File Upload';
      default:
        return type;
    }
  };

  const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex justify-between items-center'>
        <div>
          <h2 className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
            Template Questions
          </h2>
          <p className='text-[var(--custom-gray-medium)]'>
            Add and manage questions for this living document template
          </p>
        </div>
        <Button onClick={handleCreateQuestion}>
          <Plus className='mr-2 h-4 w-4' />
          Add Question
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Questions List */}
      <div className='space-y-4'>
        {sortedQuestions.length === 0 ? (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-12'>
              <Type className='h-12 w-12 text-gray-400 mb-4' />
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                No questions added yet
              </h3>
              <p className='text-gray-500 text-center mb-4'>
                Add questions to make this template interactive for users
              </p>
              <Button onClick={handleCreateQuestion} variant='outline'>
                <Plus className='mr-2 h-4 w-4' />
                Add Your First Question
              </Button>
            </CardContent>
          </Card>
        ) : (
          sortedQuestions.map((question, index) => (
            <Card
              key={question.id}
              className='hover:shadow-md transition-shadow'
            >
              <CardHeader className='pb-3'>
                <div className='flex items-start justify-between'>
                  <div className='flex items-start space-x-3 flex-1'>
                    <div className='flex items-center space-x-2 mt-1'>
                      <GripVertical className='h-4 w-4 text-gray-400' />
                      <span className='text-sm font-medium text-gray-500'>
                        {index + 1}
                      </span>
                    </div>
                    <div className='flex-1'>
                      <div className='flex items-center space-x-2 mb-2'>
                        {getQuestionTypeIcon(question.type)}
                        <Badge variant='outline'>
                          {getQuestionTypeLabel(question.type)}
                        </Badge>
                        {question.required && (
                          <Badge variant='destructive' className='text-xs'>
                            Required
                          </Badge>
                        )}
                        {question.allowMultiple && (
                          <Badge variant='secondary' className='text-xs'>
                            Multiple
                          </Badge>
                        )}
                      </div>
                      <CardTitle className='text-lg'>{question.text}</CardTitle>
                      {question.helpText && (
                        <p className='text-sm text-gray-600 mt-1'>
                          {question.helpText}
                        </p>
                      )}
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant='ghost' size='sm'>
                        <Edit className='h-4 w-4' />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align='end'>
                      <DropdownMenuItem
                        onClick={() => handleEditQuestion(question)}
                      >
                        <Edit className='h-4 w-4 mr-2' />
                        Edit Question
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDuplicateQuestion(question)}
                      >
                        <Copy className='h-4 w-4 mr-2' />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteQuestion(question.id)}
                        className='text-red-600 hover:text-red-700'
                      >
                        <Trash2 className='h-4 w-4 mr-2' />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              {question.options && question.options.length > 0 && (
                <CardContent className='pt-0'>
                  <div className='space-y-2'>
                    <p className='text-sm font-medium text-gray-700'>
                      Options:
                    </p>
                    <div className='space-y-1'>
                      {question.options.map((option: any) => (
                        <div
                          key={option.id}
                          className='flex items-center space-x-2'
                        >
                          <div className='w-2 h-2 bg-gray-300 rounded-full' />
                          <span className='text-sm text-gray-600'>
                            {option.label}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))
        )}
      </div>

      {/* Question Form Dialog */}
      <LivingDocumentQuestionFormDialog
        isOpen={showCreateDialog}
        onClose={() => {
          setShowCreateDialog(false);
          setEditingQuestion(null);
        }}
        onSave={handleQuestionSaved}
        question={editingQuestion}
      />
    </div>
  );
}
