'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Trash2,
  AlertTriangle,
  Type,
  List,
  CheckCircle,
  HelpCircle,
  Upload,
} from 'lucide-react';
import type {
  LivingDocumentQuestion,
  LivingDocumentQuestionType,
  LivingDocumentQuestionOption,
} from '@/types/living-document-builder';

interface QuestionFormData {
  text: string;
  type: LivingDocumentQuestionType;
  required: boolean;
  placeholder: string;
  helpText: string;
  options: LivingDocumentQuestionOption[];
  allowMultiple: boolean;
}

interface LivingDocumentQuestionFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (questionData: Omit<LivingDocumentQuestion, 'id' | 'order'>) => void;
  question?: LivingDocumentQuestion | null;
}

export function LivingDocumentQuestionFormDialog({
  isOpen,
  onClose,
  onSave,
  question,
}: LivingDocumentQuestionFormDialogProps) {
  const [formData, setFormData] = useState<QuestionFormData>({
    text: '',
    type: 'text',
    required: false,
    placeholder: '',
    helpText: '',
    options: [],
    allowMultiple: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  const isEditing = !!question;

  useEffect(() => {
    if (question) {
      setFormData({
        text: question.text,
        type: question.type,
        required: question.required,
        placeholder: question.placeholder || '',
        helpText: question.helpText || '',
        options: question.options || [],
        allowMultiple: question.allowMultiple,
      });
    } else {
      setFormData({
        text: '',
        type: 'text',
        required: false,
        placeholder: '',
        helpText: '',
        options: [],
        allowMultiple: false,
      });
    }
    setErrors({});
    setActiveTab('basic');
  }, [question, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.text.trim()) {
      newErrors.text = 'Question text is required';
    }

    if (
      (formData.type === 'select' || formData.type === 'radio') &&
      formData.options.length === 0
    ) {
      newErrors.options =
        'At least one option is required for select and radio questions';
    }

    if (formData.options.length > 0) {
      const hasEmptyOption = formData.options.some(
        option => !option.label.trim() || !option.value.trim()
      );
      if (hasEmptyOption) {
        newErrors.options = 'All options must have both label and value';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);
      setErrors({});
      onSave(formData);
    } catch (err) {
      setErrors({ submit: 'Failed to save question. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof QuestionFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addOption = () => {
    const newOption: LivingDocumentQuestionOption = {
      id: `option_${Date.now()}`,
      label: '',
      value: '',
    };
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, newOption],
    }));
  };

  const updateOption = (
    index: number,
    field: 'label' | 'value',
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((option, i) =>
        i === index ? { ...option, [field]: value } : option
      ),
    }));
  };

  const removeOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index),
    }));
  };

  const getQuestionTypeIcon = (type: LivingDocumentQuestionType) => {
    switch (type) {
      case 'text':
        return <Type className='h-4 w-4' />;
      case 'select':
        return <List className='h-4 w-4' />;
      case 'radio':
        return <CheckCircle className='h-4 w-4' />;
      case 'file':
        return <Upload className='h-4 w-4' />;
      default:
        return <Type className='h-4 w-4' />;
    }
  };

  const requiresOptions =
    formData.type === 'select' || formData.type === 'radio';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            {getQuestionTypeIcon(formData.type)}
            <span>{isEditing ? 'Edit Question' : 'Create New Question'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update question settings and options.'
              : 'Create a new question for your living document template.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className='space-y-6'>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='basic'>Basic Settings</TabsTrigger>
              <TabsTrigger value='options' disabled={!requiresOptions}>
                Options {requiresOptions && `(${formData.options.length})`}
              </TabsTrigger>
            </TabsList>

            <TabsContent value='basic' className='space-y-4'>
              {/* Question Text */}
              <div className='space-y-2'>
                <Label htmlFor='text'>Question Text *</Label>
                <Textarea
                  id='text'
                  value={formData.text}
                  onChange={e => handleInputChange('text', e.target.value)}
                  placeholder='Enter your question...'
                  rows={3}
                />
                {errors.text && (
                  <p className='text-sm text-red-600'>{errors.text}</p>
                )}
              </div>

              {/* Question Type */}
              <div className='space-y-2'>
                <Label htmlFor='type'>Question Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: LivingDocumentQuestionType) =>
                    handleInputChange('type', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='text'>
                      <div className='flex items-center space-x-2'>
                        <Type className='h-4 w-4' />
                        <span>Text Input</span>
                      </div>
                    </SelectItem>
                    <SelectItem value='select'>
                      <div className='flex items-center space-x-2'>
                        <List className='h-4 w-4' />
                        <span>Dropdown</span>
                      </div>
                    </SelectItem>
                    <SelectItem value='radio'>
                      <div className='flex items-center space-x-2'>
                        <CheckCircle className='h-4 w-4' />
                        <span>Radio Buttons</span>
                      </div>
                    </SelectItem>
                    <SelectItem value='file'>
                      <div className='flex items-center space-x-2'>
                        <Upload className='h-4 w-4' />
                        <span>File Upload</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Placeholder (for text questions) */}
              {formData.type === 'text' && (
                <div className='space-y-2'>
                  <Label htmlFor='placeholder'>Placeholder Text</Label>
                  <Input
                    id='placeholder'
                    value={formData.placeholder}
                    onChange={e =>
                      handleInputChange('placeholder', e.target.value)
                    }
                    placeholder='Enter placeholder text...'
                  />
                </div>
              )}

              {/* Help Text */}
              <div className='space-y-2'>
                <Label htmlFor='helpText'>Help Text</Label>
                <Input
                  id='helpText'
                  value={formData.helpText}
                  onChange={e => handleInputChange('helpText', e.target.value)}
                  placeholder='Optional help text for users...'
                />
              </div>

              {/* Settings */}
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Required Question</Label>
                    <p className='text-sm text-gray-500'>
                      Users must answer this question
                    </p>
                  </div>
                  <Switch
                    checked={formData.required}
                    onCheckedChange={checked =>
                      handleInputChange('required', checked)
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Allow Multiple Answers</Label>
                    <p className='text-sm text-gray-500'>
                      Users can add multiple responses with + button
                    </p>
                  </div>
                  <Switch
                    checked={formData.allowMultiple}
                    onCheckedChange={checked =>
                      handleInputChange('allowMultiple', checked)
                    }
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value='options' className='space-y-4'>
              <div className='flex items-center justify-between'>
                <Label>Answer Options</Label>
                <Button type='button' onClick={addOption} size='sm'>
                  <Plus className='h-4 w-4 mr-1' />
                  Add Option
                </Button>
              </div>

              {formData.options.length === 0 ? (
                <div className='text-center py-8 border-2 border-dashed border-gray-300 rounded-lg'>
                  <HelpCircle className='h-8 w-8 text-gray-400 mx-auto mb-2' />
                  <p className='text-gray-500'>No options added yet</p>
                  <Button
                    type='button'
                    onClick={addOption}
                    variant='outline'
                    size='sm'
                    className='mt-2'
                  >
                    <Plus className='h-4 w-4 mr-1' />
                    Add First Option
                  </Button>
                </div>
              ) : (
                <div className='space-y-3'>
                  {formData.options.map((option, index) => (
                    <div
                      key={option.id}
                      className='flex items-center space-x-2'
                    >
                      <div className='flex-1 grid grid-cols-2 gap-2'>
                        <Input
                          placeholder='Option label'
                          value={option.label}
                          onChange={e =>
                            updateOption(index, 'label', e.target.value)
                          }
                        />
                        <Input
                          placeholder='Option value'
                          value={option.value}
                          onChange={e =>
                            updateOption(index, 'value', e.target.value)
                          }
                        />
                      </div>
                      <Button
                        type='button'
                        variant='ghost'
                        size='sm'
                        onClick={() => removeOption(index)}
                      >
                        <Trash2 className='h-4 w-4' />
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {errors.options && (
                <Alert variant='destructive'>
                  <AlertTriangle className='h-4 w-4' />
                  <AlertDescription>{errors.options}</AlertDescription>
                </Alert>
              )}
            </TabsContent>
          </Tabs>

          {/* Submit Error */}
          {errors.submit && (
            <Alert variant='destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}

          {/* Actions */}
          <div className='flex justify-end space-x-2'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={saving}>
              {saving
                ? 'Saving...'
                : isEditing
                  ? 'Update Question'
                  : 'Create Question'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
