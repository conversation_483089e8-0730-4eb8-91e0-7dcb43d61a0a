'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FileText,
  AlertTriangle,
  Phone,
  Heart,
  Shield,
  Smartphone,
  FileHeart,
  Users,
  Home,
  Car,
  Briefcase,
  BookOpen,
  Settings,
  Star,
  HelpCircle,
} from 'lucide-react';
import type { Schema } from '@/amplify/data/resource';

export type LivingDocumentTemplate = Schema['LivingDocumentTemplate']['type'];

// Form data type based on the actual schema fields
interface TemplateFormData {
  id?: string;
  title: string;
  description: string;
  documentType: string;
  icon?: string;
  status: 'Draft' | 'Active' | 'Archived';
}

interface LivingDocumentTemplateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (templateData: TemplateFormData) => Promise<void>;
  template?: LivingDocumentTemplate | null;
}

const documentTypes = [
  // { value: 'EmergencyContacts', label: 'Emergency Contacts' },
  // { value: 'PetCare', label: 'Pet Care Instructions' },
  // { value: 'DigitalAssets', label: 'Digital Assets' },
  // { value: 'EndOfLifeWishes', label: 'End of Life Wishes' },
  // { value: 'MedicalDirectives', label: 'Medical Directives' },
  { value: 'Other', label: 'Other' },
];

// Available icons for templates
const availableIcons = [
  { value: 'Phone', label: 'Phone', icon: Phone },
  { value: 'Users', label: 'Users', icon: Users },
  { value: 'Heart', label: 'Heart', icon: Heart },
  { value: 'Shield', label: 'Shield', icon: Shield },
  { value: 'Smartphone', label: 'Smartphone', icon: Smartphone },
  { value: 'Home', label: 'Home', icon: Home },
  { value: 'Car', label: 'Car', icon: Car },
  { value: 'Briefcase', label: 'Briefcase', icon: Briefcase },
  { value: 'Book', label: 'Book', icon: BookOpen },
  { value: 'Settings', label: 'Settings', icon: Settings },
  { value: 'Star', label: 'Star', icon: Star },
  { value: 'Document', label: 'Document', icon: FileText },
  { value: 'Help', label: 'Help', icon: HelpCircle },
];

export function LivingDocumentTemplateDialog({
  isOpen,
  onClose,
  onSave,
  template,
}: LivingDocumentTemplateDialogProps) {
  const [formData, setFormData] = useState<TemplateFormData>({
    title: '',
    description: '',
    documentType: '',
    icon: '',
    status: 'Draft',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);

  const isEditing = !!template;

  useEffect(() => {
    if (template) {
      setFormData({
        id: template.id,
        title: template.title || '',
        description: template.description || '',
        documentType: template.documentType || '',
        icon: template.icon || '',
        status: template.status || 'Draft',
      });
    } else {
      setFormData({
        title: '',
        description: '',
        documentType: '',
        icon: '',
        status: 'Draft',
      });
    }
    setErrors({});
  }, [template, isOpen]);

  const handleClose = () => {
    if (!saving) {
      onClose();
      setFormData({
        title: '',
        description: '',
        documentType: '',
        icon: '',
        status: 'Draft',
      });
      setErrors({});
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Template title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.documentType) {
      newErrors.documentType = 'Document type is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);
      setErrors({});
      await onSave(formData);
      handleClose();
    } catch (err) {
      setErrors({
        submit:
          err instanceof Error
            ? err.message
            : 'Failed to save template. Please try again.',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof TemplateFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='max-w-2xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <FileText className='h-5 w-5' />
            <span>{isEditing ? 'Edit Template' : 'Create New Template'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update template information and settings.'
              : 'Create a new living document template with basic information and icon.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className='space-y-6'>
          {errors.submit && (
            <Alert variant='destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}

          {/* Template Title */}
          <div className='space-y-2'>
            <Label htmlFor='title'>Template Title *</Label>
            <Input
              id='title'
              value={formData.title}
              onChange={e => handleInputChange('title', e.target.value)}
              placeholder='Enter template title'
              className={errors.title ? 'border-red-500' : ''}
            />
            {errors.title && (
              <p className='text-sm text-red-600'>{errors.title}</p>
            )}
          </div>

          {/* Description */}
          <div className='space-y-2'>
            <Label htmlFor='description'>Description *</Label>
            <Textarea
              id='description'
              value={formData.description}
              onChange={e => handleInputChange('description', e.target.value)}
              placeholder='Describe what this template is for'
              rows={3}
              className={errors.description ? 'border-red-500' : ''}
            />
            {errors.description && (
              <p className='text-sm text-red-600'>{errors.description}</p>
            )}
          </div>

          {/* Document Type */}
          <div className='space-y-2'>
            <Label htmlFor='documentType'>Document Type *</Label>
            <Select
              value={formData.documentType}
              onValueChange={value => handleInputChange('documentType', value)}
            >
              <SelectTrigger
                className={errors.documentType ? 'border-red-500' : ''}
              >
                <SelectValue placeholder='Select document type' />
              </SelectTrigger>
              <SelectContent>
                {documentTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.documentType && (
              <p className='text-sm text-red-600'>{errors.documentType}</p>
            )}
          </div>

          {/* Icon Picker */}
          <div className='space-y-2'>
            <Label htmlFor='icon'>Template Icon</Label>
            <Select
              value={formData.icon}
              onValueChange={value => handleInputChange('icon', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder='Select an icon' />
              </SelectTrigger>
              <SelectContent>
                {availableIcons.map(iconOption => {
                  const IconComponent = iconOption.icon;
                  return (
                    <SelectItem key={iconOption.value} value={iconOption.value}>
                      <div className='flex items-center space-x-2'>
                        <IconComponent className='h-4 w-4' />
                        <span>{iconOption.label}</span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Status */}
          <div className='space-y-2'>
            <Label htmlFor='status'>Status</Label>
            <Select
              value={formData.status}
              onValueChange={value => handleInputChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder='Select status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='Draft'>Draft</SelectItem>
                <SelectItem value='Active'>Active</SelectItem>
                <SelectItem value='Archived'>Archived</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Action Buttons */}
          <div className='flex justify-end space-x-3 pt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={saving}>
              {saving
                ? 'Saving...'
                : isEditing
                  ? 'Save Changes'
                  : 'Create Template'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
