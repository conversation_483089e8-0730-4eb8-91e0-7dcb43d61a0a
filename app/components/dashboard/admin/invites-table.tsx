'use client';

import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Mail, X } from 'lucide-react';
import { toast } from 'sonner';
import { DataTable } from '@/components/ui/data-table/data-table';
import { DataTableColumnHeader } from '@/components/ui/data-table/data-table-column-header';
import type { DataTableConfig } from '@/components/ui/data-table/data-table';
import { useInvites, type Invite } from '@/hooks/useInvites';

const tableConfig: DataTableConfig = {
  searchColumn: 'email',
  searchPlaceholder: 'Filter invites...',
  filters: [
    {
      id: 'status',
      title: 'Status',
      options: [
        { value: 'pending', label: 'Pending' },
        { value: 'accepted', label: 'Accepted' },
        { value: 'expired', label: 'Expired' },
        { value: 'cancelled', label: 'Cancelled' },
      ],
    },
    {
      id: 'role',
      title: 'Role',
      options: [
        { value: 'WelonTrust', label: 'Welon Trust' },
      ],
    },
  ],
  enableColumnVisibility: true,
  enablePagination: true,
  enableRowSelection: false,
  defaultPageSize: 10,
};

interface InvitesTableProps {
  className?: string;
}

export function InvitesTable({ className = '' }: InvitesTableProps) {
  const { invites, loading, error, cancelInvite, resendInvite } = useInvites();

  const handleCancelInvite = async (invite: Invite) => {
    try {
      await cancelInvite(invite.id);
      toast.success(`Invitation to ${invite.email} has been cancelled`);
    } catch (err) {
      console.error('Failed to cancel invite:', err);
      toast.error('Failed to cancel invitation', {
        description: err instanceof Error ? err.message : 'An unexpected error occurred.',
      });
    }
  };

  const handleResendInvite = async (invite: Invite) => {
    try {
      await resendInvite(invite);
      toast.success(`Invitation resent to ${invite.email}`);
    } catch (err) {
      console.error('Failed to resend invite:', err);
      toast.error('Failed to resend invitation', {
        description: err instanceof Error ? err.message : 'An unexpected error occurred.',
      });
    }
  };

  const columns: ColumnDef<Invite>[] = [
    {
      accessorKey: 'email',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Email' />
      ),
      cell: ({ row }) => (
        <span className='font-medium'>{row.getValue('email')}</span>
      ),
    },
    {
      accessorKey: 'role',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Role' />
      ),
      cell: ({ row }) => <span>{row.getValue('role')}</span>,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: 'invitedByEmail',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Invited By' />
      ),
      cell: ({ row }) => (
        <span className='text-sm text-muted-foreground'>
          {row.getValue('invitedByEmail')}
        </span>
      ),
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Status' />
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        const invite = row.original;
        
        // Check if expired
        const isExpired = new Date(invite.expiresAt) < new Date();
        const displayStatus = isExpired && status === 'pending' ? 'expired' : status;
        
        if (displayStatus === 'accepted') {
          return (
            <Badge className='bg-green-600 text-white hover:bg-green-700'>
              Accepted
            </Badge>
          );
        } else if (displayStatus === 'cancelled') {
          return (
            <Badge variant='outline' className='border-red-500 text-red-600'>
              Cancelled
            </Badge>
          );
        } else if (displayStatus === 'expired') {
          return (
            <Badge variant='outline' className='border-orange-500 text-orange-600'>
              Expired
            </Badge>
          );
        } else {
          return (
            <Badge variant='outline' className='border-blue-500 text-blue-600'>
              Pending
            </Badge>
          );
        }
      },
      filterFn: (row, id, value) => {
        const status = row.getValue(id) as string;
        const invite = row.original;
        const isExpired = new Date(invite.expiresAt) < new Date();
        const displayStatus = isExpired && status === 'pending' ? 'expired' : status;
        return value.includes(displayStatus);
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Created' />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue('createdAt'));
        return (
          <span className='text-sm text-muted-foreground'>
            {date.toLocaleDateString()}
          </span>
        );
      },
    },
    {
      accessorKey: 'expiresAt',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title='Expires' />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue('expiresAt'));
        const isExpired = date < new Date();
        return (
          <span className={`text-sm ${isExpired ? 'text-red-600' : 'text-muted-foreground'}`}>
            {date.toLocaleDateString()}
          </span>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const invite = row.original;
        const isExpired = new Date(invite.expiresAt) < new Date();
        const canResend = invite.status === 'pending' && !isExpired;
        const canCancel = invite.status === 'pending';

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <span className='sr-only'>Open menu</span>
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              {canResend && (
                <DropdownMenuItem onClick={() => handleResendInvite(invite)}>
                  <Mail className='mr-2 h-4 w-4' />
                  Resend
                </DropdownMenuItem>
              )}
              {canCancel && (
                <DropdownMenuItem
                  onClick={() => handleCancelInvite(invite)}
                  className='text-red-600'
                >
                  <X className='mr-2 h-4 w-4' />
                  Cancel
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>Invitations</h2>
          <p className='text-muted-foreground'>
            Manage user invitations and their status
          </p>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={invites}
        config={tableConfig}
        loading={loading}
        error={error}
      />
    </div>
  );
}
