'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  Home,
  FileText,
  Shield,
  AlertTriangle,
  BookOpen,
  Settings,
  Users,
  GraduationCap,
  Share,
  Upload,
  Bell,
  MessageSquare,
  CheckSquare,
  FileCheck,
  Package,
  UserPlus,
} from 'lucide-react';
import { useRole } from '@/lib/roles/role-context';
import routes from '@/utils/routes';

interface SidebarProps {
  userRole?: 'Member' | 'Administrator' | 'Welon Trust' | 'Professional';
}

export function Sidebar({ userRole = 'Member' }: SidebarProps) {
  const pathname = usePathname();
  const { userContext } = useRole();

  // Define navigation items based on user role and context
  const getNavItems = () => {
    // For linked accounts, show limited navigation
    if (userContext?.role === 'linked_account') {
      return [
        {
          title: 'Dashboard',
          href: '/linked',
          icon: <Home className='h-5 w-5' />,
          description: 'Linked account overview',
        },
        {
          title: 'Notifications',
          href: '/member/notifications',
          icon: <Bell className='h-5 w-5' />,
          description: 'View notifications',
        },
        {
          title: 'Shared Documents',
          href: '/dashboard/member/shared-documents',
          icon: <Share className='h-5 w-5' />,
          description: 'View shared documents',
        },
        {
          title: 'Emergency Access',
          href: '/emergency/documents',
          icon: <Shield className='h-5 w-5' />,
          description: 'Emergency document access',
        },
        {
          title: 'Emergency Contacts',
          href: '/dashboard/member/emergency-contacts',
          icon: <Users className='h-5 w-5' />,
          description: 'View emergency contacts',
        },
      ];
    }

    if (userRole === 'Member') {
      return [
        {
          title: 'Dashboard',
          href: routes.member.dashboard,
          icon: <Home className='h-5 w-5' />,
          description: 'Overview',
        },
        {
          title: 'Documents',
          href: routes.member.documents,
          icon: <FileText className='h-5 w-5' />,
          description: 'Manage, review & sign documents',
        },
        {
          title: 'Living Documents',
          href: routes.member.livingDocuments,
          icon: <FileCheck className='h-5 w-5' />,
          description: 'Manage, review & sign living documents',
        },
        {
          title: 'Emergency Contacts',
          href: routes.member.emergencyContacts,
          icon: <Shield className='h-5 w-5' />,
          description: 'Emergency contacts',
        },
        {
          title: "Dead Man's Switch",
          href: routes.member.deadMansSwitch,
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'Automated notifications',
        },
        {
          title: 'Educational Content',
          href: routes.member.educationalContent,
          icon: <GraduationCap className='h-5 w-5' />,
          description: 'Videos, articles & guides',
        },
        {
          title: 'Shipping & Tracking',
          href: '/dashboard/member/shipping',
          icon: <Package className='h-5 w-5' />,
          description: 'Save address & track packages',
        },
        {
          title: 'Settings',
          href: routes.member.settings,
          icon: <Settings className='h-5 w-5' />,
          description: 'Account settings',
        },
      ];
    }

    if (userRole === 'Administrator') {
      return [
        {
          title: 'Dashboard',
          href: routes.admin.dashboard,
          icon: <Home className='h-5 w-5' />,
          description: 'Admin overview',
        },
        {
          title: 'Users',
          href: routes.admin.users,
          icon: <Users className='h-5 w-5' />,
          description: 'User management',
        },
        {
          title: 'Invitations',
          href: '/admin/invites',
          icon: <UserPlus className='h-5 w-5' />,
          description: 'Manage invitations',
        },
        {
          title: 'Content Management',
          href: routes.admin.content,
          icon: <BookOpen className='h-5 w-5' />,
          description: 'Educational content',
        },
        {
          title: 'Emergency Management',
          href: routes.admin.emergency,
          icon: <AlertTriangle className='h-5 w-5' />,
          description: 'DMS & emergency access',
        },
        {
          title: 'Templates',
          href: routes.admin.templates,
          icon: <FileText className='h-5 w-5' />,
          description: 'Document templates',
        },
        {
          title: 'Interview Builder',
          href: routes.admin.interviewBuilder,
          icon: <MessageSquare className='h-5 w-5' />,
          description: 'Create & manage interviews',
        },
        {
          title: 'Living Documents Builder',
          href: routes.admin.livingDocumentsBuilder,
          icon: <CheckSquare className='h-5 w-5' />,
          description: 'Living Documents Builder',
        },
        {
          title: 'Settings',
          href: routes.admin.settings,
          icon: <Settings className='h-5 w-5' />,
          description: 'System settings',
        },
      ];
    }

    if (userRole === 'Welon Trust') {
      return [
        {
          title: 'Dashboard',
          href: routes.welon.dashboard,
          icon: <Home className='h-5 w-5' />,
          description: 'Welon Trust overview',
        },
        {
          title: 'Document Management',
          href: routes.welon.documents,
          icon: <FileText className='h-5 w-5' />,
          description: 'View & manage all documents',
        },
        {
          title: 'Document Upload',
          href: routes.welon.uploadDocuments,
          icon: <Upload className='h-5 w-5' />,
          description: 'Upload signed documents',
        },
        {
          title: 'Submit Evidence',
          href: routes.welon.submitEvidence,
          icon: <Shield className='h-5 w-5' />,
          description: 'Evidence submission',
        },
        {
          title: 'Shipping & Tracking',
          href: routes.welon.shipping,
          icon: <Package className='h-5 w-5' />,
          description: 'Save address & track packages',
        },
        {
          title: 'Settings',
          href: routes.welon.settings,
          icon: <Settings className='h-5 w-5' />,
          description: 'Account settings',
        },
      ];
    }

    return [];
  };

  const navItems = getNavItems();

  const getActiveItemHref = () => {
    if (pathname.includes('/dashboard/member/documents')) {
      const documentsItem = navItems.find(item => item.title === 'Documents');
      if (documentsItem) return documentsItem.href;
    }

    const matchingItems = navItems.filter(
      item => pathname === item.href || pathname.startsWith(`${item.href}/`)
    );

    if (matchingItems.length === 0) return null;
    if (matchingItems.length === 1) return matchingItems[0].href;

    const bestMatch = matchingItems.reduce((longest, current) =>
      current.href.length > longest.href.length ? current : longest
    );

    return bestMatch.href;
  };

  const activeItemHref = getActiveItemHref();

  // Get display title based on role context
  const getDisplayTitle = () => {
    if (userContext?.role === 'linked_account') {
      return `${userContext.displayName}`;
    }
    if (userRole === 'Administrator') {
      return '';
    }
    return `${userRole} Dashboard`;
  };

  return (
    <aside className='w-64 bg-background border-r border-gray-200 min-h-screen shadow-sm'>
      <div className='p-6'>
        {getDisplayTitle() && (
          <h2 className='text-lg font-semibold mb-6'>{getDisplayTitle()}</h2>
        )}

        <nav className='space-y-2'>
          {navItems.map(item => {
            const isActive = activeItemHref === item.href;

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors group',
                  isActive
                    ? 'bg-[var(--eggplant)]/10 text-[var(--eggplant)] border-l-4 border-[var(--eggplant)]'
                    : 'hover:bg-gray-100 hover:text-[var(--eggplant)]'
                )}
              >
                <span
                  className={cn(
                    'mr-3',
                    isActive
                      ? 'text-[var(--eggplant)]'
                      : 'text-[var(--custom-gray-dark)] group-hover:text-[var(--eggplant)]'
                  )}
                >
                  {item.icon}
                </span>
                <div className='flex-1'>
                  <div className='font-medium'>{item.title}</div>
                  <div
                    className={cn(
                      'text-xs mt-0.5',
                      isActive
                        ? 'text-[var(--eggplant)]/70'
                        : 'text-[var(--custom-gray-dark)] group-hover:text-[var(--eggplant)]/70'
                    )}
                  >
                    {item.description}
                  </div>
                </div>
              </Link>
            );
          })}
        </nav>
      </div>
    </aside>
  );
}
