import React, { FC } from 'react';
import { Document } from '@/types/documents';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  FileText,
  PenTool,
  Truck,
  XCircle,
} from 'lucide-react';
import { Badge } from '@workspace/ui/badge';

type DocumentStatusBadgeProps = {
  status: Document['status'];
};

type DocumentStatusMessageProps = {
  status: Document['status'];
};

const getStatusIcon = (status: Document['status']) => {
  switch (status) {
    case 'draft':
      return <FileText className='h-4 w-4' />;
    case 'ready_for_review':
      return <Eye className='h-4 w-4' />;
    case 'under_review':
      return <Clock className='h-4 w-4' />;
    case 'ready_for_signing':
      return <PenTool className='h-4 w-4' />;
    case 'signed':
      return <CheckCircle className='h-4 w-4' />;
    case 'shipped':
      return <Truck className='h-4 w-4' />;
    case 'received':
      return <CheckCircle className='h-4 w-4' />;
    case 'approved':
      return <CheckCircle className='h-4 w-4' />;
    case 'rejected':
      return <XCircle className='h-4 w-4' />;
    default:
      return <AlertCircle className='h-4 w-4' />;
  }
};

const getStatusColor = (status: Document['status']) => {
  switch (status) {
    case 'draft':
      return 'bg-gray-100 text-[var(--custom-gray-dark)]';
    case 'ready_for_review':
      return 'bg-blue-100 text-blue-800';
    case 'under_review':
      return 'bg-yellow-100 text-yellow-800';
    case 'ready_for_signing':
      return 'bg-green-100 text-green-800';
    case 'signed':
      return 'bg-purple-100 text-purple-800';
    case 'shipped':
      return 'bg-indigo-100 text-indigo-800';
    case 'received':
      return 'bg-teal-100 text-teal-800';
    case 'approved':
      return 'bg-emerald-100 text-emerald-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-[var(--custom-gray-dark)]';
  }
};

const getStatusMessage = (status: Document['status']) => {
  switch (status) {
    case 'draft':
      return 'Document is in draft status';
    case 'ready_for_review':
      return 'Ready for your review';
    case 'under_review':
      return 'Currently under review';
    case 'ready_for_signing':
      return 'Ready for signing';
    case 'signed':
      return 'Document has been signed';
    case 'shipped':
      return 'Shipped to Welon Trust';
    case 'received':
      return 'Received by Welon Trust';
    case 'approved':
      return 'Approved and finalized';
    case 'rejected':
      return 'Rejected - needs correction';
    default:
      return 'Status unknown';
  }
};

const DocumentStatusBadge: FC<DocumentStatusBadgeProps> = ({ status }) => (
  <Badge className={getStatusColor(status)}>
    <div className='flex items-center gap-1'>
      {getStatusIcon(status)}
      {status.replace('_', ' ').toUpperCase()}
    </div>
  </Badge>
);

const DocumentStatusMessage: FC<DocumentStatusMessageProps> = ({ status }) => (
  <div className='flex items-center gap-2 text-sm text-muted-foreground'>
    {getStatusIcon(status)}
    <span>{getStatusMessage(status)}</span>
  </div>
);

export { DocumentStatusMessage, DocumentStatusBadge };
