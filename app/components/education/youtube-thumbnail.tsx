/**
 * YouTubeThumbnail Component
 *
 * A component for displaying YouTube video thumbnails with fallback handling.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Play } from 'lucide-react';

interface YouTubeThumbnailProps {
  videoUrl: string;
  title: string;
  duration?: number;
  className?: string;
  showPlayButton?: boolean;
  onClick?: () => void;
}

export function YouTubeThumbnail({
  videoUrl,
  title,
  duration,
  className = '',
  showPlayButton = true,
  onClick,
}: YouTubeThumbnailProps) {
  const [thumbnailSrc, setThumbnailSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Extract YouTube video ID from URL
  const getYouTubeVideoId = (url: string) => {
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : null;
  };

  // Format duration
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Get thumbnail URLs in order of preference
  const getThumbnailUrls = (videoId: string) => [
    `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
    `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
    `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
    `https://img.youtube.com/vi/${videoId}/default.jpg`,
  ];

  // Try to load thumbnail
  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
    setThumbnailSrc('');

    const videoId = getYouTubeVideoId(videoUrl);
    if (!videoId) {
      setHasError(true);
      setIsLoading(false);
      return;
    }

    const thumbnailUrls = getThumbnailUrls(videoId);
    let currentIndex = 0;

    const tryLoadThumbnail = () => {
      if (currentIndex >= thumbnailUrls.length) {
        setHasError(true);
        setIsLoading(false);
        return;
      }

      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        // Check if image is valid (YouTube returns 120x90 placeholder for invalid videos)
        if (img.width > 120 && img.height > 90) {
          setThumbnailSrc(thumbnailUrls[currentIndex]);
          setHasError(false);
          setIsLoading(false);
        } else {
          currentIndex++;
          tryLoadThumbnail();
        }
      };

      img.onerror = () => {
        currentIndex++;
        tryLoadThumbnail();
      };

      img.src = thumbnailUrls[currentIndex];
    };

    tryLoadThumbnail();
  }, [videoUrl]);

  // Show loading state
  if (isLoading) {
    return (
      <div
        className={`relative bg-muted flex items-center justify-center cursor-pointer ${className}`}
        onClick={onClick}
      >
        <div className='text-center p-4'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2'></div>
          <p className='text-sm text-muted-foreground'>Loading...</p>
        </div>
        {duration && (
          <div className='absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded'>
            {formatDuration(duration)}
          </div>
        )}
      </div>
    );
  }

  // Show error state
  if (hasError || !thumbnailSrc) {
    return (
      <div
        className={`relative bg-muted flex items-center justify-center cursor-pointer ${className}`}
        onClick={onClick}
      >
        <div className='text-center p-4'>
          <Play className='h-12 w-12 text-muted-foreground mx-auto mb-2' />
          <p className='text-sm text-muted-foreground'>Video Thumbnail</p>
          <p className='text-xs text-muted-foreground'>{title}</p>
        </div>
        {duration && (
          <div className='absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded'>
            {formatDuration(duration)}
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      className={`relative cursor-pointer overflow-hidden ${className}`}
      onClick={onClick}
    >
      {thumbnailSrc && (
        <img
          src={thumbnailSrc}
          alt={title}
          className='w-full h-full object-cover'
          loading='lazy'
        />
      )}

      {showPlayButton && (
        <div className='absolute inset-0 flex items-center justify-center'>
          <div className='bg-white bg-opacity-90 rounded-full p-3 shadow-lg'>
            <Play className='h-8 w-8 text-black fill-black' />
          </div>
        </div>
      )}
      {duration && (
        <div className='absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded'>
          {formatDuration(duration)}
        </div>
      )}
    </div>
  );
}
