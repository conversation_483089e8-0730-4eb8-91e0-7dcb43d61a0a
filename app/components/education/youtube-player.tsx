/**
 * YouTubePlayer Component
 *
 * A component for playing YouTube videos with proper embedding and size controls.
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Monitor } from 'lucide-react';

interface YouTubePlayerProps {
  video: any;
  onComplete?: () => void;
  autoPlay?: boolean;
}

type PlayerSize = 'small' | 'medium' | 'large';

export function YouTubePlayer({
  video,
  onComplete,
  autoPlay = false,
}: YouTubePlayerProps) {
  const [playerSize, setPlayerSize] = useState<PlayerSize>('medium');

  // Extract YouTube video ID from URL
  const getYouTubeVideoId = (url: string) => {
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : null;
  };

  // Get YouTube embed URL
  const getYouTubeEmbedUrl = (url: string) => {
    const videoId = getYouTubeVideoId(url);
    if (videoId) {
      const autoplayParam = autoPlay ? '&autoplay=1' : '';
      return `https://www.youtube.com/embed/${videoId}?rel=0${autoplayParam}&modestbranding=1&showinfo=0`;
    }
    return url;
  };

  // Check if URL is a YouTube video
  const isYouTubeVideo = (url: string) => {
    return getYouTubeVideoId(url) !== null;
  };

  // Get container classes based on player size
  const getContainerClasses = () => {
    switch (playerSize) {
      case 'small':
        return 'w-full max-w-md mx-auto';
      case 'medium':
        return 'w-full max-w-2xl mx-auto';
      case 'large':
        return 'w-full max-w-4xl mx-auto';
      default:
        return 'w-full max-w-2xl mx-auto';
    }
  };

  // Get size label
  const getSizeLabel = () => {
    switch (playerSize) {
      case 'small':
        return 'Small';
      case 'medium':
        return 'Medium';
      case 'large':
        return 'Large';
      default:
        return 'Medium';
    }
  };

  // Cycle through sizes
  const cycleSizes = () => {
    const sizes: PlayerSize[] = ['small', 'medium', 'large'];
    const currentIndex = sizes.indexOf(playerSize);
    const nextIndex = (currentIndex + 1) % sizes.length;
    setPlayerSize(sizes[nextIndex]);
  };

  if (!video.contentUrl) {
    return (
      <div className='flex items-center justify-center h-64 bg-muted rounded-lg'>
        <p className='text-muted-foreground'>Video not available</p>
      </div>
    );
  }

  if (isYouTubeVideo(video.contentUrl)) {
    return (
      <div className='flex flex-col space-y-4 w-full'>
        {/* Player Size Controls */}
        <div className='flex items-center justify-between'>
          <div className='space-y-2'>
            <h2 className='text-2xl font-bold'>{video.title}</h2>
            {video.description && (
              <p className='text-muted-foreground'>{video.description}</p>
            )}
          </div>
          <div className='flex items-center space-x-2'>
            <span className='text-sm text-muted-foreground'>
              Size: {getSizeLabel()}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={cycleSizes}
              className='flex items-center space-x-1'
            >
              <Monitor className='h-4 w-4' />
              <span>Resize</span>
            </Button>
          </div>
        </div>

        {/* Video Player */}
        <div className={getContainerClasses()}>
          <div className='relative w-full aspect-video rounded-lg overflow-hidden bg-black'>
            <iframe
              src={getYouTubeEmbedUrl(video.contentUrl)}
              title={video.title}
              className='w-full h-full'
              allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
              allowFullScreen
            />
          </div>
        </div>

        {/* Video Info */}
        <div className='space-y-2'>
          {video.duration && (
            <p className='text-sm text-muted-foreground'>
              Duration: {Math.floor(video.duration / 60)}:
              {(video.duration % 60).toString().padStart(2, '0')}
            </p>
          )}
        </div>
      </div>
    );
  }

  // Fallback for non-YouTube videos
  return (
    <div className='flex flex-col space-y-4 w-full'>
      {/* Player Size Controls */}
      <div className='flex items-center justify-between'>
        <div className='space-y-2'>
          <h2 className='text-2xl font-bold'>{video.title}</h2>
          {video.description && (
            <p className='text-muted-foreground'>{video.description}</p>
          )}
        </div>
        <div className='flex items-center space-x-2'>
          <span className='text-sm text-muted-foreground'>
            Size: {getSizeLabel()}
          </span>
          <Button
            variant='outline'
            size='sm'
            onClick={cycleSizes}
            className='flex items-center space-x-1'
          >
            <Monitor className='h-4 w-4' />
            <span>Resize</span>
          </Button>
        </div>
      </div>

      {/* Video Player */}
      <div className={getContainerClasses()}>
        <div className='relative rounded-lg overflow-hidden bg-black'>
          <video
            src={video.contentUrl}
            className='w-full'
            poster={video.thumbnailUrl}
            controls
            autoPlay={autoPlay}
          />
        </div>
      </div>
    </div>
  );
}
