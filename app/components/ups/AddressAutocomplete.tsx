'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, Loader2, Check, AlertTriangle, Info } from 'lucide-react';
import { UPSAddress } from '@/app/types/ups';

interface AddressAutocompleteProps {
  label: string;
  address: UPSAddress & { name?: string; phone?: string };
  onChange: (address: UPSAddress & { name?: string; phone?: string }) => void;
  nameAutoComplete?: string;
  addressPrefix?: string; // 'shipping' or empty for billing
  required?: boolean;
  hideNameAndPhone?: boolean; // New prop to hide name and phone fields
}

interface ZipCodeInfo {
  city: string;
  state: string;
  stateCode: string;
  county?: string;
  timezone?: string;
}

export default function AddressAutocomplete({
  label,
  address,
  onChange,
  addressPrefix = '',
  required = false,
}: AddressAutocompleteProps) {
  const [isLookingUp, setIsLookingUp] = useState(false);
  const [zipSuggestions, setZipSuggestions] = useState<ZipCodeInfo[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Simple ZIP code lookup
  const lookupZipCode = useCallback(async (zipCode: string) => {
    if (zipCode.length !== 5 || !/^\d{5}$/.test(zipCode)) {
      return;
    }

    setIsLookingUp(true);
    try {
      // Basic ZIP code suggestions for common cities
      const commonZips: Record<string, ZipCodeInfo> = {
        '90210': {
          city: 'Beverly Hills',
          state: 'California',
          stateCode: 'CA',
        },
        '10001': { city: 'New York', state: 'New York', stateCode: 'NY' },
        '60601': { city: 'Chicago', state: 'Illinois', stateCode: 'IL' },
        '77001': { city: 'Houston', state: 'Texas', stateCode: 'TX' },
        '33101': { city: 'Miami', state: 'Florida', stateCode: 'FL' },
        '94102': {
          city: 'San Francisco',
          state: 'California',
          stateCode: 'CA',
        },
        '02101': { city: 'Boston', state: 'Massachusetts', stateCode: 'MA' },
        '98101': { city: 'Seattle', state: 'Washington', stateCode: 'WA' },
        '30301': { city: 'Atlanta', state: 'Georgia', stateCode: 'GA' },
        '80201': { city: 'Denver', state: 'Colorado', stateCode: 'CO' },
      };

      const zipInfo = commonZips[zipCode];
      if (zipInfo) {
        setZipSuggestions([zipInfo]);
        setShowSuggestions(true);
      } else {
        setZipSuggestions([]);
        setShowSuggestions(false);
      }
    } catch (error) {
      console.error('ZIP code lookup failed:', error);
    } finally {
      setIsLookingUp(false);
    }
  }, []);

  // Auto-lookup when ZIP code is entered
  useEffect(() => {
    if (address.postalCode && address.postalCode.length === 5) {
      lookupZipCode(address.postalCode);
    } else {
      setShowSuggestions(false);
    }
  }, [address.postalCode, lookupZipCode]);

  const handleZipSuggestionClick = (suggestion: ZipCodeInfo) => {
    onChange({
      ...address,
      city: suggestion.city,
      stateProvinceCode: suggestion.stateCode,
    });
    setShowSuggestions(false);
  };

  const getAutoCompleteValue = (field: string) => {
    const prefix = addressPrefix ? `${addressPrefix} ` : '';
    return `${prefix}${field}`;
  };

  return (
    <div className='space-y-4'>
      <h3 className='text-lg font-medium'>{label}</h3>

      {/* Address Lines */}
      <div>
        <Label htmlFor={`${addressPrefix}-address1`}>
          Address Line 1 {required && '*'}
        </Label>
        <Input
          id={`${addressPrefix}-address1`}
          value={address.addressLine1}
          onChange={e => onChange({ ...address, addressLine1: e.target.value })}
          placeholder='Street address'
          autoComplete={getAutoCompleteValue('address-line1')}
          required={required}
        />
      </div>

      <div>
        <Label htmlFor={`${addressPrefix}-address2`}>Address Line 2</Label>
        <Input
          id={`${addressPrefix}-address2`}
          value={address.addressLine2 || ''}
          onChange={e => onChange({ ...address, addressLine2: e.target.value })}
          placeholder='Apartment, suite, etc.'
          autoComplete={getAutoCompleteValue('address-line2')}
        />
      </div>

      {/* City, State, ZIP with smart suggestions */}
      <div className='grid grid-cols-3 gap-4'>
        <div>
          <Label htmlFor={`${addressPrefix}-city`}>
            City {required && '*'}
          </Label>
          <Input
            id={`${addressPrefix}-city`}
            value={address.city}
            onChange={e => onChange({ ...address, city: e.target.value })}
            placeholder='City'
            autoComplete={getAutoCompleteValue('address-level2')}
            required={required}
          />
        </div>
        <div>
          <Label htmlFor={`${addressPrefix}-state`}>
            State {required && '*'}
          </Label>
          <Input
            id={`${addressPrefix}-state`}
            value={address.stateProvinceCode}
            onChange={e =>
              onChange({
                ...address,
                stateProvinceCode: e.target.value.toUpperCase(),
              })
            }
            placeholder='CA'
            maxLength={2}
            autoComplete={getAutoCompleteValue('address-level1')}
            required={required}
          />
        </div>
        <div className='relative'>
          <Label htmlFor={`${addressPrefix}-zip`}>
            ZIP Code {required && '*'}
          </Label>
          <div className='relative'>
            <Input
              id={`${addressPrefix}-zip`}
              value={address.postalCode}
              onChange={e =>
                onChange({ ...address, postalCode: e.target.value })
              }
              placeholder='12345'
              autoComplete={getAutoCompleteValue('postal-code')}
              required={required}
            />
            {isLookingUp && (
              <div className='absolute right-3 top-1/2 transform -translate-y-1/2'>
                <Loader2 className='h-4 w-4 animate-spin text-muted-foreground' />
              </div>
            )}
          </div>

          {/* ZIP Code Suggestions */}
          {showSuggestions && zipSuggestions.length > 0 && (
            <Card className='absolute z-10 w-full mt-1 shadow-lg'>
              <CardContent className='p-2'>
                {zipSuggestions.map((suggestion, index) => (
                  <Button
                    key={index}
                    variant='ghost'
                    className='w-full justify-start text-left h-auto p-2'
                    onClick={() => handleZipSuggestionClick(suggestion)}
                  >
                    <div className='flex items-center gap-2'>
                      <MapPin className='h-4 w-4 text-muted-foreground' />
                      <div>
                        <div className='font-medium'>
                          {suggestion.city}, {suggestion.stateCode}
                        </div>
                        <div className='text-sm text-muted-foreground'>
                          {suggestion.state}
                        </div>
                      </div>
                      <Check className='h-4 w-4 ml-auto text-green-600' />
                    </div>
                  </Button>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Country (hidden for US, but kept for data structure) */}
      <input
        type='hidden'
        value={address.countryCode}
        onChange={e => onChange({ ...address, countryCode: e.target.value })}
      />
    </div>
  );
}
