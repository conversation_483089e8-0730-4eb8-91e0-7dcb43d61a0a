'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Eye,
  EyeOff,
  Mail,
  Lock,
  User,
  Phone,
  MapPin,
  Loader2,
} from 'lucide-react';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { usaStates } from '@/app/utils/states';
import PasswordStrengthIndicator from './PasswordStrengthIndicator';
import PasswordRequirements from './PasswordRequirements';
import { checkEmailExists } from '@/app/utils/emailValidation';
import { customSignUp } from '@/app/utils/customSignUp';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { useModal } from '@/hooks/use-modal';

// Map our component steps to the onboarding steps enum
type SignUpStep = 'credentials' | 'personalInfo' | 'confirmation';

interface SignUpFormProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
  confirmPassword: string;
  setConfirmPassword: (confirmPassword: string) => void;
  code: string;
  setCode: (code: string) => void;
  showPassword: boolean;
  togglePasswordVisibility: () => void;
  error: string;
  setError: (error: string) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  onComplete: (step: 'confirmSignUp') => void;
  // Invite-related props
  inviteData?: {
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    birthdate?: string;
    state?: string;
    inviteToken?: string;
    role?: string;
  };
}

export default function SignUpForm({
  email,
  setEmail,
  password,
  setPassword,
  confirmPassword,
  setConfirmPassword,
  code,
  setCode,
  showPassword,
  togglePasswordVisibility,
  error,
  setError,
  loading,
  setLoading,
  onComplete,
  inviteData,
}: SignUpFormProps) {
  const [currentStep, setCurrentStep] = useState<SignUpStep>('credentials');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [state, setState] = useState('');
  const [birthdayDate, setBirthdayDate] = useState<Date | undefined>(undefined);
  const [phone, setPhone] = useState('');
  const [emailChecking, setEmailChecking] = useState(false);
  const [inviteToken, setInviteToken] = useState<string | null>(null);

  // Modal for age restriction
  const ageRestrictionModal = useModal();

  // Pre-fill form with invite data if available
  useEffect(() => {
    if (inviteData) {
      setFirstName(inviteData?.firstName || '');
      setLastName(inviteData.lastName || '');
      setPhone(inviteData.phoneNumber || '');
      setState(inviteData.state || '');
      setBirthdayDate(
        inviteData.birthdate ? new Date(inviteData.birthdate) : undefined
      );
      setInviteToken(inviteData.inviteToken || null);

      // Skip to credentials step since personal info is already filled
      setCurrentStep('credentials');
    }
  }, [inviteData]);

  // Validation functions for each step
  const isCredentialsStepValid = () => {
    if (!email || !password || !confirmPassword) return false;

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return false;

    // Password strength validation
    const hasMinLength = password.length >= 12;
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[^A-Za-z0-9]/.test(password);
    const isPasswordStrong =
      hasMinLength &&
      hasUppercase &&
      hasLowercase &&
      hasNumber &&
      hasSpecialChar;

    if (!isPasswordStrong) return false;
    if (password !== confirmPassword) return false;

    return true;
  };

  const isPersonalInfoStepValid = () => {
    if (!firstName || !lastName || !state || !birthdayDate || !phone)
      return false;

    // Validate age (must be at least 18 years old)
    const age = calculateAge(birthdayDate);
    if (age < 18) return false;

    // Validate phone number length (8-15 digits)
    const phoneDigits = phone.replace(/\D/g, '');
    if (phoneDigits.length < 8 || phoneDigits.length > 15) return false;

    return true;
  };

  // Function to calculate age from birthday
  const calculateAge = (birthDate: Date): number => {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  // Function to get the maximum allowed birth date (18 years ago from today)
  const getMaxBirthDate = (): Date => {
    const today = new Date();
    const maxDate = new Date(today);
    maxDate.setFullYear(today.getFullYear() - 18);
    return maxDate;
  };

  // Function to get the maximum allowed birth year
  const getMaxBirthYear = (): number => {
    const today = new Date();
    return today.getFullYear() - 18;
  };

  const handleEmailChange = (newEmail: string) => {
    setEmail(newEmail);
    // Clear any existing form errors when user starts typing
    if (error && error.includes('email')) {
      setError('');
    }
  };

  const handlePasswordChange = (newPassword: string) => {
    setPassword(newPassword);
    // Clear any existing form errors when user starts typing
    if (error && (error.includes('password') || error.includes('Password'))) {
      setError('');
    }
  };

  const handleConfirmPasswordChange = (newConfirmPassword: string) => {
    setConfirmPassword(newConfirmPassword);
    // Clear any existing form errors when user starts typing
    if (
      error &&
      (error.includes('password') ||
        error.includes('Password') ||
        error.includes('match'))
    ) {
      setError('');
    }
  };

  const handleFieldChange = (setter: (value: any) => void, value: any) => {
    setter(value);
    // Clear any existing form errors when user starts typing
    if (error) {
      setError('');
    }
  };

  const handleNextStep = async () => {
    if (currentStep === 'credentials') {
      if (!email || !password || !confirmPassword) {
        setError('Please fill in all fields');
        return;
      }

      // Simple email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        setError('Please enter a valid email address');
        return;
      }

      // Skip email checking for invites (email is pre-validated)
      if (!inviteData) {
        // Check if email already exists
        setEmailChecking(true);
        setError('');

        try {
          const emailCheck = await checkEmailExists(email);

          if (emailCheck.error) {
            setError(`Error checking email: ${emailCheck.error}`);
            setEmailChecking(false);
            return;
          }

          if (emailCheck.exists) {
            setError(
              'This email is already registered. Please use a different email or try logging in.'
            );
            setEmailChecking(false);
            return;
          }
        } catch (error) {
          setError('Failed to verify email availability. Please try again.');
          setEmailChecking(false);
          return;
        }

        setEmailChecking(false);
      }

      // Password validation
      const hasMinLength = password.length >= 12;
      const hasUppercase = /[A-Z]/.test(password);
      const hasLowercase = /[a-z]/.test(password);
      const hasNumber = /[0-9]/.test(password);
      const hasSpecialChar = /[^A-Za-z0-9]/.test(password);
      const isPasswordStrong =
        hasMinLength &&
        hasUppercase &&
        hasLowercase &&
        hasNumber &&
        hasSpecialChar;

      if (!isPasswordStrong) {
        setError('Password does not meet the strength requirements');
        return;
      }

      if (password !== confirmPassword) {
        setError('Passwords do not match');
        return;
      }

      setError('');
      setCurrentStep('personalInfo');
    } else if (currentStep === 'personalInfo') {
      if (!firstName || !lastName || !state || !birthdayDate) {
        setError('Please fill in all required fields');
        return;
      }

      // Validate age (must be at least 18 years old)
      const age = calculateAge(birthdayDate);
      if (age < 18) {
        setError('You must be at least 18 years old to register');
        return;
      }

      // Check if user is at least 65 years old for service eligibility
      if (age < 65) {
        ageRestrictionModal.open();
        return;
      }

      // Validate phone number length (8-15 digits)
      const phoneDigits = phone.replace(/\D/g, '');
      if (phoneDigits.length < 8 || phoneDigits.length > 15) {
        setError('Phone number must be between 8 and 15 digits');
        return;
      }

      setError('');
      handleSignUp();
    }
  };

  const handlePreviousStep = () => {
    if (currentStep === 'personalInfo') {
      setCurrentStep('credentials');
    } else if (currentStep === 'confirmation') {
      setCurrentStep('personalInfo');
    }
  };

  const handleSignUp = async () => {
    setLoading(true);
    setError('');

    try {
      // Get the state label from the selected state code
      const selectedState = usaStates.find(s => s.value === state);
      const stateLabel = selectedState ? selectedState.label : state;

      const result = await customSignUp({
        email,
        password,
        firstName,
        lastName,
        state: stateLabel,
        birthdayDate: birthdayDate!,
        phone,
        inviteToken: inviteToken || undefined,
      });

      if (result.success) {
        // Show success message and redirect to login
        setError('');
        // You could show a success message here or redirect
        onComplete('confirmSignUp');
      } else {
        setError(result.error || 'Failed to sign up');
      }
    } catch (err) {
      console.error('Error signing up:', err);
      if (typeof err === 'object' && err !== null && 'message' in err) {
        setError((err as { message: string }).message);
      } else {
        setError('Failed to sign up');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='w-full'>
      <div className='mb-4'>
        <div className='relative w-full h-2 bg-background rounded-full overflow-hidden'>
          <div
            className='absolute top-0 left-0 h-full bg-primary transition-all duration-300'
            style={{ width: currentStep === 'credentials' ? '50%' : '100%' }}
          />
        </div>
        <div className='flex justify-between mt-1 text-xs text-muted-foreground'>
          <span>Account</span>
          <span>Personal Info</span>
        </div>
      </div>

      {currentStep === 'credentials' && (
        <div className='space-y-4 pt-4'>
          <div className='space-y-2'>
            <Label htmlFor='signup-email'>Email</Label>
            <div className='relative'>
              <Mail className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
              <Input
                id='signup-email'
                type='email'
                value={email}
                onChange={e => handleEmailChange(e.target.value)}
                className='pl-10'
                placeholder='Enter your email'
                readOnly={!!inviteData}
                required
              />
            </div>
            {inviteData && (
              <p className='text-xs text-muted-foreground'>
                Email is pre-filled from your invitation
              </p>
            )}
          </div>
          <div className='space-y-2'>
            <Label htmlFor='signup-password'>Password</Label>
            <div className='relative'>
              <Lock className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
              <Input
                id='signup-password'
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={e => handlePasswordChange(e.target.value)}
                className='pl-10 pr-10'
                placeholder='Create a password'
                required
              />
              <Button
                type='button'
                variant='ghost'
                size='icon'
                className='absolute right-0 top-0 h-9 w-9'
                onClick={togglePasswordVisibility}
              >
                {showPassword ? (
                  <Eye className='h-4 w-4' />
                ) : (
                  <EyeOff className='h-4 w-4' />
                )}
              </Button>
            </div>
            <div className='space-y-1 mt-2'>
              <PasswordStrengthIndicator password={password} />
              <PasswordRequirements password={password} />
            </div>
          </div>
          <div className='space-y-2'>
            <Label htmlFor='confirm-password'>Confirm Password</Label>
            <div className='relative'>
              <Lock className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
              <Input
                id='confirm-password'
                type={showPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={e => handleConfirmPasswordChange(e.target.value)}
                className='pl-10'
                placeholder='Confirm your password'
                required
              />
            </div>
          </div>
          {error && <p className='text-destructive text-sm'>{error}</p>}
          <Button
            type='button'
            className='w-full'
            onClick={handleNextStep}
            disabled={loading || emailChecking || !isCredentialsStepValid()}
          >
            {emailChecking ? (
              <>
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                Checking email...
              </>
            ) : (
              'Next'
            )}
          </Button>
        </div>
      )}

      {currentStep === 'personalInfo' && (
        <div className='space-y-4 pt-4'>
          <div className='grid grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='first-name'>First Name</Label>
              <div className='relative'>
                <User className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                <Input
                  id='first-name'
                  value={firstName}
                  onChange={e =>
                    handleFieldChange(setFirstName, e.target.value)
                  }
                  className='pl-10'
                  placeholder='First name'
                  required
                />
              </div>
            </div>
            <div className='space-y-2'>
              <Label htmlFor='last-name'>Last Name</Label>
              <div className='relative'>
                <User className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                <Input
                  id='last-name'
                  value={lastName}
                  onChange={e => handleFieldChange(setLastName, e.target.value)}
                  className='pl-10'
                  placeholder='Last name'
                  required
                />
              </div>
            </div>
          </div>
          <div className='space-y-2'>
            <Label htmlFor='state'>State</Label>
            <div className='relative'>
              <MapPin className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground z-10' />
              <Select
                value={state}
                onValueChange={value => handleFieldChange(setState, value)}
              >
                <SelectTrigger id='state' className='pl-10 w-full'>
                  <SelectValue placeholder='Select your state' />
                </SelectTrigger>
                <SelectContent>
                  {usaStates.map(state => (
                    <SelectItem key={state.value} value={state.value}>
                      {state.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className='space-y-2'>
            <Label htmlFor='birthday'>Birthday</Label>
            <DatePicker
              date={birthdayDate}
              setDate={date => handleFieldChange(setBirthdayDate, date)}
              showYearPicker={true}
              showMonthPicker={true}
              yearRange={{ from: 1900, to: getMaxBirthYear() }}
              calendarProps={{
                disabled: (date: Date) => date > getMaxBirthDate(),
              }}
            />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='phone'>Phone Number</Label>
            <div className='relative'>
              <Phone className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
              <Input
                id='phone'
                type='tel'
                value={phone}
                onChange={e => {
                  const value = e.target.value;
                  if (value.length <= 15) {
                    handleFieldChange(setPhone, value);
                  }
                }}
                className='pl-10'
                placeholder='Phone number'
                maxLength={15}
                required
              />
            </div>
            <p className='text-xs text-muted-foreground'>
              Must be between 8-15 digits
            </p>
          </div>
          {error && <p className='text-destructive text-sm'>{error}</p>}
          <div className='flex gap-2'>
            <Button
              type='button'
              variant='outline'
              className='flex-1'
              onClick={handlePreviousStep}
            >
              Back
            </Button>
            <Button
              type='button'
              className='flex-1'
              onClick={handleNextStep}
              disabled={loading || !isPersonalInfoStepValid()}
            >
              {loading ? 'Signing up...' : 'Sign Up'}
            </Button>
          </div>
        </div>
      )}

      {/* Age Restriction Modal */}
      <ConfirmationDialog
        {...ageRestrictionModal.modalProps}
        title='Service Eligibility'
        description='This service is aimed at an older audience. You must be at least 65 years old to register.'
        confirmLabel='I understand'
        onConfirm={() => ageRestrictionModal.close()}
        confirmVariant='default'
      />
    </div>
  );
}
