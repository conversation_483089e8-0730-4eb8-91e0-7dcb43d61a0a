'use client';

import { useState, useEffect } from 'react';
import { confirmSignUp, signIn, autoSignIn } from 'aws-amplify/auth';
import { useRouter, useSearchParams } from 'next/navigation';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, Lock, Mail } from 'lucide-react';
import routes from '@/utils/routes';
import SignUpForm from './SignUpForm';
import { useAuth } from '@/app/context/AuthContext';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';

type FormState = 'signIn' | 'signUp' | 'confirmSignUp' | 'forgotPassword';

export default function CustomLogin() {
  const {
    user,
    loading: isLoadingUser,
    refreshUser,
    onboardingStatus,
    userRoles,
  } = useAuth();

  const router = useRouter();
  const searchParams = useSearchParams();
  const client = generateClient<Schema>();
  const [formState, setFormState] = useState<FormState>('signIn');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [code, setCode] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isAccountLocked, setIsAccountLocked] = useState(false);
  const [lockoutUntil, setLockoutUntil] = useState<string | null>(null);
  const [isCheckingLockout, setIsCheckingLockout] = useState(false);

  // Check for invite parameters
  const inviteEmail = searchParams.get('email');
  const inviteToken = searchParams.get('inviteToken');
  const inviteRole = searchParams.get('role');
  const isInviteRegistration = !!inviteToken;

  // Set email from invite parameters
  useEffect(() => {
    if (inviteEmail && inviteToken) {
      setEmail(inviteEmail);
      setFormState('signUp'); // Switch to signup tab for invites
    }
  }, [inviteEmail, inviteToken]);

  // Check account lockout status when email changes
  useEffect(() => {
    if (email && email.includes('@') && !loading) {
      checkAccountLockout(email);
    }
  }, [email, loading]); // Don't check during loading to prevent interference

  // Check if account is locked before attempting login
  const checkAccountLockout = async (email: string) => {
    if (isCheckingLockout) {
      return isAccountLocked;
    }

    try {
      setIsCheckingLockout(true);
      const result = await client.queries.checkAccountLockout(
        {
          email,
          action: 'check',
        },
        {
          authMode: 'iam',
        }
      );

      if (result.data) {
        // Parse the JSON string response
        let lockoutData;
        try {
          if (typeof result.data === 'string') {
            lockoutData = JSON.parse(result.data);
          } else {
            lockoutData = result.data as any;
          }
        } catch (parseError) {
          return false;
        }

        // Convert string "true"/"false" to boolean if needed
        const isLocked =
          lockoutData.isLocked === true || lockoutData.isLocked === 'true';

        if (isLocked) {
          console.log('🔒 Account IS locked, setting state');
          setIsAccountLocked(true);
          setLockoutUntil(lockoutData.lockoutUntil);
          return true;
        } else {
          console.log('✅ Account is NOT locked');
        }
      }
      setIsAccountLocked(false);
      setLockoutUntil(null);
      return false;
    } catch (error) {
      console.error('❌ Error checking account lockout:', error);
      setIsAccountLocked(false);
      setLockoutUntil(null);
      return false;
    } finally {
      setIsCheckingLockout(false);
    }
  };

  // Track login attempt
  const trackLoginAttempt = async (email: string, success: boolean) => {
    try {
      const authMode = success ? 'userPool' : 'iam';

      const params = {
        email,
        success,
        action: 'track',
        ipAddress: undefined, // Could be enhanced to get real IP
        userAgent: navigator.userAgent,
      };

      const result = await client.mutations.trackLoginAttempt(params, {
        authMode,
      });
    } catch (error) {
      console.error('❌ Error tracking login attempt:', error);
      console.error('❌ Error details:', {
        name: (error as any)?.name,
        message: (error as any)?.message,
        stack: (error as any)?.stack,
      });
      throw error; // Re-throw to see the error in the calling function
    }
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Check if account is locked before attempting login
      const isLocked = await checkAccountLockout(email);

      if (isLocked) {
        setError(
          `Account is locked due to too many failed login attempts. Please try again in 15 minutes.`
        );
        setLoading(false);
        return;
      }

      // Store remember me preference before sign in
      localStorage.setItem('rememberMe', rememberMe.toString());

      const { nextStep: signUpNextStep } = await signIn({
        username: email,
        password: password,
      });

      // Track successful login attempt
      try {
        await trackLoginAttempt(email, true);
      } catch (trackError) {
        console.error('❌ Error tracking successful login:', trackError);
      }

      if (signUpNextStep.signInStep === 'CONFIRM_SIGN_UP') {
        setFormState('confirmSignUp');
      } else if (signUpNextStep.signInStep === 'DONE') {
        await refreshUser();
      }
    } catch (err) {
      console.error('Error signing in:', err);

      // Track failed login attempt
      await trackLoginAttempt(email, false);

      if (typeof err === 'object' && err !== null && 'name' in err) {
        const error = err as { name: string; message?: string };

        if (error.name === 'UserNotConfirmedException') {
          setFormState('confirmSignUp');
        } else {
          setError(error.message || 'Failed to sign in');
        }
      } else {
        setError('Failed to sign in');
      }
      setLoading(false);
    }
  };

  const handleFormStateChange = (newState: FormState) => {
    setFormState(newState);
  };

  const handleConfirmSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const { nextStep: signUpNextStep } = await confirmSignUp({
        username: email,
        confirmationCode: code,
      });

      if (signUpNextStep.signUpStep === 'COMPLETE_AUTO_SIGN_IN') {
        const { nextStep } = await autoSignIn();
        if (nextStep.signInStep === 'DONE') {
          await refreshUser();
          router.push(routes.member.dashboard);
        }
      } else if (signUpNextStep.signUpStep === 'DONE') {
        await signIn({ username: email, password });
        await refreshUser();
        router.push(routes.member.onboarding);
      }

      // Import the updateUserOnboardingStep function
      const { updateUserOnboardingStep, OnboardingStep } = await import(
        '@/app/utils/userOnboarding'
      );

      try {
        // Update the user's onboarding step to COMPLETED
        // This will create the initial onboarding record for the user
        await updateUserOnboardingStep(OnboardingStep.CHILD_STATUS, {
          signupCompleted: true,
          signupDate: new Date().toISOString(),
        });
      } catch (onboardingErr) {
        // Log the error but don't prevent the user from proceeding
        console.error('Error updating onboarding status:', onboardingErr);
      }
      // Auto sign in is enabled, so we can redirect to profile
      router.push(routes.member.onboarding);
    } catch (err) {
      console.error('Error confirming sign up:', err);
      setError('Failed to confirm sign up');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const client = generateClient<Schema>({
        authMode: 'iam',
      });

      // Send password reset email using your custom function
      const emailResult = await client.mutations.sendEmail({
        to: email,
        subject: 'Reset Your Password - Childfree',
        message: `You requested to reset your password. Please click the link below to reset your password.\n\nThis link will expire in 24 hours.`,
        emailType: 'passwordReset',
        isNewAccount: false,
      });

      const emailData = emailResult.data as any;
      let parsedEmailData = emailData;
      if (typeof emailData === 'string') {
        try {
          parsedEmailData = JSON.parse(emailData);
        } catch (e) {
          console.log('Failed to parse email data as JSON:', e);
        }
      }

      if (parsedEmailData?.success) {
        // Show success message and go back to sign in
        setError('');
        setFormState('signIn');
        toast.success('Password reset email sent!', {
          description: 'Please check your email and follow the instructions.',
          duration: 5000,
        });
      } else {
        throw new Error(parsedEmailData?.error || 'Failed to send reset email');
      }
    } catch (err) {
      console.error('Error sending reset email:', err);
      setError('Failed to send reset email');
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  if (formState === 'confirmSignUp') {
    return (
      <Card className='w-full max-w-md mx-auto'>
        <CardHeader>
          <CardTitle>Check Your Email</CardTitle>
          <CardDescription>
            We've sent a verification link to your email address. Please click
            the link to verify your account and complete the registration
            process.
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='text-center'>
            <Mail className='h-12 w-12 text-primary mx-auto mb-4' />
            <p className='text-sm text-muted-foreground mb-4'>
              Verification email sent to: <strong>{email}</strong>
            </p>
            <p className='text-xs text-muted-foreground'>
              The verification link will expire in 24 hours. If you don't see
              the email, please check your spam folder.
            </p>
          </div>
          {error && (
            <p className='text-destructive text-sm text-center'>{error}</p>
          )}
        </CardContent>
        <CardFooter className='flex flex-col gap-4 mt-4'>
          <Button
            type='button'
            variant='outline'
            onClick={() => setFormState('signIn')}
            className='w-full'
          >
            Back to Sign In
          </Button>
        </CardFooter>
      </Card>
    );
  }

  if (formState === 'forgotPassword') {
    return (
      <Card className='w-full max-w-md mx-auto'>
        <CardHeader>
          <CardTitle>Reset Password</CardTitle>
          <CardDescription>
            Enter your email address and we&apos;ll send you a code to reset
            your password.
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleForgotPassword}>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='email'>Email</Label>
              <div className='relative'>
                <Mail className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                <Input
                  id='email'
                  type='email'
                  value={email}
                  onChange={e => {
                    setEmail(e.target.value);
                    if (error) setError('');
                  }}
                  className='pl-10'
                  placeholder='Enter your email'
                  required
                />
              </div>
            </div>
            {error && <p className='text-destructive text-sm'>{error}</p>}
          </CardContent>
          <CardFooter className='flex flex-col gap-4 mt-4'>
            <Button type='submit' className='w-full' disabled={loading}>
              {loading ? 'Sending...' : 'Send Reset Code'}
            </Button>
            <Button
              type='button'
              variant='ghost'
              onClick={() => {
                setError('');
                setFormState('signIn');
              }}
              className='w-full'
            >
              Back to Sign In
            </Button>
          </CardFooter>
        </form>
      </Card>
    );
  }

  return (
    <Card className='w-full max-w-md mx-auto'>
      <CardHeader>
        <CardTitle>
          {isInviteRegistration ? 'Complete Your Invitation' : 'Welcome'}
        </CardTitle>
        <CardDescription>
          {isInviteRegistration
            ? `You've been invited to join as a ${
                inviteRole || 'Member'
              } member. Please set your password to continue.`
            : 'Sign in to your account or create a new one'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs
          defaultValue={isInviteRegistration ? 'signup' : 'signin'}
          className='w-full'
          onValueChange={() => setError('')}
        >
          {!isInviteRegistration && (
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='signin'>Sign In</TabsTrigger>
              <TabsTrigger value='signup'>Sign Up</TabsTrigger>
            </TabsList>
          )}
          {!isInviteRegistration && (
            <TabsContent value='signin'>
              <form onSubmit={handleSignIn} className='space-y-4 pt-4'>
                <div className='space-y-2'>
                  <Label htmlFor='email'>Email</Label>
                  <div className='relative'>
                    <Mail className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                    <Input
                      id='email'
                      type='email'
                      value={email}
                      onChange={e => {
                        setEmail(e.target.value);
                        if (error) setError('');
                      }}
                      className='pl-10'
                      placeholder='Enter your email'
                      required
                    />
                  </div>
                </div>
                <div className='space-y-2'>
                  <div className='flex items-center justify-between'>
                    <Label htmlFor='password'>Password</Label>
                    <Button
                      type='button'
                      variant='link'
                      className='px-0 text-xs'
                      onClick={() => {
                        setError('');
                        setFormState('forgotPassword');
                      }}
                    >
                      Forgot password?
                    </Button>
                  </div>
                  <div className='relative'>
                    <Lock className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                    <Input
                      id='password'
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={e => {
                        setPassword(e.target.value);
                        if (error) setError('');
                      }}
                      className='pl-10 pr-10'
                      placeholder='Enter your password'
                      required
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='icon'
                      className='absolute right-0 top-0 h-9 w-9'
                      onClick={togglePasswordVisibility}
                    >
                      {showPassword ? (
                        <Eye className='h-4 w-4' />
                      ) : (
                        <EyeOff className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                </div>
                {error && <p className='text-destructive text-sm'>{error}</p>}
                {isAccountLocked && lockoutUntil && (
                  <div className='bg-yellow-50 border border-yellow-200 rounded-md p-3'>
                    <p className='text-yellow-800 text-sm'>
                      <strong>Account Locked:</strong> Too many failed login
                      attempts. Account will be unlocked at{' '}
                      {new Date(lockoutUntil).toLocaleTimeString()}.
                    </p>
                  </div>
                )}
                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='remember-me'
                    className={'cursor-pointer'}
                    checked={rememberMe}
                    onCheckedChange={checked =>
                      setRememberMe(checked as boolean)
                    }
                  />
                  <label
                    htmlFor='remember-me'
                    className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer'
                  >
                    Remember Me
                  </label>
                </div>
                <Button
                  type='submit'
                  className='w-full'
                  disabled={loading || isAccountLocked}
                >
                  {loading
                    ? 'Signing in...'
                    : isAccountLocked
                      ? 'Account Locked'
                      : 'Sign In'}
                </Button>
              </form>
            </TabsContent>
          )}
          <TabsContent value='signup'>
            <SignUpForm
              email={email}
              setEmail={setEmail}
              password={password}
              setPassword={setPassword}
              confirmPassword={confirmPassword}
              setConfirmPassword={setConfirmPassword}
              code={code}
              setCode={setCode}
              showPassword={showPassword}
              togglePasswordVisibility={togglePasswordVisibility}
              error={error}
              setError={setError}
              loading={loading}
              setLoading={setLoading}
              onComplete={handleFormStateChange}
              inviteData={
                inviteToken
                  ? {
                      inviteToken: inviteToken,
                      role: inviteRole || 'WelonTrust',
                    }
                  : undefined
              }
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
