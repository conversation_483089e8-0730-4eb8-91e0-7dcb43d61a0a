'use client';

import React, { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import { toast } from 'sonner';
import { ContentType, ContentStatus } from '@/types/education';

// Validation schema
const editContentSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  type: z.nativeEnum(ContentType, {
    required_error: 'Content type is required',
  }),
  status: z.nativeEnum(ContentStatus),
  description: z.string().optional(),
  contentUrl: z.string().min(1, 'Content URL is required'),
  thumbnailUrl: z.string().optional(),
  duration: z.string().optional(),
  readingTime: z.string().optional(),
  tags: z.array(z.string()),
});

type EditContentFormData = z.infer<typeof editContentSchema>;

interface EditContentModalProps {
  contentItem: any;
  isOpen: boolean;
  onClose: () => void;
  onContentUpdated?: () => void;
}

export function EditContentModal({
  contentItem,
  isOpen,
  onClose,
  onContentUpdated,
}: EditContentModalProps) {
  const { updateContent } = useEducationalContent();
  const [newTag, setNewTag] = useState('');

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<EditContentFormData>({
    resolver: zodResolver(editContentSchema),
    defaultValues: {
      title: '',
      type: undefined,
      status: ContentStatus.DRAFT,
      description: '',
      contentUrl: '',
      thumbnailUrl: '',
      duration: '',
      readingTime: '',
      tags: [],
    },
  });

  const watchedType = watch('type');
  const watchedTags = watch('tags');

  // Initialize form data when contentItem changes
  useEffect(() => {
    if (contentItem) {
      reset({
        title: contentItem.title || '',
        type: contentItem.type || undefined,
        status: contentItem.status || ContentStatus.DRAFT,
        description: contentItem.description || '',
        contentUrl: contentItem.contentUrl || '',
        thumbnailUrl: contentItem.thumbnailUrl || '',
        duration: contentItem.duration?.toString() || '',
        readingTime: contentItem.readingTime?.toString() || '',
        tags: contentItem.tags || [],
      });
    }
  }, [contentItem, reset]);

  const onSubmit = async (data: EditContentFormData) => {
    try {
      const updateData = {
        title: data.title,
        type: data.type,
        status: data.status,
        description: data.description,
        contentUrl: data.contentUrl,
        thumbnailUrl: data.thumbnailUrl,
        tags: data.tags,
        ...(data.duration && { duration: parseInt(data.duration) }),
        ...(data.readingTime && {
          readingTime: parseInt(data.readingTime),
        }),
      };

      await updateContent(contentItem.id, updateData);

      onClose();

      if (onContentUpdated) {
        onContentUpdated();
      }

      toast.success('Content updated successfully');
    } catch (error) {
      console.error('Error updating content:', error);
      toast.error('Failed to update educational content');
    }
  };

  const addTag = () => {
    if (newTag.trim() && !watchedTags.includes(newTag.trim())) {
      setValue('tags', [...watchedTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setValue(
      'tags',
      watchedTags.filter(tag => tag !== tagToRemove)
    );
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle>Edit Educational Content</DialogTitle>
          <DialogDescription>
            Update the educational content information.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
          {/* Title */}
          <div className='space-y-2'>
            <Label htmlFor='title'>Title *</Label>
            <Controller
              name='title'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id='title'
                  placeholder='Enter content title'
                  className={errors.title ? 'border-red-500' : ''}
                />
              )}
            />
            {errors.title && (
              <p className='text-sm text-red-500'>{errors.title.message}</p>
            )}
          </div>

          {/* Type and Status */}
          <div className='grid grid-cols-2 gap-4'>
            <div className='space-y-2'>
              <Label htmlFor='type'>Type *</Label>
              <Controller
                name='type'
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger
                      className={errors.type ? 'border-red-500' : ''}
                    >
                      <SelectValue placeholder='Select content type' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ContentType.VIDEO}>Video</SelectItem>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SelectItem
                              value={ContentType.ARTICLE}
                              disabled
                              className='opacity-50'
                            >
                              Article (In development)
                            </SelectItem>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>In development</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SelectItem
                              value={ContentType.INFOGRAPHIC}
                              disabled
                              className='opacity-50'
                            >
                              Infographic (In development)
                            </SelectItem>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>In development</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SelectItem
                              value={ContentType.AVATAR}
                              disabled
                              className='opacity-50'
                            >
                              Interactive Guide (In development)
                            </SelectItem>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>In development</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <SelectItem
                              value={ContentType.TOOLTIP}
                              disabled
                              className='opacity-50'
                            >
                              Quick Tip (In development)
                            </SelectItem>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>In development</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.type && (
                <p className='text-sm text-red-500'>{errors.type.message}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='status'>Status</Label>
              <Controller
                name='status'
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ContentStatus.DRAFT}>Draft</SelectItem>
                      <SelectItem value={ContentStatus.PUBLISHED}>
                        Published
                      </SelectItem>
                      <SelectItem value={ContentStatus.ARCHIVED}>
                        Archived
                      </SelectItem>
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
          </div>

          {/* Content URL */}
          <div className='space-y-2'>
            <Label htmlFor='contentUrl'>
              {watchedType === ContentType.VIDEO
                ? 'Video URL'
                : watchedType === ContentType.ARTICLE
                  ? 'Article URL'
                  : watchedType === ContentType.INFOGRAPHIC
                    ? 'Image URL'
                    : 'Content URL'}
            </Label>
            <Controller
              name='contentUrl'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id='contentUrl'
                  placeholder={
                    watchedType === ContentType.VIDEO
                      ? 'https://youtube.com/watch?v=...'
                      : watchedType === ContentType.ARTICLE
                        ? 'https://example.com/article'
                        : watchedType === ContentType.INFOGRAPHIC
                          ? 'https://example.com/image.jpg'
                          : 'Enter content URL'
                  }
                  className={errors.contentUrl ? 'border-red-500' : ''}
                />
              )}
            />
            {errors.contentUrl && (
              <p className='text-sm text-red-500'>
                {errors.contentUrl.message}
              </p>
            )}
          </div>

          {/* Thumbnail URL */}
          <div className='space-y-2'>
            <Label htmlFor='thumbnailUrl'>Thumbnail URL (Optional)</Label>
            <Controller
              name='thumbnailUrl'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id='thumbnailUrl'
                  placeholder='https://example.com/thumbnail.jpg'
                />
              )}
            />
          </div>

          {/* Duration/Reading Time */}
          {(watchedType === ContentType.VIDEO ||
            watchedType === ContentType.ARTICLE) && (
            <div className='space-y-2'>
              <Label
                htmlFor={
                  watchedType === ContentType.VIDEO ? 'duration' : 'readingTime'
                }
              >
                {watchedType === ContentType.VIDEO
                  ? 'Duration (seconds)'
                  : 'Reading Time (minutes)'}
              </Label>
              <Controller
                name={
                  watchedType === ContentType.VIDEO ? 'duration' : 'readingTime'
                }
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id={
                      watchedType === ContentType.VIDEO
                        ? 'duration'
                        : 'readingTime'
                    }
                    type='number'
                    placeholder={
                      watchedType === ContentType.VIDEO ? '300' : '5'
                    }
                  />
                )}
              />
            </div>
          )}

          {/* Description */}
          <div className='space-y-2'>
            <Label htmlFor='description'>Description</Label>
            <Controller
              name='description'
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  id='description'
                  placeholder='Enter content description'
                  rows={3}
                />
              )}
            />
          </div>

          {/* Tags */}
          <div className='space-y-2'>
            <Label>Tags</Label>
            <div className='flex flex-wrap gap-2 mb-2'>
              {watchedTags.map((tag, index) => (
                <Badge
                  key={index}
                  variant='secondary'
                  className='flex items-center gap-1'
                >
                  {tag}
                  <X
                    className='h-3 w-3 cursor-pointer'
                    onClick={() => removeTag(tag)}
                  />
                </Badge>
              ))}
            </div>
            <div className='flex gap-2'>
              <Input
                value={newTag}
                onChange={e => setNewTag(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder='Add a tag'
              />
              <Button
                type='button'
                variant='outline'
                onClick={addTag}
                disabled={!newTag.trim()}
              >
                Add
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={isSubmitting}>
              {isSubmitting ? 'Updating...' : 'Update Content'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
