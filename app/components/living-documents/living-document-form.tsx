'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { DocumentTemplateSelector } from './document-template-selector';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Save, ArrowLeft } from 'lucide-react';

interface LivingDocumentFormProps {
  initialData?: {
    id?: string;
    title: string;
    type: string;
    content: string;
  };
  mode: 'create' | 'update';
}

export function LivingDocumentForm({
  initialData,
  mode = 'create',
}: LivingDocumentFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    type: initialData?.type || '',
    content: initialData?.content || '',
  });
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setError(null);
  };

  const handleTypeChange = (value: string) => {
    setFormData(prev => ({ ...prev, type: value }));
    setError(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.title.trim()) {
      setError('Please enter a document title');
      return;
    }

    if (!formData.type) {
      setError('Please select a document type');
      return;
    }

    if (!formData.content.trim()) {
      setError('Please enter document content');
      return;
    }

    // Simulate saving
    setIsSaving(true);

    // In a real implementation, this would call an API to save the document
    setTimeout(() => {
      setIsSaving(false);
      router.push('/dashboard/member/living-documents');
    }, 1000);
  };

  const handleCancel = () => {
    router.push('/dashboard/member/living-documents');
  };

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>
            {mode === 'create'
              ? 'Create New Living Document'
              : 'Update Living Document'}
          </CardTitle>
          <CardDescription>
            {mode === 'create'
              ? 'Create a new document that requires regular updates'
              : 'Update your existing living document'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-6'>
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Document Title</label>
              <Input
                name='title'
                value={formData.title}
                onChange={handleInputChange}
                placeholder='Enter document title'
              />
            </div>

            <DocumentTemplateSelector
              value={formData.type}
              onChange={handleTypeChange}
            />

            <div className='space-y-2'>
              <label className='text-sm font-medium'>Document Content</label>
              <Textarea
                name='content'
                value={formData.content}
                onChange={handleInputChange}
                placeholder='Enter document content'
                rows={10}
              />
              <p className='text-xs text-[var(--custom-gray-medium)]'>
                Enter all the information you want to include in this document
              </p>
            </div>

            {error && (
              <Alert variant='destructive'>
                <AlertCircle className='h-4 w-4' />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
        <CardFooter className='flex justify-between'>
          <Button
            type='button'
            variant='outline'
            size='sm'
            onClick={handleCancel}
          >
            <ArrowLeft className='h-4 w-4 mr-2' />
            Cancel
          </Button>
          <Button type='submit' variant='default' size='sm' disabled={isSaving}>
            <Save className='h-4 w-4 mr-2' />
            {isSaving
              ? 'Saving...'
              : mode === 'create'
                ? 'Create Document'
                : 'Update Document'}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}
