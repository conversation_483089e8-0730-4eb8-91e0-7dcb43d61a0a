'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { LivingDocumentQuestionRenderer } from './question-renderer';
import { Save, FileText, AlertTriangle } from 'lucide-react';
import type {
  LivingDocumentQuestion,
  LivingDocumentAnswer,
  LivingDocumentTemplate,
} from '@/types/living-document-builder';

interface LivingDocumentTemplateFormProps {
  template: LivingDocumentTemplate;
  onSave: (answers: LivingDocumentAnswer[]) => Promise<void>;
  initialAnswers?: LivingDocumentAnswer[];
}

export function LivingDocumentTemplateForm({
  template,
  onSave,
  initialAnswers = [],
}: LivingDocumentTemplateFormProps) {
  const [answers, setAnswers] =
    useState<LivingDocumentAnswer[]>(initialAnswers);
  const [saving, setSaving] = useState(false);
  const [showValidation, setShowValidation] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Parse questions from template
  const questions: LivingDocumentQuestion[] = React.useMemo(() => {
    try {
      if (template.questions) {
        // Handle both string (legacy) and array (new) formats
        const parsed =
          typeof template.questions === 'string'
            ? JSON.parse(template.questions)
            : Array.isArray(template.questions)
              ? template.questions
              : [];
        return Array.isArray(parsed) ? parsed : [];
      }
      return [];
    } catch (error) {
      console.error('Error parsing template questions:', error);
      return [];
    }
  }, [template.questions]);

  const handleAnswersChange = (newAnswers: LivingDocumentAnswer[]) => {
    setAnswers(newAnswers);
    setError(null);
  };

  const validateForm = () => {
    const requiredQuestions = questions.filter(q => q.required);
    const missingAnswers = requiredQuestions.filter(question => {
      const answer = answers.find(a => a.questionId === question.id);
      return !answer || !answer.values.some((v: string) => v.trim() !== '');
    });

    return missingAnswers.length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setShowValidation(true);

    if (!validateForm()) {
      setError('Please answer all required questions before saving.');
      return;
    }

    try {
      setSaving(true);
      setError(null);
      await onSave(answers);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to save document. Please try again.'
      );
    } finally {
      setSaving(false);
    }
  };

  const getDocumentTypeLabel = (documentType: string) => {
    const typeLabels: { [key: string]: string } = {
      EmergencyContacts: 'Emergency Contacts',
      PetCare: 'Pet Care Instructions',
      DigitalAssets: 'Digital Assets',
      EndOfLifeWishes: 'End of Life Wishes',
      MedicalDirectives: 'Medical Directives',
      Other: 'Other',
    };
    return typeLabels[documentType] || documentType;
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      {/* Template Header */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-3'>
            {template.icon && <span className='text-2xl'>{template.icon}</span>}
            <div>
              <h1 className='text-2xl'>{template.title}</h1>
              <p className='text-sm text-gray-500 font-normal'>
                {getDocumentTypeLabel(template.documentType)}
              </p>
            </div>
          </CardTitle>
          {template.description && (
            <p className='text-gray-600 mt-2'>{template.description}</p>
          )}
        </CardHeader>
      </Card>

      {/* Questions Section */}
      {questions.length > 0 ? (
        <div className='space-y-6'>
          <div className='flex items-center justify-between'>
            <h2 className='text-xl font-semibold'>Document Questions</h2>
            <span className='text-sm text-gray-500'>
              {questions.filter(q => q.required).length} required questions
            </span>
          </div>

          <LivingDocumentQuestionRenderer
            questions={questions}
            answers={answers}
            onAnswersChange={handleAnswersChange}
            showValidation={showValidation}
          />
        </div>
      ) : (
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-12'>
            <FileText className='h-12 w-12 text-gray-400 mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              No Questions Available
            </h3>
            <p className='text-gray-500 text-center'>
              This template doesn't have any questions configured yet.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Template Content (if available) */}
      {template.content && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className='whitespace-pre-wrap text-sm text-gray-700'>
              {template.content}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Submit Button */}
      <div className='flex justify-end space-x-2'>
        <Button type='submit' disabled={saving} size='lg'>
          <Save className='h-4 w-4 mr-2' />
          {saving ? 'Saving Document...' : 'Save Document'}
        </Button>
      </div>

      {/* Help Text */}
      <div className='text-sm text-gray-500 text-center'>
        <p>
          * Required fields must be completed before saving.
          {questions.some(q => q.allowMultiple) && (
            <span className='block mt-1'>
              Questions marked as "multiple" allow you to add additional answers
              using the + button.
            </span>
          )}
        </p>
      </div>
    </form>
  );
}

// Example usage component for demonstration
export function LivingDocumentTemplateFormExample() {
  const exampleTemplate: LivingDocumentTemplate = {
    id: 'example-template',
    createdByEmail: '<EMAIL>',
    documentType: 'EmergencyContacts',
    title: 'Emergency Contacts Template',
    description: 'Manage your emergency contact information',
    icon: '🚨',
    version: 1,
    status: 'Active',
    questions: [
      {
        id: 'contact-name',
        text: 'What is the name of your emergency contact?',
        type: 'text',
        required: true,
        placeholder: 'Enter full name...',
        helpText: 'Provide the full legal name of your emergency contact',
        allowMultiple: true,
        order: 1,
      },
      {
        id: 'relationship',
        text: 'What is your relationship to this person?',
        type: 'select',
        required: true,
        options: [
          { id: 'spouse', label: 'Spouse/Partner', value: 'spouse' },
          { id: 'parent', label: 'Parent', value: 'parent' },
          { id: 'sibling', label: 'Sibling', value: 'sibling' },
          { id: 'friend', label: 'Friend', value: 'friend' },
          { id: 'other', label: 'Other', value: 'other' },
        ],
        allowMultiple: true,
        order: 2,
      },
      {
        id: 'contact-preference',
        text: 'How should they be contacted in an emergency?',
        type: 'radio',
        required: true,
        options: [
          { id: 'phone', label: 'Phone Call', value: 'phone' },
          { id: 'text', label: 'Text Message', value: 'text' },
          { id: 'email', label: 'Email', value: 'email' },
        ],
        allowMultiple: false,
        order: 3,
      },
    ] as LivingDocumentQuestion[],
  };

  const handleSave = async (answers: LivingDocumentAnswer[]) => {
    console.log('Saving answers:', answers);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    alert('Document saved successfully!');
  };

  return (
    <div className='max-w-4xl mx-auto p-6'>
      <LivingDocumentTemplateForm
        template={exampleTemplate}
        onSave={handleSave}
      />
    </div>
  );
}
