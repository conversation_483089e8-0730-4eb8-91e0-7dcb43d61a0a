'use client';

import React, { useState, useMemo } from 'react';
import { LivingDocumentCard, LivingDocument } from './living-document-card';
import { Button } from '@/components/ui/button';
import { PlusCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface LivingDocumentListProps {
  documents: LivingDocument[];
}

export function LivingDocumentList({ documents }: LivingDocumentListProps) {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [documentsPerPage, setDocumentsPerPage] = useState(6); // 2 rows of 3 cards each

  const handleCreateNew = () => {
    router.push('/dashboard/member/living-documents/create');
  };

  // Calculate pagination
  const totalPages = Math.ceil(documents.length / documentsPerPage);
  const startIndex = (currentPage - 1) * documentsPerPage;
  const endIndex = startIndex + documentsPerPage;
  const currentDocuments = documents.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of the list
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  const handleDocumentsPerPageChange = (newPerPage: number) => {
    setDocumentsPerPage(newPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div>
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h2 className='text-2xl font-bold'>Your Living Documents</h2>
          {documents.length > 0 && (
            <div className='flex items-baseline gap-4 mt-1'>
              <p className='text-sm text-[var(--custom-gray-medium)]'>
                Showing {startIndex + 1}-{Math.min(endIndex, documents.length)}{' '}
                of {documents.length} documents
              </p>
              {documents.length > 6 && (
                <div className='flex items-baseline gap-2'>
                  <span className='text-sm text-[var(--custom-gray-medium)]'>
                    Show:
                  </span>
                  <select
                    value={documentsPerPage}
                    onChange={e =>
                      handleDocumentsPerPageChange(Number(e.target.value))
                    }
                    className='text-sm border border-gray-300 rounded px-2 py-0.5 bg-background align-baseline'
                  >
                    <option value={6}>6 per page</option>
                    <option value={9}>9 per page</option>
                    <option value={12}>12 per page</option>
                    <option value={documents.length}>All</option>
                  </select>
                </div>
              )}
            </div>
          )}
        </div>
        <Button onClick={handleCreateNew} variant='default' size='sm'>
          <PlusCircle className='h-4 w-4 mr-2' />
          Create New Document
        </Button>
      </div>

      {documents.length === 0 ? (
        <div className='bg-gray-50 border rounded-lg p-8 text-center'>
          <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
            No Living Documents Yet
          </h3>
          <p className='text-[var(--custom-gray-medium)] mb-6'>
            Living documents help you manage information that needs regular
            updates, like emergency contacts or pet care instructions.
          </p>
          <Button onClick={handleCreateNew} variant='default' size='sm'>
            <PlusCircle className='h-4 w-4 mr-2' />
            Create Your First Document
          </Button>
        </div>
      ) : (
        <>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {currentDocuments.map(document => (
              <LivingDocumentCard key={document.id} document={document} />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className='flex items-center justify-center gap-2 mt-8'>
              <Button
                variant='outline'
                size='sm'
                onClick={handlePrevious}
                disabled={currentPage === 1}
                className='flex items-center gap-1'
              >
                <ChevronLeft className='h-4 w-4' />
                Previous
              </Button>

              <div className='flex items-center gap-1'>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  page => (
                    <Button
                      key={page}
                      variant={currentPage === page ? 'default' : 'outline'}
                      size='sm'
                      onClick={() => handlePageChange(page)}
                      className='w-10 h-10 p-0'
                    >
                      {page}
                    </Button>
                  )
                )}
              </div>

              <Button
                variant='outline'
                size='sm'
                onClick={handleNext}
                disabled={currentPage === totalPages}
                className='flex items-center gap-1'
              >
                Next
                <ChevronRight className='h-4 w-4' />
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
