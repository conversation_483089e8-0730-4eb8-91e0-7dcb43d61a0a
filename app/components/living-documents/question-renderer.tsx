'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Plus, Trash2, HelpCircle, AlertTriangle } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type {
  LivingDocumentQuestion,
  LivingDocumentAnswer,
} from '@/types/living-document-builder';

interface QuestionRendererProps {
  questions: LivingDocumentQuestion[];
  answers: LivingDocumentAnswer[];
  onAnswersChange: (answers: LivingDocumentAnswer[]) => void;
  showValidation?: boolean;
}

export function LivingDocumentQuestionRenderer({
  questions,
  answers,
  onAnswersChange,
  showValidation = false,
}: QuestionRendererProps) {
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  const getAnswerForQuestion = (questionId: string): string[] => {
    const answer = answers.find(a => a.questionId === questionId);
    return answer?.values || [''];
  };

  const updateAnswer = (questionId: string, values: string[]) => {
    const updatedAnswers = answers.filter(a => a.questionId !== questionId);
    if (values.some(v => v.trim() !== '')) {
      updatedAnswers.push({
        questionId,
        values: values.filter(v => v.trim() !== ''),
      });
    }
    onAnswersChange(updatedAnswers);

    // Clear validation error when user starts typing
    if (validationErrors[questionId]) {
      setValidationErrors(prev => ({ ...prev, [questionId]: '' }));
    }
  };

  const addMultipleAnswer = (questionId: string) => {
    const currentValues = getAnswerForQuestion(questionId);
    updateAnswer(questionId, [...currentValues, '']);
  };

  const removeMultipleAnswer = (questionId: string, index: number) => {
    const currentValues = getAnswerForQuestion(questionId);
    const newValues = currentValues.filter((_, i) => i !== index);
    updateAnswer(questionId, newValues.length > 0 ? newValues : ['']);
  };

  const updateMultipleAnswer = (
    questionId: string,
    index: number,
    value: string
  ) => {
    const currentValues = getAnswerForQuestion(questionId);
    const newValues = [...currentValues];
    newValues[index] = value;
    updateAnswer(questionId, newValues);
  };

  const validateAnswers = () => {
    const errors: Record<string, string> = {};

    questions.forEach(question => {
      if (question.required) {
        const answer = getAnswerForQuestion(question.id);
        const hasValidAnswer = answer.some(value => value.trim() !== '');

        if (!hasValidAnswer) {
          errors[question.id] = 'This question is required';
        }
      }
    });

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  React.useEffect(() => {
    if (showValidation) {
      validateAnswers();
    }
  }, [showValidation, answers]);

  const renderQuestionInput = (question: LivingDocumentQuestion) => {
    const currentValues = getAnswerForQuestion(question.id);
    const hasError = validationErrors[question.id];

    switch (question.type) {
      case 'text':
        return (
          <div className='space-y-3'>
            {currentValues.map((value, index) => (
              <div key={index} className='flex items-center space-x-2'>
                <div className='flex-1'>
                  <Textarea
                    value={value}
                    onChange={e =>
                      updateMultipleAnswer(question.id, index, e.target.value)
                    }
                    placeholder={question.placeholder || 'Enter your answer...'}
                    rows={3}
                    className={hasError ? 'border-red-500' : ''}
                  />
                </div>
                {question.allowMultiple && currentValues.length > 1 && (
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={() => removeMultipleAnswer(question.id, index)}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                )}
              </div>
            ))}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={() => addMultipleAnswer(question.id)}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another Answer
              </Button>
            )}
          </div>
        );

      case 'select':
        return (
          <div className='space-y-3'>
            {currentValues.map((value, index) => (
              <div key={index} className='flex items-center space-x-2'>
                <div className='flex-1'>
                  <Select
                    value={value}
                    onValueChange={newValue =>
                      updateMultipleAnswer(question.id, index, newValue)
                    }
                  >
                    <SelectTrigger className={hasError ? 'border-red-500' : ''}>
                      <SelectValue placeholder='Select an option...' />
                    </SelectTrigger>
                    <SelectContent>
                      {question.options?.map((option: any) => (
                        <SelectItem key={option.id} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                {question.allowMultiple && currentValues.length > 1 && (
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={() => removeMultipleAnswer(question.id, index)}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                )}
              </div>
            ))}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={() => addMultipleAnswer(question.id)}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another Selection
              </Button>
            )}
          </div>
        );

      case 'radio':
        if (question.allowMultiple) {
          // For multiple radio selections, render multiple radio groups
          return (
            <div className='space-y-3'>
              {currentValues.map((value, index) => (
                <div key={index} className='flex items-start space-x-2'>
                  <div className='flex-1'>
                    <RadioGroup
                      value={value}
                      onValueChange={newValue =>
                        updateMultipleAnswer(question.id, index, newValue)
                      }
                      className={
                        hasError ? 'border border-red-500 rounded p-2' : ''
                      }
                    >
                      {question.options?.map((option: any) => (
                        <div
                          key={option.id}
                          className='flex items-center space-x-2'
                        >
                          <RadioGroupItem
                            value={option.value}
                            id={`${question.id}-${index}-${option.id}`}
                          />
                          <Label
                            htmlFor={`${question.id}-${index}-${option.id}`}
                          >
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                  {currentValues.length > 1 && (
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => removeMultipleAnswer(question.id, index)}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={() => addMultipleAnswer(question.id)}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another Selection
              </Button>
            </div>
          );
        } else {
          // Single radio selection
          return (
            <RadioGroup
              value={currentValues[0] || ''}
              onValueChange={value => updateAnswer(question.id, [value])}
              className={hasError ? 'border border-red-500 rounded p-2' : ''}
            >
              {question.options?.map((option: any) => (
                <div key={option.id} className='flex items-center space-x-2'>
                  <RadioGroupItem
                    value={option.value}
                    id={`${question.id}-${option.id}`}
                  />
                  <Label htmlFor={`${question.id}-${option.id}`}>
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          );
        }

      default:
        return <div>Unsupported question type</div>;
    }
  };

  const sortedQuestions = [...questions].sort((a, b) => a.order - b.order);

  return (
    <TooltipProvider>
      <div className='space-y-6'>
        {sortedQuestions.map((question, index) => (
          <Card key={question.id}>
            <CardHeader>
              <CardTitle className='flex items-start justify-between'>
                <div className='flex items-start space-x-2'>
                  <span className='text-sm font-medium text-gray-500 mt-1'>
                    {index + 1}.
                  </span>
                  <div>
                    <div className='flex items-center space-x-2'>
                      <span>{question.text}</span>
                      {question.required && (
                        <span className='text-red-500 text-sm'>*</span>
                      )}
                      {question.helpText && (
                        <Tooltip>
                          <TooltipTrigger>
                            <HelpCircle className='h-4 w-4 text-gray-400' />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className='max-w-xs'>{question.helpText}</p>
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>
                    {question.allowMultiple && (
                      <p className='text-sm text-gray-500 mt-1'>
                        You can add multiple answers to this question
                      </p>
                    )}
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {renderQuestionInput(question)}
              {validationErrors[question.id] && (
                <Alert variant='destructive' className='mt-3'>
                  <AlertTriangle className='h-4 w-4' />
                  <AlertDescription>
                    {validationErrors[question.id]}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </TooltipProvider>
  );
}
