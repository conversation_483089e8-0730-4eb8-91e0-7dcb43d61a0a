'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUserContext } from '@/components/welon-trust/user-context';
import { useRole } from '@/lib/roles/role-context';
import {
  Shield,
  FileText,
  AlertTriangle,
  Upload,
  Clock,
  CheckCircle,
  Users,
  Phone,
  Mail,
  User,
  Lock,
  AlertCircle,
  Settings,
} from 'lucide-react';
import { Headline, Subhead } from '@workspace/ui/brand';

// Function to generate user-specific data
const generateUserStats = (userId: string) => {
  // Generate consistent but different stats for each user based on their ID
  const seed = userId
    .split('')
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const random = (min: number, max: number) =>
    Math.floor((((seed * 9301 + 49297) % 233280) / 233280) * (max - min + 1)) +
    min;

  return {
    documentsCount: random(3, 12),
    lastAccess: new Date(Date.now() - random(1, 30) * 24 * 60 * 60 * 1000),
    emergencyContacts: random(1, 4),
    hasActiveCase: random(1, 10) > 7,
    caseType: random(1, 2) === 1 ? 'Death Certificate' : 'Incapacitation',
    caseNumber: `2025-${String(random(100, 999)).padStart(3, '0')}`,
  };
};

const generateUserActivity = (userId: string, userName: string) => {
  const seed = userId
    .split('')
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const random = (min: number, max: number) =>
    Math.floor((((seed * 9301 + 49297) % 233280) / 233280) * (max - min + 1)) +
    min;

  const activities = [
    {
      type: 'Document Access',
      description: 'Last Will and Testament accessed',
      time: new Date(Date.now() - random(1, 7) * 24 * 60 * 60 * 1000),
      status: 'completed' as const,
    },
    {
      type: 'Emergency Contact Update',
      description: 'Emergency contact information updated',
      time: new Date(Date.now() - random(7, 30) * 24 * 60 * 60 * 1000),
      status: 'completed' as const,
    },
    {
      type: 'Document Upload',
      description: 'Healthcare directive uploaded',
      time: new Date(Date.now() - random(30, 90) * 24 * 60 * 60 * 1000),
      status: 'completed' as const,
    },
  ];

  return activities.slice(0, random(1, 3));
};

// Generate all documents from all users for Recent Activity
const generateAllUsersDocuments = () => {
  const users = [
    { id: 'user-1', name: 'Emily Rodriguez' },
    { id: 'user-2', name: 'Michael Chen' },
    { id: 'user-3', name: 'Sarah Johnson' },
    { id: 'user-4', name: 'David Wilson' },
    { id: 'user-5', name: 'Lisa Anderson' },
    { id: 'user-6', name: 'Robert Brown' },
    { id: 'user-7', name: 'Jennifer Davis' },
    { id: 'user-8', name: 'James Miller' },
  ];

  const documentTypes = ['Legal', 'Medical', 'Personal', 'Financial'];
  const documentTitles = {
    Legal: [
      'Last Will and Testament',
      'Power of Attorney',
      'Trust Agreement',
      'Living Will',
    ],
    Medical: [
      'Healthcare Directive',
      'Medical Power of Attorney',
      'DNR Order',
      'Medical Records',
    ],
    Personal: [
      'Personal Instructions',
      'Digital Assets',
      'Pet Care Instructions',
      'Funeral Wishes',
    ],
    Financial: [
      'Bank Account Info',
      'Investment Portfolio',
      'Insurance Policies',
      'Retirement Accounts',
    ],
  };

  const statuses = ['pending_access', 'accessed', 'verified', 'expired'];
  const statusColors = {
    pending_access: 'orange',
    accessed: 'green',
    verified: 'blue',
    expired: 'red',
  };

  const allDocuments: any[] = [];

  users.forEach(user => {
    const seed = user.id
      .split('')
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    const random = (min: number, max: number) =>
      Math.floor(
        (((seed * 9301 + 49297) % 233280) / 233280) * (max - min + 1)
      ) + min;

    const numDocs = random(2, 4);
    for (let i = 0; i < numDocs; i++) {
      const type = documentTypes[i % documentTypes.length];
      const title =
        documentTitles[type as keyof typeof documentTitles][
          random(
            0,
            documentTitles[type as keyof typeof documentTitles].length - 1
          )
        ];
      const status = statuses[random(0, statuses.length - 1)];

      allDocuments.push({
        id: `doc-${user.id}-${i}`,
        userId: user.id,
        userName: user.name,
        title,
        type,
        status,
        statusColor: statusColors[status as keyof typeof statusColors],
        lastAccessed: new Date(
          Date.now() - random(1, 30) * 24 * 60 * 60 * 1000
        ),
        createdAt: new Date(Date.now() - random(30, 365) * 24 * 60 * 60 * 1000),
        accessRequests: random(0, 5),
        isUrgent: random(1, 10) > 8,
      });
    }
  });

  // Sort by date (newest first)
  return allDocuments.sort(
    (a, b) => b.lastAccessed.getTime() - a.lastAccessed.getTime()
  );
};

export default function EmergencyDashboard() {
  const router = useRouter();
  const { selectedUser } = useUserContext();
  const { hasPermission } = useRole();
  const [allDocuments] = useState(() => generateAllUsersDocuments());
  const [accessingDocument, setAccessingDocument] = useState<any>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [verificationError, setVerificationError] = useState<string | null>(
    null
  );

  // Generate user-specific data when user is selected
  const userStats = selectedUser ? generateUserStats(selectedUser.id) : null;
  const userActivity = selectedUser
    ? generateUserActivity(selectedUser.id, selectedUser.name)
    : [];

  // Check if user can manage emergency contacts
  const canManageEmergencyContacts = hasPermission('manage_emergency_contacts');

  const handleDocumentAccess = (document: any) => {
    setAccessingDocument(document);
    setVerificationCode('');
    setVerificationError(null);
  };

  const handleVerifyAccess = () => {
    if (verificationCode === '123456') {
      setVerificationError(null);
      setAccessingDocument(null);
      // Navigate to documents page with the specific user selected
      router.push(`/emergency/documents?userId=${accessingDocument.userId}`);
    } else {
      setVerificationError('Invalid verification code. Please try again.');
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusBadge = (status: string, statusColor: string) => {
    const variants = {
      orange: 'outline',
      green: 'default',
      blue: 'secondary',
      red: 'destructive',
    };

    const labels = {
      pending_access: 'Pending Access',
      accessed: 'Accessed',
      verified: 'Verified',
      expired: 'Expired',
    };

    return (
      <Badge
        variant={
          (variants[statusColor as keyof typeof variants] || 'outline') as
            | 'default'
            | 'destructive'
            | 'outline'
            | 'secondary'
        }
      >
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-6xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>Welon Trust Emergency Dashboard</Headline>
          <Subhead className='text-muted-foreground'>
            Emergency document access and processing center
          </Subhead>
        </div>

        {/* No User Selected Warning */}
        {/* {!selectedUser && (
          <Alert className='mb-6 bg-amber-50 text-amber-800 border-amber-200'>
            <Users className='h-4 w-4' />
            <AlertTitle>No Member Selected</AlertTitle>
            <AlertDescription>
              Please select a member from the dropdown above to access their
              emergency documents and perform member-specific actions.
            </AlertDescription>
          </Alert>
        )} */}

        {/* Status Overview */}
        {/* <div className='grid md:grid-cols-3 gap-4 mb-8'>
          {selectedUser ? (
            // User-specific stats
            <>
              <Card>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-sm font-medium text-muted-foreground'>
                    {selectedUser.name}'s Documents
                  </CardTitle>
                </CardHeader>
                <CardContent className='pb-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-xl font-bold'>
                      {userStats?.documentsCount}
                    </span>
                    <FileText className='h-4 w-4 text-blue-500' />
                  </div>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Last accessed {userStats?.lastAccess.toLocaleDateString()}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-sm font-medium text-muted-foreground'>
                    Emergency Contacts
                  </CardTitle>
                </CardHeader>
                <CardContent className='pb-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-xl font-bold'>
                      {userStats?.emergencyContacts}
                    </span>
                    <Users className='h-4 w-4 text-green-500' />
                  </div>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Registered contacts
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-sm font-medium text-muted-foreground'>
                    Case Status
                  </CardTitle>
                </CardHeader>
                <CardContent className='pb-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm font-bold'>
                      {userStats?.hasActiveCase
                        ? userStats.caseType
                        : 'No Active Case'}
                    </span>
                    <AlertTriangle
                      className={`h-4 w-4 ${userStats?.hasActiveCase ? 'text-orange-500' : 'text-green-500'}`}
                    />
                  </div>
                  <p className='text-xs text-muted-foreground mt-1'>
                    {userStats?.hasActiveCase
                      ? `Case #${userStats.caseNumber}`
                      : 'All clear'}
                  </p>
                </CardContent>
              </Card>
            </>
          ) : (
            // Global stats when no user selected
            <>
              <Card>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-sm font-medium text-muted-foreground'>
                    Active Emergency Cases
                  </CardTitle>
                </CardHeader>
                <CardContent className='pb-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-xl font-bold'>3</span>
                    <AlertTriangle className='h-4 w-4 text-orange-500' />
                  </div>
                  <p className='text-xs text-muted-foreground mt-1'>
                    2 new this week
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-sm font-medium text-muted-foreground'>
                    Documents Processed
                  </CardTitle>
                </CardHeader>
                <CardContent className='pb-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-xl font-bold'>47</span>
                    <FileText className='h-4 w-4 text-blue-500' />
                  </div>
                  <p className='text-xs text-muted-foreground mt-1'>
                    12 this month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className='pb-2'>
                  <CardTitle className='text-sm font-medium text-muted-foreground'>
                    Response Time
                  </CardTitle>
                </CardHeader>
                <CardContent className='pb-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-xl font-bold'>2.4h</span>
                    <Clock className='h-4 w-4 text-green-500' />
                  </div>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Average response
                  </p>
                </CardContent>
              </Card>
            </>
          )}
        </div> */}

        {/* Quick Actions */}
        <Card className='mb-8'>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Emergency tasks and document processing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-4'>
              <Button
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
                onClick={() => router.push('/welon/documents')}
              >
                <FileText className='h-6 w-6' />
                <span>Access Documents</span>
              </Button>

              <Button
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
                onClick={() => router.push('/welon/upload-documents')}
              >
                <Upload className='h-6 w-6' />
                <span>Upload Documents</span>
              </Button>

              <Button
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
                onClick={() => router.push('/welon/submit-evidence')}
              >
                <Shield className='h-6 w-6' />
                <span>Submit Evidence</span>
              </Button>

              <Button
                variant='outline'
                className={`h-auto p-4 flex flex-col items-center gap-2 ${
                  !canManageEmergencyContacts
                    ? 'opacity-50 cursor-not-allowed'
                    : ''
                }`}
                onClick={() => {
                  if (!canManageEmergencyContacts) {
                    alert(
                      "You don't have permission to manage emergency contacts. This feature is only available to members."
                    );
                  } else {
                    router.push('/welon/settings');
                  }
                }}
                disabled={!canManageEmergencyContacts}
              >
                <Settings className='h-6 w-6' />
                <span>Settings</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        {/* <div className='grid lg:grid-cols-2 gap-6'>
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedUser
                  ? `${selectedUser.name}'s Documents`
                  : 'All Documents & Access Requests'}
              </CardTitle>
              <CardDescription>
                {selectedUser
                  ? `${selectedUser.name}'s documents - click to request access`
                  : 'All member documents sorted by date - click to request access'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                {selectedUser ? (
                  // User-specific documents
                  (() => {
                    const userDocuments = allDocuments.filter(
                      doc => doc.userId === selectedUser.id
                    );
                    return userDocuments.length > 0 ? (
                      <div className='max-h-96 overflow-y-auto space-y-2'>
                        {userDocuments.map(document => (
                          <div
                            key={document.id}
                            className='flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200 hover:bg-blue-100 transition-colors cursor-pointer'
                            onClick={() => handleDocumentAccess(document)}
                          >
                            <div className='flex-1'>
                              <div className='flex items-center gap-2 mb-1'>
                                <p className='font-medium text-blue-900'>
                                  {document.title}
                                </p>
                                {document.isUrgent && (
                                  <Badge
                                    variant='destructive'
                                    className='text-xs'
                                  >
                                    Urgent
                                  </Badge>
                                )}
                              </div>
                              <p className='text-sm text-blue-700'>
                                {document.type}
                              </p>
                              <p className='text-xs text-blue-600'>
                                Last accessed:{' '}
                                {formatDate(document.lastAccessed)}
                                {document.accessRequests > 0 &&
                                  ` • ${document.accessRequests} access requests`}
                              </p>
                            </div>
                            <div className='flex items-center gap-2'>
                              {getStatusBadge(
                                document.status,
                                document.statusColor
                              )}
                              <Button size='sm' variant='outline'>
                                Access
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className='text-center py-8 text-muted-foreground'>
                        <FileText className='h-8 w-8 mx-auto mb-2 opacity-50' />
                        <p>No documents found for {selectedUser.name}</p>
                      </div>
                    );
                  })()
                ) : (
                  // All documents when no user selected
                  <div className='max-h-96 overflow-y-auto space-y-2'>
                    {allDocuments.map(document => (
                      <div
                        key={document.id}
                        className='flex items-center justify-between p-3 bg-gray-[var(--custom-gray-light)] rounded-lg border hover:bg-[var(--custom-blue-light)] transition-colors cursor-pointer'
                        onClick={() => handleDocumentAccess(document)}
                      >
                        <div className='flex-1'>
                          <div className='flex items-center gap-2 mb-1'>
                            <p className='font-medium text-[var(--custom-gray-dark)]'>
                              {document.title}
                            </p>
                            {document.isUrgent && (
                              <Badge variant='destructive' className='text-xs'>
                                Urgent
                              </Badge>
                            )}
                          </div>
                          <p className='text-sm text-[var(--custom-gray-dark)]'>
                            {document.userName} • {document.type}
                          </p>
                          <p className='text-xs text-[var(--custom-gray-dark)]'>
                            Last accessed: {formatDate(document.lastAccessed)}
                            {document.accessRequests > 0 &&
                              ` • ${document.accessRequests} access requests`}
                          </p>
                        </div>
                        <div className='flex items-center gap-2'>
                          {getStatusBadge(
                            document.status,
                            document.statusColor
                          )}
                          <Button size='sm' variant='outline'>
                            Access
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Emergency Protocols</CardTitle>
              <CardDescription>
                Standard procedures and contact information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='p-3 bg-blue-50 rounded-lg border border-blue-200'>
                  <h4 className='font-medium text-blue-800 mb-2'>
                    Death Certificate Verification
                  </h4>
                  <ul className='text-sm text-blue-700 space-y-1'>
                    <li>• Verify certificate authenticity</li>
                    <li>• Confirm identity of requester</li>
                    <li>• Grant permanent document access</li>
                    <li>• Notify emergency contacts</li>
                  </ul>
                </div>

                <div className='p-3 bg-purple-50 rounded-lg border border-purple-200'>
                  <h4 className='font-medium text-purple-800 mb-2'>
                    Incapacitation Protocol
                  </h4>
                  <ul className='text-sm text-purple-700 space-y-1'>
                    <li>• Verify medical documentation</li>
                    <li>• Confirm authorized requester</li>
                    <li>• Grant temporary access (180 days)</li>
                    <li>• Schedule review date</li>
                  </ul>
                </div>

                <div className='p-3 bg-gray-50 rounded-lg border border-gray-200'>
                  <h4 className='font-medium text-black mb-2'>
                    Emergency Contacts
                  </h4>
                  <div className='space-y-2 text-sm text-black'>
                    <div className='flex items-center gap-2'>
                      <Phone className='h-4 w-4' />
                      <span>24/7 Hotline: (*************</span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Mail className='h-4 w-4' />
                      <span><EMAIL></span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div> */}

        {/* System Status */}
        <Alert className='mt-8 border-green-200 bg-green-50'>
          <CheckCircle className='h-4 w-4 text-green-600' />
          <AlertTitle className='text-green-800'>
            System Status: Operational
          </AlertTitle>
          <AlertDescription className='text-green-700'>
            All emergency systems are functioning normally. Last system check:{' '}
            {new Date().toLocaleString()}
          </AlertDescription>
        </Alert>

        {/* Document Access Verification Modal */}
        {accessingDocument && (
          <div className='fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50'>
            <Card className='w-full max-w-md'>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Lock className='h-5 w-5 text-blue-600' />
                  Document Access Verification
                </CardTitle>
                <CardDescription>
                  Enter verification code to access "{accessingDocument.title}"
                  for {accessingDocument.userName}
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='bg-[var(--custom-blue-light)] p-3 rounded-lg border border-blue-200'>
                  <div className='flex items-center justify-between text-sm'>
                    <span className='font-medium'>Document:</span>
                    <span>{accessingDocument.title}</span>
                  </div>
                  <div className='flex items-center justify-between text-sm mt-1'>
                    <span className='font-medium'>Member:</span>
                    <span>{accessingDocument.userName}</span>
                  </div>
                  <div className='flex items-center justify-between text-sm mt-1'>
                    <span className='font-medium'>Type:</span>
                    <span>{accessingDocument.type}</span>
                  </div>
                  <div className='flex items-center justify-between text-sm mt-1'>
                    <span className='font-medium'>Status:</span>
                    {getStatusBadge(
                      accessingDocument.status,
                      accessingDocument.statusColor
                    )}
                  </div>
                </div>

                {verificationError && (
                  <Alert variant='destructive'>
                    <AlertCircle className='h-4 w-4' />
                    <AlertDescription>{verificationError}</AlertDescription>
                  </Alert>
                )}

                <div className='space-y-2'>
                  <Label htmlFor='accessCode'>Verification Code</Label>
                  <Input
                    id='accessCode'
                    type='password'
                    value={verificationCode}
                    onChange={e => setVerificationCode(e.target.value)}
                    placeholder='Enter 6-digit code'
                    maxLength={6}
                    className={verificationError ? 'border-destructive' : ''}
                  />
                  <p className='text-xs text-muted-foreground'>
                    Enter the 6-digit verification code to access this document.
                  </p>
                </div>

                <div className='flex gap-2'>
                  <Button
                    variant='outline'
                    onClick={() => setAccessingDocument(null)}
                    className='flex-1'
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleVerifyAccess}
                    className='flex-1'
                    disabled={!verificationCode.trim()}
                  >
                    Verify & Access
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
