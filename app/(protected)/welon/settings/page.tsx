'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Shield,
  CheckCircle,
  History,
  Monitor,
  Smartphone,
  Tablet,
  LogOut,
  User,
  RefreshCw,
} from 'lucide-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { fetchDevices, signOut, forgetDevice } from 'aws-amplify/auth';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { useAuth } from '@/app/context/AuthContext';
import { useRouter } from 'next/navigation';
import routes from '@/utils/routes';
import {
  fetchLoginHistory,
  parseDeviceInfo,
} from '../../../utils/loginHistory';
import ChangePasswordDialog from '@/components/ChangePasswordDialog';
import { fetchUserByCognitoId } from '@/lib/data/users';
import type { User as UserType } from '@/types/account';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../../../amplify/data/resource';

export default function WelonSettingsPage() {
  const { refreshUser, user, logout } = useAuth();
  const queryClient = useQueryClient();
  const router = useRouter();

  // Amplify client for mutations
  const client = generateClient<Schema>();

  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const [fullUserData, setFullUserData] = useState<UserType | null>(null);
  const [isLoadingUserData, setIsLoadingUserData] = useState(false);
  const [hasAttemptedUserDataLoad, setHasAttemptedUserDataLoad] =
    useState(false);

  // Reset attempt flag when user changes
  React.useEffect(() => {
    setHasAttemptedUserDataLoad(false);
    setFullUserData(null);
  }, [user?.userId]);

  // Load full user data when user is available
  React.useEffect(() => {
    const loadUserData = async () => {
      if (user?.userId && !hasAttemptedUserDataLoad && !isLoadingUserData) {
        setIsLoadingUserData(true);
        setHasAttemptedUserDataLoad(true);
        try {
          const userData = await fetchUserByCognitoId(user.userId);
          setFullUserData(userData);
        } catch (error) {
          console.error('Error loading user data:', error);
          // Keep fullUserData as null, but don't retry automatically
        } finally {
          setIsLoadingUserData(false);
        }
      }
    };

    loadUserData();
  }, [user?.userId, hasAttemptedUserDataLoad, isLoadingUserData]);

  // Security-related state
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [securitySettings, setSecuritySettings] = useState({
    mfaEnabled: false,
    rememberMe: true,
  });

  // Login history query
  const {
    data: loginHistory,
    isLoading: isLoadingLoginHistory,
    refetch: refetchLoginHistory,
  } = useQuery({
    queryKey: ['welonLoginHistory'],
    queryFn: () => fetchLoginHistory(user?.signInDetails?.loginId || ''),
    enabled: !!user?.signInDetails?.loginId,
  });

  const {
    data: userDevices,
    isLoading: isLoadingDevices,
    error,
    refetch: refetchDevices,
  } = useQuery({
    queryKey: ['userDevices'],
    queryFn: fetchDevices,
  });

  // Helper function to get device display name
  const getDeviceDisplayName = (device: any) => {
    const userAgent = device.deviceAttributes?.user_agent || '';

    if (userAgent) {
      const deviceInfo = parseDeviceInfo(userAgent);
      return `${deviceInfo.os} • ${deviceInfo.browser}`;
    }

    // Fallback to device name if available
    if (device.name) {
      return `Device ${device.name.slice(0, 20)}`;
    }

    return 'Unknown Device';
  };

  // Helper function to get device type for icon
  const getDeviceType = (device: any) => {
    const userAgent = device.deviceAttributes?.user_agent || '';

    if (userAgent) {
      const deviceInfo = parseDeviceInfo(userAgent);
      return deviceInfo.device;
    }

    // Fallback: try to guess from device name
    const deviceName = device.name?.toLowerCase() || '';
    if (
      deviceName.includes('android') ||
      deviceName.includes('iphone') ||
      deviceName.includes('mobile')
    ) {
      return 'Mobile';
    }
    if (deviceName.includes('ipad') || deviceName.includes('tablet')) {
      return 'Tablet';
    }

    return 'Desktop';
  };

  // Helper function to get device icon based on device type
  const getDeviceIcon = (device: any) => {
    const deviceType = getDeviceType(device);

    switch (deviceType) {
      case 'Mobile':
        return Smartphone;
      case 'Tablet':
        return Tablet;
      default:
        return Monitor;
    }
  };

  // Helper function to check if device is current
  const isCurrentDevice = (device: any) => {
    if (!userDevices || userDevices.length === 0) return false;

    // Since AWS Cognito doesn't store User Agent in deviceAttributes,
    // we'll use a different approach based on device name and timing
    if (typeof window !== 'undefined') {
      const currentUserAgent = navigator.userAgent;
      const currentDeviceInfo = parseDeviceInfo(currentUserAgent);

      // Try to match based on device name patterns
      const deviceName = device.name || '';

      // Parse device info from device name (AWS Cognito sometimes stores browser info in name)
      const deviceInfo = parseDeviceInfo(deviceName);

      // Check if device name contains current browser/OS info
      const deviceNameLower = deviceName.toLowerCase();
      const currentBrowserBase = currentDeviceInfo.browser
        .split(' ')[0]
        .toLowerCase();
      const currentOSBase = currentDeviceInfo.os.split(' ')[0].toLowerCase();

      // Check for browser match (including chromium as chrome)
      const nameContainsBrowser =
        deviceNameLower.includes(currentBrowserBase) ||
        (currentBrowserBase === 'chrome' &&
          deviceNameLower.includes('chromium'));

      const nameContainsOS = deviceNameLower.includes(currentOSBase);

      // If device name contains current browser and OS, it's likely current
      if (nameContainsBrowser && nameContainsOS) {
        return true;
      }

      // Alternative: check if parsed device info from name matches current
      if (deviceInfo.browser && deviceInfo.os) {
        const browserMatch = deviceInfo.browser
          .toLowerCase()
          .includes(currentBrowserBase);
        const osMatch = deviceInfo.os.toLowerCase().includes(currentOSBase);

        if (browserMatch && osMatch) {
          return true;
        }
      }
    }

    // Final fallback: if only one device exists, it must be current
    if (userDevices.length === 1) {
      return true;
    }

    return false;
  };

  const handleSignOutDevice = async (deviceId: string) => {
    try {
      if (!user?.userId) {
        toast.error('User not authenticated');
        return;
      }

      if (!deviceId) {
        toast.error('Device ID is required');
        return;
      }

      const mutationArgs = {
        deviceId,
        username: user.userId,
      };

      const result = await client.mutations.signOutDevice(mutationArgs);

      if (result.data && JSON.parse(result.data as string).success) {
        toast.success('Device signed out successfully');
        queryClient.invalidateQueries({ queryKey: ['userDevices'] });
      } else {
        throw new Error('Failed to sign out device');
      }
    } catch (error) {
      toast.error('Failed to sign out device');
    }
  };

  // Device management functions
  const handleRefreshDevices = async () => {
    try {
      await refetchDevices();
      toast.success('Device list refreshed');
    } catch (error) {
      console.error('Error refreshing devices:', error);
      toast.error('Failed to refresh device list');
    }
  };

  const handleSignOutAllDevices = async () => {
    try {
      setIsSigningOut(true);

      if (!user?.userId) {
        toast.error('User not authenticated');
        setIsSigningOut(false);
        return;
      }

      if (!userDevices || userDevices.length === 0) {
        toast.info('No devices to sign out');
        setIsSigningOut(false);
        return;
      }

      // Sign out all devices except current one using our Lambda function
      const otherDevices = userDevices.filter(
        device => !isCurrentDevice(device)
      );

      if (otherDevices.length > 0) {
        // Sign out other devices first
        const signOutPromises = otherDevices.map(device =>
          client.mutations.signOutDevice({
            deviceId: device.id,
            username: user.userId,
          })
        );

        await Promise.all(signOutPromises);

        // Refresh device list to show the changes
        await refetchDevices();

        toast.success(`Signed out from ${otherDevices.length} other device(s)`);
      }

      // Finally, sign out current device using global sign out
      await signOut({ global: true });
      // Clear auth context state
      await logout();
      toast.success('Successfully signed out from all devices');
      // Redirect to login page
      router.push(routes.login);
    } catch (error) {
      console.error('Error signing out from all devices:', error);
      toast.error('Failed to sign out from all devices');
      setIsSigningOut(false);
    }
  };

  // Initialize rememberMe from localStorage
  useEffect(() => {
    const storedRememberMe = localStorage.getItem('rememberMe');
    if (storedRememberMe !== null) {
      const rememberMeValue = storedRememberMe === 'true';
      setSecuritySettings(prev => ({
        ...prev,
        rememberMe: rememberMeValue,
      }));
    }
  }, []);

  const handleRememberMeChange = (checked: boolean) => {
    localStorage.setItem('rememberMe', checked.toString());
    setSecuritySettings(prev => ({
      ...prev,
      rememberMe: checked,
    }));
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h1 className='text-3xl font-bold mb-2 text-[var(--foreground)]'>
          Account Settings
        </h1>
        <p className='text-lg text-[var(--custom-gray-medium)]'>
          Manage your Welon Trust account settings and preferences
        </p>
      </div>

      {/* Notification */}
      {notification && (
        <Alert
          className={`${
            notification.type === 'success'
              ? 'border-green-200 bg-green-50'
              : 'border-red-200 bg-red-50'
          }`}
        >
          {notification.type === 'success' ? (
            <CheckCircle className='h-4 w-4 text-green-600' />
          ) : (
            <Shield className='h-4 w-4 text-red-600' />
          )}
          <AlertDescription
            className={
              notification.type === 'success'
                ? 'text-green-800'
                : 'text-red-800'
            }
          >
            {notification.message}
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue='profile' className='w-full'>
        <TabsList className='grid w-full grid-cols-3'>
          <TabsTrigger value='profile' className='cursor-pointer flex-1'>
            <User className='h-4 w-4 mr-2' />
            Profile
          </TabsTrigger>
          <TabsTrigger value='security' className='cursor-pointer flex-1'>
            <Shield className='h-4 w-4 mr-2' />
            Security
          </TabsTrigger>
          <TabsTrigger value='history' className='cursor-pointer flex-1'>
            <History className='h-4 w-4 mr-2' />
            Login History
          </TabsTrigger>
        </TabsList>

        <TabsContent value='profile' className='space-y-6'>
          {/* User Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <User className='h-5 w-5' />
                <span>Account Information</span>
              </CardTitle>
              <CardDescription>
                Your Welon Trust account details
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    Email
                  </Label>
                  <div className='p-3 bg-[var(--background)] rounded-md'>
                    <p className='text-sm'>
                      {user?.signInDetails?.loginId || 'Not available'}
                    </p>
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    User ID
                  </Label>
                  <div className='p-3 bg-[var(--background)] rounded-md'>
                    <p className='text-sm font-mono'>
                      {user?.userId || 'Not available'}
                    </p>
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    Role
                  </Label>
                  <div className='p-3 bg-[var(--background)] rounded-md'>
                    {isLoadingUserData ? (
                      <p className='text-sm'>Loading...</p>
                    ) : fullUserData ? (
                      <div className='flex flex-wrap gap-2'>
                        <Badge
                          variant='default'
                          className='bg-[var(--eggplant)]/10 text-[var(--eggplant)] hover:bg-[var(--eggplant)]/10'
                        >
                          {fullUserData.role}
                        </Badge>
                        {fullUserData.subrole && (
                          <Badge
                            variant='secondary'
                            className='bg-gray-100 text-gray-800 hover:bg-gray-100'
                          >
                            {fullUserData.subrole}
                          </Badge>
                        )}
                      </div>
                    ) : (
                      <p className='text-sm'>Not available</p>
                    )}
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    Full Name
                  </Label>
                  <div className='p-3 bg-[var(--background)] rounded-md'>
                    <p className='text-sm'>
                      {isLoadingUserData
                        ? 'Loading...'
                        : fullUserData
                          ? `${fullUserData.firstName} ${fullUserData.lastName}`
                          : 'Not available'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='security'>
          <div className='space-y-6'>
            {/* Password Settings */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  <Shield className='h-5 w-5' />
                  <span>Password & Authentication</span>
                </CardTitle>
                <CardDescription>
                  Manage your password and two-factor authentication
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='space-y-4'>
                  <div className='space-y-2'>
                    <Label>Password Management</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Regularly update your password to keep your account
                      secure.
                    </p>
                    <ChangePasswordDialog
                      trigger={
                        <Button className='w-full'>Change Password</Button>
                      }
                    />
                  </div>
                </div>

                {/* <div className='border-t pt-4'>
                  <div className='flex items-center justify-between'>
                    <div className='space-y-0.5'>
                      <Label>Two-Factor Authentication</Label>
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        Add an extra layer of security to your account
                      </p>
                    </div>
                    <Switch
                      checked={securitySettings.mfaEnabled}
                      onCheckedChange={checked =>
                        setSecuritySettings(prev => ({
                          ...prev,
                          mfaEnabled: checked,
                        }))
                      }
                    />
                  </div>
                </div> */}

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Remember Me</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Stay logged in for extended periods
                    </p>
                  </div>
                  <Switch
                    checked={securitySettings.rememberMe}
                    onCheckedChange={handleRememberMeChange}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Active Sessions */}
            <Card>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <div>
                    <CardTitle>Active Sessions</CardTitle>
                    <CardDescription>
                      View and manage your active login sessions across
                      different devices and browsers
                    </CardDescription>
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={handleRefreshDevices}
                    disabled={isLoadingDevices}
                    className='flex items-center space-x-2'
                  >
                    <RefreshCw
                      className={`h-4 w-4 ${isLoadingDevices ? 'animate-spin' : ''}`}
                    />
                    <span>Refresh</span>
                  </Button>
                </div>
              </CardHeader>
              <CardContent className='space-y-4'>
                {isLoadingDevices ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading device information...
                    </p>
                  </div>
                ) : !userDevices || userDevices.length === 0 ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      No devices found.
                    </p>
                  </div>
                ) : (
                  // Sort devices to show current device first, then by last authenticated date
                  [...userDevices]
                    .sort((a: any, b: any) => {
                      const aIsCurrent = isCurrentDevice(a);
                      const bIsCurrent = isCurrentDevice(b);

                      // Current device always comes first
                      if (aIsCurrent && !bIsCurrent) return -1;
                      if (!aIsCurrent && bIsCurrent) return 1;

                      // If both or neither are current, sort by last authenticated date
                      const dateA = a.lastAuthenticatedDate
                        ? new Date(a.lastAuthenticatedDate).getTime()
                        : 0;
                      const dateB = b.lastAuthenticatedDate
                        ? new Date(b.lastAuthenticatedDate).getTime()
                        : 0;
                      return dateB - dateA;
                    })
                    .map((device: any, index) => (
                      <div
                        key={device.id || index}
                        className='flex items-center justify-between p-4 border rounded-lg'
                      >
                        <div className='flex items-center space-x-3'>
                          {React.createElement(getDeviceIcon(device), {
                            className:
                              'h-5 w-5 text-[var(--custom-gray-medium)]',
                          })}
                          <div>
                            <div className='flex flex-col'>
                              <p className='font-medium'>
                                {getDeviceDisplayName(device)}
                              </p>
                              {isCurrentDevice(device) && (
                                <span className='text-xs text-green-600 font-medium'>
                                  Current Session
                                </span>
                              )}
                            </div>
                            <p className='text-sm text-[var(--custom-gray-medium)]'>
                              Created:{' '}
                              {device.createDate
                                ? format(
                                    new Date(device.createDate),
                                    'MMM d, yyyy'
                                  )
                                : 'Unknown'}
                            </p>
                            <p className='text-xs text-[var(--custom-gray-medium)]'>
                              Last used:{' '}
                              {device.lastAuthenticatedDate
                                ? format(
                                    new Date(device.lastAuthenticatedDate),
                                    'MMM d, yyyy h:mm a'
                                  )
                                : 'Unknown'}
                            </p>
                          </div>
                        </div>
                        <div className='flex items-center space-x-2'>
                          {isCurrentDevice(device) && (
                            <Badge variant='default'>Current</Badge>
                          )}
                          {!isCurrentDevice(device) && (
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => handleSignOutDevice(device.id)}
                              disabled={isSigningOut}
                            >
                              <LogOut className='h-4 w-4 mr-1' />
                              Sign Out Device
                            </Button>
                          )}
                        </div>
                      </div>
                    ))
                )}

                <Button
                  variant='outline'
                  className='w-full'
                  onClick={handleSignOutAllDevices}
                  disabled={isSigningOut}
                >
                  <LogOut className='h-4 w-4 mr-2' />
                  {isSigningOut ? 'Signing out...' : 'Sign Out All Devices'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='history' className='space-y-6'>
          {/* Login History */}
          <Card>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <div>
                  <CardTitle className='flex items-center space-x-2'>
                    <History className='h-5 w-5' />
                    <span>Login History</span>
                  </CardTitle>
                  <CardDescription>
                    Track your login attempts and sessions
                  </CardDescription>
                </div>
                <Badge variant='outline' className='text-xs'>
                  Welon Trust Account
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingLoginHistory ? (
                <div className='space-y-3'>
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className='flex items-center space-x-3 p-3 border rounded-lg'
                    >
                      <div className='h-2 w-2 rounded-full bg-gray-300 animate-pulse' />
                      <div className='flex-1 space-y-2'>
                        <div className='h-4 bg-gray-300 rounded animate-pulse w-1/3' />
                        <div className='h-3 bg-gray-300 rounded animate-pulse w-1/2' />
                      </div>
                      <div className='h-3 bg-gray-300 rounded animate-pulse w-20' />
                    </div>
                  ))}
                </div>
              ) : loginHistory && loginHistory.length > 0 ? (
                <div className='space-y-3'>
                  {loginHistory.map((login, index) => (
                    <div
                      key={login.id}
                      className={`flex items-center justify-between p-3 border rounded-lg ${
                        index === 0
                          ? 'border-green-500'
                          : 'border-[var(--custom-gray-light)]'
                      }`}
                    >
                      <div className='flex items-center space-x-3'>
                        <div
                          className={`h-2 w-2 rounded-full ${
                            index === 0
                              ? 'bg-green-500'
                              : 'bg-[var(--custom-gray-medium)]'
                          }`}
                        />
                        <div>
                          <p className='font-medium'>{login.device}</p>
                          <p className='text-sm text-[var(--custom-gray-medium)]'>
                            {login.location} Device
                          </p>
                          <p className='text-xs text-[var(--foreground)] font-medium'>
                            {index === 0
                              ? 'Most recent login'
                              : 'Successful login'}
                          </p>
                        </div>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm text-[var(--custom-gray-medium)]'>
                          {format(new Date(login.timestamp), 'MMM d, yyyy')}
                        </p>
                        <p className='text-xs text-[var(--custom-gray-medium)]'>
                          {format(new Date(login.timestamp), 'h:mm a')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='text-center py-8'>
                  <Monitor className='mx-auto h-12 w-12 text-gray-400' />
                  <h3 className='mt-2 text-sm font-medium text-gray-900'>
                    No login history found
                  </h3>
                  <p className='mt-1 text-sm text-gray-500'>
                    Login history will appear here once available.
                  </p>
                </div>
              )}

              <div className='border-t pt-4 space-y-2'>
                <Button
                  variant='outline'
                  className='w-full'
                  onClick={() => refetchLoginHistory()}
                  disabled={isLoadingLoginHistory}
                >
                  <RefreshCw
                    className={`mr-2 h-4 w-4 ${
                      isLoadingLoginHistory ? 'animate-spin' : ''
                    }`}
                  />
                  Refresh History
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Change Password Dialog */}
      <ChangePasswordDialog
        open={showPasswordDialog}
        onOpenChange={setShowPasswordDialog}
      />
    </div>
  );
}
