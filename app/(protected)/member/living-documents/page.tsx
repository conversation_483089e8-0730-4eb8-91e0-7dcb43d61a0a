'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import routes from '@/utils/routes';
import { useQuery } from '@tanstack/react-query';
import { livingDocumentTemplatesAPI } from '@/lib/api/living-documents';
import { GetIconComponent } from '@/components/dashboard/admin/living-documents-builder/template-table';
import Link from 'next/link';

export default function CreateLivingDocumentPage() {
  const { data: livingDocumentsSidebarList = [], isLoading } = useQuery({
    queryKey: ['living-documents-list'],
    queryFn: livingDocumentTemplatesAPI.getLivingDocumentTemplatesForSidebar,
  });

  // TODO ADD LOADING UI
  if (isLoading) {
    return null;
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-4xl mx-auto'>
        {/* Header */}
        <div className='flex items-center gap-4 mb-8'>
          <div>
            <h1 className='text-3xl font-bold'>Create Living Document</h1>
            <p className='text-[var(--custom-gray-medium)] mt-2'>
              Choose a template or start from scratch to create your living
              document.
            </p>
          </div>
        </div>

        {/* Create Options */}
        <div className='grid gap-6 mb-8'>
          <div>
            {livingDocumentsSidebarList.length > 0 ? (
              <div className='grid gap-4 md:grid-cols-2'>
                {livingDocumentsSidebarList.map(template => (
                  <Link
                    key={template.id}
                    href={routes.member.livingDocumentById(template.id)}
                    className='cursor-pointer hover:shadow-md transition-shadow'
                  >
                    <Card>
                      <CardHeader className='pb-3'>
                        <div className='flex items-center gap-3'>
                          <div className='p-2 bg-green-100 rounded-lg'>
                            <GetIconComponent
                              iconName={template.icon ?? 'file-text'}
                            />
                          </div>
                          <div>
                            <CardTitle className='text-lg'>
                              {template.title}
                            </CardTitle>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className='pt-0'>
                        <p className='text-sm text-[var(--custom-gray-medium)] mb-3'>
                          {template.description}
                        </p>
                        <div className='text-xs text-[var(--custom-gray-medium)]'>
                          Click to use this template
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            ) : (
              <Card className='bg-gray-50 border-gray-200'>
                <CardContent className='p-8 text-center'>
                  <div className='p-4 bg-gray-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center'>
                    <GetIconComponent iconName='file-text' />
                  </div>
                  <h3 className='font-semibold text-gray-900 mb-2'>
                    No Templates Available
                  </h3>
                  <p className='text-sm text-[var(--custom-gray-medium)] mb-4'>
                    There are currently no living document templates available.
                    Templates will appear here once they are created by
                    administrators.
                  </p>
                  <p className='text-xs text-[var(--custom-gray-medium)]'>
                    Check back later or contact support if you need assistance.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Info Section */}
        <Card className='bg-blue-50 border-blue-200'>
          <CardContent className='p-6'>
            <h3 className='font-semibold text-blue-900 mb-2'>
              About Living Documents
            </h3>
            <div className='space-y-2 text-sm text-blue-800'>
              <p>
                • Living documents are designed to be updated regularly to keep
                information current
              </p>
              <p>
                • Set reminder frequencies to ensure you review and update your
                documents on schedule
              </p>
              <p>
                • Templates provide a structured starting point with common
                fields and sections
              </p>
              <p>
                • All documents are securely encrypted and stored in your
                personal vault
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Modal */}
        {/*<LivingDocumentModal*/}
        {/*  open={modalOpen}*/}
        {/*  onOpenChange={setModalOpen}*/}
        {/*  onSave={handleSaveDocument}*/}
        {/*  initialValues={getInitialValues()}*/}
        {/*/>*/}
      </div>
    </div>
  );
}
