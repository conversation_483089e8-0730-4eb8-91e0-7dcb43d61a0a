'use client';

import React, { useState, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { livingDocumentTemplatesAPI } from '@/lib/api/living-documents';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FileText,
  X,
  CheckCircle,
  AlertTriangle,
  Plus,
  Trash2,
} from 'lucide-react';

// Types for the living document structure
interface Question {
  id: string;
  text: string;
  type: 'text' | 'file' | 'textarea' | 'select' | 'radio';
  required: boolean;
  placeholder?: string;
  helpText?: string;
  options?: any[];
  allowMultiple?: boolean;
  order: number;
}

interface LivingDocument {
  id: string;
  title: string;
  icon: string;
  questions: Question[];
  description: string;
}

interface FormAnswer {
  questionId: string;
  values: (string | File)[];
}

interface FormAnswers {
  [questionId: string]: FormAnswer;
}

interface FormErrors {
  [questionId: string]: string;
}

const LivingDocumentPage = () => {
  const params = useParams();
  const templateId = params.id as string;

  // Form state
  const [answers, setAnswers] = useState<FormAnswers>({});
  const [errors, setErrors] = useState<FormErrors>({});

  const {
    data: livingDocumentTemplate,
    isLoading: isLoadingLivingDocumentTemplate,
    error,
  } = useQuery({
    queryKey: ['member-living-document-template', templateId],
    queryFn: () =>
      livingDocumentTemplatesAPI.getLivingDocumentTemplate(templateId),
  });

  // Get answer values for a question
  const getAnswerForQuestion = useCallback(
    (questionId: string): (string | File)[] => {
      const answer = answers[questionId];
      return answer?.values || [''];
    },
    [answers]
  );

  // Update answer values for a question
  const updateAnswer = useCallback(
    (questionId: string, values: (string | File)[]) => {
      setAnswers(prev => ({
        ...prev,
        [questionId]: {
          questionId,
          values: values, // Don't filter out empty strings here to preserve form state
        },
      }));

      // Clear error when user starts typing/selecting
      if (errors[questionId]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[questionId];
          return newErrors;
        });
      }
    },
    [errors]
  );

  // Add multiple answer
  const addMultipleAnswer = useCallback(
    (questionId: string) => {
      const currentValues = getAnswerForQuestion(questionId);
      updateAnswer(questionId, [...currentValues, '']);
    },
    [getAnswerForQuestion, updateAnswer]
  );

  // Remove multiple answer
  const removeMultipleAnswer = useCallback(
    (questionId: string, index: number) => {
      const currentValues = getAnswerForQuestion(questionId);
      const newValues = currentValues.filter((_, i) => i !== index);
      // Ensure we always have at least one empty field
      updateAnswer(questionId, newValues.length > 0 ? newValues : ['']);
    },
    [getAnswerForQuestion, updateAnswer]
  );

  // Handle save function
  const handleSave = useCallback(() => {
    console.log('=== SAVING LIVING DOCUMENT ANSWERS ===');
    console.log('Template ID:', templateId);
    console.log('Document Title:', document?.title);
    console.log('All Answers:', answers);

    // Log each answer in detail
    Object.entries(answers).forEach(([questionId, answer]) => {
      const question = document?.questions.find(q => q.id === questionId);
      console.log(`Question: ${question?.text || questionId}`);
      console.log(`Answer Values:`, answer.values);
      console.log('---');
    });

    console.log('=== END SAVE LOG ===');
  }, [answers, templateId]);

  // Update specific answer in multiple answers
  const updateMultipleAnswer = useCallback(
    (questionId: string, index: number, value: string | File) => {
      const currentValues = getAnswerForQuestion(questionId);
      const newValues = [...currentValues];
      newValues[index] = value;
      updateAnswer(questionId, newValues);
    },
    [getAnswerForQuestion, updateAnswer]
  );

  // File upload handlers
  const handleFileSelect = useCallback(
    (
      questionId: string,
      index: number,
      event: React.ChangeEvent<HTMLInputElement>
    ) => {
      const file = event.target.files?.[0];
      if (file) {
        // Validate file type
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
          setErrors(prev => ({
            ...prev,
            [questionId]: 'Please select a PDF, JPEG, or PNG file.',
          }));
          return;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setErrors(prev => ({
            ...prev,
            [questionId]: 'File size must be less than 10MB.',
          }));
          return;
        }

        updateMultipleAnswer(questionId, index, file);
      }
    },
    [updateMultipleAnswer]
  );

  const handleFileDrop = useCallback(
    (questionId: string, index: number, event: React.DragEvent) => {
      event.preventDefault();
      const file = event.dataTransfer.files[0];
      if (file) {
        // Validate file type
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
          setErrors(prev => ({
            ...prev,
            [questionId]: 'Please select a PDF, JPEG, or PNG file.',
          }));
          return;
        }

        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setErrors(prev => ({
            ...prev,
            [questionId]: 'File size must be less than 10MB.',
          }));
          return;
        }

        updateMultipleAnswer(questionId, index, file);
      }
    },
    [updateMultipleAnswer]
  );

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const removeFile = useCallback(
    (questionId: string, index: number) => {
      const currentValues = getAnswerForQuestion(questionId);
      const newValues = [...currentValues];
      newValues[index] = ''; // Clear the file but keep the slot
      updateAnswer(questionId, newValues);
    },
    [getAnswerForQuestion, updateAnswer]
  );

  // TODO ADD LOADING AND ERROR UI
  if (isLoadingLivingDocumentTemplate) {
    return null;
  }
  if (error) {
    return <div>Error: {error.message}</div>;
  }

  console.log('===> LIVING DOCUMENT TEMPLATE', livingDocumentTemplate);

  const document = livingDocumentTemplate as LivingDocument;

  if (!document || !document.questions) {
    return <div>Document not found</div>;
  }

  return (
    <div className='max-w-4xl mx-auto p-6'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-3'>
            <FileText className='h-6 w-6' />
            {document.title}
          </CardTitle>
          <CardDescription>{document.description}</CardDescription>
        </CardHeader>
        <CardContent>
          <form className='space-y-8'>
            {document.questions
              .sort((a, b) => a.order - b.order)
              .map(question => (
                <QuestionRenderer
                  key={question.id}
                  question={question}
                  values={getAnswerForQuestion(question.id)}
                  error={errors[question.id]}
                  onUpdateAnswer={(index, value) =>
                    updateMultipleAnswer(question.id, index, value)
                  }
                  onAddAnswer={() => addMultipleAnswer(question.id)}
                  onRemoveAnswer={index =>
                    removeMultipleAnswer(question.id, index)
                  }
                  onFileSelect={(index, event) =>
                    handleFileSelect(question.id, index, event)
                  }
                  onFileDrop={(index, event) =>
                    handleFileDrop(question.id, index, event)
                  }
                  onFileRemove={index => removeFile(question.id, index)}
                  onDragOver={handleDragOver}
                />
              ))}

            {/* Save Button */}
            <div className='flex justify-end pt-6 border-t'>
              <Button type='button' onClick={handleSave} className='px-8'>
                Save Document
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

// Question renderer component
interface QuestionRendererProps {
  question: Question;
  values: (string | File)[];
  error?: string;
  onUpdateAnswer: (index: number, value: string | File) => void;
  onAddAnswer: () => void;
  onRemoveAnswer: (index: number) => void;
  onFileSelect: (
    index: number,
    event: React.ChangeEvent<HTMLInputElement>
  ) => void;
  onFileDrop: (index: number, event: React.DragEvent) => void;
  onFileRemove: (index: number) => void;
  onDragOver: (event: React.DragEvent) => void;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  values,
  error,
  onUpdateAnswer,
  onAddAnswer,
  onRemoveAnswer,
  onFileSelect,
  onFileDrop,
  onFileRemove,
  onDragOver,
}) => {
  const renderInput = () => {
    switch (question.type) {
      case 'text':
        return (
          <div className='space-y-3'>
            {values.map((value, index) => (
              <div key={index} className='flex items-center space-x-2'>
                <div className='flex-1'>
                  <Input
                    id={`${question.id}-${index}`}
                    value={(value as string) || ''}
                    onChange={e => onUpdateAnswer(index, e.target.value)}
                    placeholder={question.placeholder || 'Enter your answer...'}
                    className={error ? 'border-red-500' : ''}
                  />
                </div>
                {question.allowMultiple && values.length > 1 && (
                  <Button
                    type='button'
                    variant='ghost'
                    size='sm'
                    onClick={() => onRemoveAnswer(index)}
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                )}
              </div>
            ))}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={onAddAnswer}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another Answer
              </Button>
            )}
          </div>
        );

      case 'file':
        return (
          <div className='space-y-4'>
            {values.map((value, index) => {
              const file = value as File | string;
              const isFile = file instanceof File;
              const hasFile = isFile && file.name;

              return (
                <div key={index} className='flex items-start space-x-2'>
                  <div className='flex-1'>
                    <div
                      className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                        hasFile
                          ? 'border-green-300 bg-green-50'
                          : error
                            ? 'border-red-300 bg-red-50'
                            : 'border-gray-300 hover:border-gray-400'
                      }`}
                      onDragOver={onDragOver}
                      onDrop={e => onFileDrop(index, e)}
                    >
                      {hasFile ? (
                        <div className='flex items-center justify-center gap-2'>
                          <CheckCircle className='h-5 w-5 text-green-600' />
                          <span className='text-green-800 font-medium'>
                            {(file as File).name}
                          </span>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={() => onFileRemove(index)}
                            type='button'
                          >
                            <X className='h-4 w-4' />
                          </Button>
                        </div>
                      ) : (
                        <div>
                          <FileText className='h-8 w-8 text-gray-400 mx-auto mb-2' />
                          <p className='text-gray-600 mb-2'>
                            Drag and drop a file here, or click to select
                          </p>
                          <p className='text-sm text-gray-500'>
                            Supports PDF, JPEG, PNG (max 10MB)
                          </p>
                        </div>
                      )}
                      <input
                        id={`file-${question.id}-${index}`}
                        type='file'
                        accept='.pdf,.jpg,.jpeg,.png'
                        onChange={e => onFileSelect(index, e)}
                        className='hidden'
                      />
                      {!hasFile && (
                        <Button
                          variant='outline'
                          className='mt-2'
                          onClick={() =>
                            document
                              .getElementById(`file-${question.id}-${index}`)
                              ?.click()
                          }
                          type='button'
                        >
                          Select File
                        </Button>
                      )}
                    </div>
                  </div>
                  {question.allowMultiple && values.length > 1 && (
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => onRemoveAnswer(index)}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  )}
                </div>
              );
            })}
            {question.allowMultiple && (
              <Button
                type='button'
                variant='outline'
                size='sm'
                onClick={onAddAnswer}
                className='w-full'
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Another File
              </Button>
            )}
          </div>
        );

      default:
        return (
          <div className='text-gray-500'>
            Unsupported question type: {question.type}
          </div>
        );
    }
  };

  return (
    <div className='space-y-3'>
      <Label htmlFor={question.id} className='text-base font-medium'>
        {question.text}
        {question.required && <span className='text-red-500 ml-1'>*</span>}
      </Label>

      {question.helpText && (
        <p className='text-sm text-gray-600'>{question.helpText}</p>
      )}

      {renderInput()}

      {error && (
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default LivingDocumentPage;
