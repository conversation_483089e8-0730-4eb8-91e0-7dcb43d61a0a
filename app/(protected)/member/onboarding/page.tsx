'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  ChildStatus,
  EstateSize,
  getOnboardingAnswers,
  OnboardingAnswers,
  OnboardingStep,
  saveOnboardingAnswer,
} from '@/app/utils/userOnboarding';
import { ArrowRight, Info, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/app/context/AuthContext';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { useRouter } from 'next/navigation';
import routes from '@/utils/routes';
import { AuthGuard } from '@/lib/auth/auth-guard';

function OnboardingPageContent() {
  const { onboardingStatus, loading, updateOnboardingStatus } = useAuth();
  const router = useRouter();

  const [childStatus, setChildStatus] = useState<ChildStatus | undefined>(
    undefined
  );
  const [estateSize, setEstateSize] = useState<EstateSize | undefined>(
    undefined
  );
  const [savedAnswers, setSavedAnswers] = useState<OnboardingAnswers | null>(
    null
  );
  const [loadingAnswers, setLoadingAnswers] = useState(true);

  // Load existing answers if available
  useEffect(() => {
    async function loadAnswers() {
      try {
        const answers = await getOnboardingAnswers();
        if (answers) {
          setSavedAnswers(answers);
          if (answers.childStatus) {
            setChildStatus(answers.childStatus);
          }
          if (answers.estateSize) {
            setEstateSize(answers.estateSize);
          }
        }
      } catch (error) {
        console.error('Error loading saved answers:', error);
      } finally {
        setLoadingAnswers(false);
      }
    }

    if (!loading) {
      loadAnswers();
    }
  }, [loading]);

  const steps = [
    {
      id: OnboardingStep.CHILD_STATUS,
      label: 'Are you childfree?',
      description:
        "Childfree Legacy is specifically designed for individuals who don't have children and need specialized estate planning.",
    },
    {
      id: OnboardingStep.ESTATE,
      label: 'What is the approximate size of your estate?',
      description: 'Information about your estate planning preferences.',
    },
  ];

  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  useEffect(() => {
    if (onboardingStatus === OnboardingStep.COMPLETED)
      router.push(routes.member.dashboard);
    setCurrentStepIndex(steps.findIndex(step => step.id === onboardingStatus));
  }, [onboardingStatus]);

  const currentStepData = steps[currentStepIndex] || steps[0];

  const progressValue = (currentStepIndex / steps.length) * 100;

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Function to save the current step's answer and move to the next step
  const handleCompleteStep = async () => {
    setIsSubmitting(true);
    try {
      if (currentStepData.id === OnboardingStep.CHILD_STATUS && childStatus) {
        await saveOnboardingAnswer(
          OnboardingStep.CHILD_STATUS,
          childStatus,
          savedAnswers || undefined
        );

        setCurrentStepIndex(prevState => prevState + 1);
        setIsSubmitting(false);
      } else if (currentStepData.id === OnboardingStep.ESTATE && estateSize) {
        await saveOnboardingAnswer(
          OnboardingStep.ESTATE,
          estateSize,
          savedAnswers || undefined
        );
        await updateOnboardingStatus();
        router.push(routes.dashboard);
      }
    } catch (error) {
      console.error('===> Error saving onboarding answer:', error);
    }
  };

  if (loading || loadingAnswers) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-lg'>Loading...</div>
      </div>
    );
  }

  // Check if user is not childfree and show blocking message
  const isNotChildfree = savedAnswers?.childStatus === ChildStatus.NO;

  // Show blocking message if user is not childfree
  if (isNotChildfree) {
    return (
      <div className='min-h-screen bg-background'>
        <div className='container max-w-2xl mx-auto py-10 px-4 sm:px-6'>
          <div className='flex items-center justify-center min-h-[60vh]'>
            <div className='bg-card rounded-lg border shadow-sm p-8 text-center space-y-6'>
              <div className='flex justify-center'>
                <AlertTriangle className='h-16 w-16 text-orange-500' />
              </div>

              <div className='space-y-4'>
                <h1 className='text-2xl font-bold text-foreground'>
                  Access Restricted
                </h1>
                <p className='text-lg text-muted-foreground'>
                  Our website is exclusively designed for childfree individuals.
                </p>
                <p className='text-muted-foreground'>
                  Based on your response, this service may not be suitable for
                  your needs.
                </p>
              </div>

              <div className='bg-muted/50 rounded-lg p-4 space-y-3'>
                <h3 className='font-medium text-foreground'>
                  Need assistance?
                </h3>
                <p className='text-sm text-muted-foreground'>
                  If you believe this is an error or have questions about our
                  services, please contact our support team at{' '}
                  <a
                    href='mailto:<EMAIL>'
                    className='text-primary hover:underline font-medium'
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-background'>
      <div className='container max-w-3xl mx-auto py-10 px-4 sm:px-6'>
        <div className='space-y-6'>
          {/* Header */}
          <div className='text-center space-y-2'>
            <h1 className='text-3xl font-bold'>Complete Your Profile</h1>
            <p className='text-muted-foreground'>
              Follow these steps to complete your profile setup
            </p>
          </div>

          {/* Content */}
          <div className='bg-card rounded-lg border shadow-sm p-6'>
            {/* Progress Bar */}
            <div className='mb-6'>
              <Progress value={progressValue} className='h-2' />
            </div>

            {/* Current Step Content */}
            <div className='rounded-lg border p-6 bg-muted/50 mb-8'>
              <div className='flex items-start gap-4 mb-6'>
                <Info className='h-6 w-6 text-primary mt-0.5 flex-shrink-0' />
                <div>
                  <h3 className='text-xl font-medium mb-2'>
                    {currentStepData.label}
                  </h3>
                  <p className='text-muted-foreground'>
                    {currentStepData.description}
                  </p>
                </div>
              </div>
              {currentStepData.id === OnboardingStep.CHILD_STATUS && (
                <div className='mt-6 space-y-4'>
                  <RadioGroup
                    value={childStatus as string}
                    onValueChange={(value: string) =>
                      setChildStatus(value as ChildStatus)
                    }
                    className='space-y-3'
                  >
                    <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${childStatus === 'yes' ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={ChildStatus.YES}
                        id='childfree-yes'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='childfree-yes'
                        className='flex-1 cursor-pointer text-base'
                      >
                        Yes, I am childfree
                      </Label>
                    </div>
                    <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${childStatus === 'no' ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={ChildStatus.NO}
                        id='childfree-no'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='childfree-no'
                        className='flex-1 cursor-pointer text-base'
                      >
                        No, I have children
                      </Label>
                    </div>
                    <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${childStatus === 'planning' ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={ChildStatus.PLANNING}
                        id='childfree-planning'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='childfree-planning'
                        className='flex-1 cursor-pointer text-base'
                      >
                        I'm planning to remain childfree
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              )}

              {currentStepData.id === OnboardingStep.ESTATE && (
                <div className='mt-6 space-y-4'>
                  <RadioGroup
                    value={estateSize as string}
                    onValueChange={(value: string) =>
                      setEstateSize(value as EstateSize)
                    }
                    className='space-y-3'
                  >
                    <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${estateSize === EstateSize.UNDER_100K ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={EstateSize.UNDER_100K}
                        id='estate-under100k'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='estate-under100k'
                        className='flex-1 cursor-pointer text-base'
                      >
                        Under $100,000
                      </Label>
                    </div>
                    <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${estateSize === EstateSize.BETWEEN_100K_500K ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={EstateSize.BETWEEN_100K_500K}
                        id='estate-100k-500k'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='estate-100k-500k'
                        className='flex-1 cursor-pointer text-base'
                      >
                        $100,000 - $500,000
                      </Label>
                    </div>
                    <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${estateSize === EstateSize.BETWEEN_500K_1M ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={EstateSize.BETWEEN_500K_1M}
                        id='estate-500k-1m'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='estate-500k-1m'
                        className='flex-1 cursor-pointer text-base'
                      >
                        $500,000 - $1 million
                      </Label>
                    </div>
                    <div
                      className={`flex items-center space-x-3 rounded-md border p-4 transition-colors ${estateSize === EstateSize.OVER_1M ? 'bg-accent border-primary' : 'hover:bg-accent/50'}`}
                    >
                      <RadioGroupItem
                        value={EstateSize.OVER_1M}
                        id='estate-over1m'
                        className='h-5 w-5'
                      />
                      <Label
                        htmlFor='estate-over1m'
                        className='flex-1 cursor-pointer text-base'
                      >
                        Over $1 million
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              )}
            </div>

            {/* Action Button */}
            <div className='flex justify-end'>
              <Button
                onClick={handleCompleteStep}
                disabled={
                  loading ||
                  loadingAnswers ||
                  isSubmitting ||
                  (currentStepData.id === OnboardingStep.CHILD_STATUS &&
                    !childStatus) ||
                  (currentStepData.id === OnboardingStep.ESTATE && !estateSize)
                }
                size='lg'
                className='w-full sm:w-auto'
              >
                {isSubmitting ? 'Processing...' : 'Continue'}
                {!isSubmitting && <ArrowRight className='ml-2 h-5 w-5' />}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function OnboardingPage() {
  return (
    <AuthGuard fallbackMessage='You need to be logged in to access onboarding.'>
      <OnboardingPageContent />
    </AuthGuard>
  );
}
