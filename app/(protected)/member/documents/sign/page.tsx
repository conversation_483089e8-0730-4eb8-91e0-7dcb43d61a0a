'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Document } from '@/types/documents';
import {
  ArrowLeft,
  Download,
  Printer,
  FileText,
  Truck,
  CheckCircle,
  AlertCircle,
  MapPin,
  Clock,
  Shield,
  Eye,
  RefreshCw,
  PenTool,
} from 'lucide-react';
import routes from '@/utils/routes';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getDocumentById,
  signDocument,
  DocumentResponse,
} from '@/lib/api/documents';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';

function DocumentSignContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const documentId = searchParams.get('id');

  const [acknowledged, setAcknowledged] = useState(false);
  const [signatureType, setSignatureType] = useState<
    'manual' | 'electronic' | 'notarized'
  >('electronic');

  // Query for real document data
  const {
    data: realDocument,
    isLoading: realDocumentLoading,
    error: realDocumentError,
    refetch: refetchDocument,
  } = useQuery({
    queryKey: ['document', documentId],
    queryFn: () => getDocumentById(documentId!),
    enabled: !!documentId && !!user,
    refetchOnWindowFocus: false,
  });

  // Mutation for signing document
  const signDocumentMutation = useMutation({
    mutationFn: ({
      documentId,
      signatureType,
    }: {
      documentId: string;
      signatureType: 'manual' | 'electronic' | 'notarized';
    }) => signDocument(documentId, signatureType),
    onSuccess: data => {
      toast.success('Document signed successfully!');

      // Invalidate and refetch documents
      queryClient.invalidateQueries({ queryKey: ['user-documents'] });
      queryClient.invalidateQueries({ queryKey: ['document', documentId] });

      // Navigate back to documents after a short delay
      setTimeout(() => {
        router.push(routes.documentsManage);
      }, 2000);
    },
    onError: (error: Error) => {
      toast.error(`Failed to sign document: ${error.message}`);
    },
  });

  // Convert DocumentResponse to Document interface
  function convertDocumentResponse(docResponse: DocumentResponse): Document {
    return {
      id: docResponse.id,
      title: docResponse.title,
      type: docResponse.type as Document['type'],
      status: docResponse.status as Document['status'],
      dateCreated: docResponse.dateCreated,
      lastModified: docResponse.lastModified || docResponse.dateCreated,
      version: docResponse.version,
      content: docResponse.content,
      userId: docResponse.userId,
      fileUrl: docResponse.fileUrl,
      trackingNumber: docResponse.trackingNumber,
      signatureType: docResponse.signatureType as Document['signatureType'],
      executionDate: docResponse.executionDate,
    };
  }

  // Determine document and current state
  const document = realDocument ? convertDocumentResponse(realDocument) : null;
  const loading = realDocumentLoading;
  const error = realDocumentError;

  const handleDownloadToReview = () => {
    if (!document) return;
    // In real implementation, this would trigger actual download
    alert(`Downloading ${document.title} for review...`);
  };

  const handleSignDocument = async () => {
    if (!document || !acknowledged || !documentId) return;

    // Sign the document
    signDocumentMutation.mutate({ documentId, signatureType });
  };

  const handleBackToManage = () => {
    router.push(routes.documentsManage);
  };

  const handlePreviewDocument = () => {
    if (!document) return;
    router.push(`${routes.documentsReview}?id=${document.id}&mode=preview`);
  };

  // Handle missing document ID
  if (!documentId) {
    return (
      <Alert className='max-w-md mx-auto' variant='destructive'>
        <AlertCircle className='h-4 w-4' />
        <AlertDescription>No document ID provided</AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading your document...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center max-w-md'>
          <div className='text-red-500 text-6xl mb-4'>⚠️</div>
          <h3 className='text-lg font-medium text-red-800 mb-2'>
            Error Loading Document
          </h3>
          <p className='text-red-600 mb-4'>
            {error instanceof Error
              ? error.message
              : 'Failed to load the document. Please try again.'}
          </p>
          <div className='flex gap-2 justify-center'>
            <Button
              onClick={() => window.location.reload()}
              variant='outline'
              className='border-red-300 text-red-700 hover:bg-red-50'
            >
              Try Again
            </Button>
            <Button
              onClick={() => refetchDocument()}
              variant='outline'
              disabled={realDocumentLoading}
              className='border-blue-300 text-blue-700 hover:bg-blue-50'
            >
              <RefreshCw className='h-4 w-4 mr-2' />
              {realDocumentLoading ? 'Refreshing...' : 'Refresh'}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!document) {
    return (
      <Alert className='max-w-md mx-auto' variant='destructive'>
        <AlertCircle className='h-4 w-4' />
        <AlertDescription>Document not found</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleBackToManage}
            className='flex items-center gap-2'
          >
            <ArrowLeft className='h-4 w-4' />
            Back to Documents
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)]'>
              Sign Document
            </h1>
            <p className='text-muted-foreground mt-1'>
              Complete the signing process for your estate planning document
            </p>
          </div>
        </div>
        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => refetchDocument()}
            disabled={realDocumentLoading}
            className='flex items-center gap-2'
          >
            <RefreshCw className='h-4 w-4' />
            {realDocumentLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Badge className='bg-green-100 text-green-800'>
            READY FOR SIGNING
          </Badge>
        </div>
      </div>

      {/* Document Info */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            {document.title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
            <div>
              <span className='text-muted-foreground'>Document Type:</span>
              <div className='font-medium'>{document.type}</div>
            </div>
            <div>
              <span className='text-muted-foreground'>Version:</span>
              <div className='font-medium'>v{document.version}</div>
            </div>
            <div>
              <span className='text-muted-foreground'>Status:</span>
              <Badge variant='outline' className='ml-1'>
                {document.status}
              </Badge>
            </div>
            <div>
              <span className='text-muted-foreground'>Created:</span>
              <div className='font-medium'>
                {new Date(document.dateCreated).toLocaleDateString()}
              </div>
            </div>
            <div>
              <span className='text-muted-foreground'>Last Modified:</span>
              <div className='font-medium'>
                {new Date(document.lastModified).toLocaleDateString()}
              </div>
            </div>
            {document.signatureType && (
              <div>
                <span className='text-muted-foreground'>
                  Current Signature:
                </span>
                <div className='font-medium capitalize'>
                  {document.signatureType}
                </div>
              </div>
            )}
            {document.executionDate && (
              <div>
                <span className='text-muted-foreground'>Execution Date:</span>
                <div className='font-medium'>
                  {new Date(document.executionDate).toLocaleDateString()}
                </div>
              </div>
            )}
            <div>
              <span className='text-muted-foreground'>Notarization:</span>
              <div className='font-medium'>
                {document.notarizationRequired ? 'Required' : 'Not Required'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Signing Options */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        {/* Option 1: Download to Review */}
        <Card className='border-blue-200'>
          <CardHeader>
            <CardTitle className='flex items-center gap-2 text-blue-900'>
              <Eye className='h-5 w-5' />
              Download to Review
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <p className='text-sm text-blue-800'>
              Download the document to review its contents before proceeding
              with the execution process. This will not generate shipping labels
              or instructions.
            </p>
            <div className='bg-blue-50 p-3 rounded-lg'>
              <p className='text-xs text-blue-700'>
                💡 <strong>Note:</strong> Use this option to preview your
                document. No shipping materials will be generated.
              </p>
            </div>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                onClick={handleDownloadToReview}
                className='flex items-center gap-2 border-blue-300 text-blue-700 hover:bg-blue-50'
              >
                <Download className='h-4 w-4' />
                Download to Review
              </Button>
              <Button
                variant='outline'
                onClick={handlePreviewDocument}
                className='flex items-center gap-2 border-blue-300 text-blue-700 hover:bg-blue-50'
              >
                <Eye className='h-4 w-4' />
                Preview Online
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Document Signing */}
        <Card className='border-green-200 bg-green-50'>
          <CardHeader>
            <CardTitle className='text-green-900 flex items-center gap-2'>
              <PenTool className='h-5 w-5' />
              Sign Document
            </CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='bg-amber-50 border border-amber-200 rounded-lg p-4'>
              <p className='text-sm text-amber-800'>
                ⚠️ <strong>Important:</strong> Signing this document will mark
                it as executed and update its status to "signed".
              </p>
            </div>

            {/* Signature Type Selection */}
            <div className='space-y-3'>
              <label className='text-sm font-medium text-green-800'>
                Select Signature Type:
              </label>
              <div className='space-y-2'>
                <div className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    id='electronic'
                    name='signatureType'
                    value='electronic'
                    checked={signatureType === 'electronic'}
                    onChange={e =>
                      setSignatureType(e.target.value as 'electronic')
                    }
                    className='text-green-600'
                  />
                  <label
                    htmlFor='electronic'
                    className='text-sm text-green-800 cursor-pointer'
                  >
                    Electronic Signature (Digital)
                  </label>
                </div>
                <div className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    id='manual'
                    name='signatureType'
                    value='manual'
                    checked={signatureType === 'manual'}
                    onChange={e => setSignatureType(e.target.value as 'manual')}
                    className='text-green-600'
                  />
                  <label
                    htmlFor='manual'
                    className='text-sm text-green-800 cursor-pointer'
                  >
                    Manual Signature (Handwritten)
                  </label>
                </div>
                <div className='flex items-center space-x-2'>
                  <input
                    type='radio'
                    id='notarized'
                    name='signatureType'
                    value='notarized'
                    checked={signatureType === 'notarized'}
                    onChange={e =>
                      setSignatureType(e.target.value as 'notarized')
                    }
                    className='text-green-600'
                  />
                  <label
                    htmlFor='notarized'
                    className='text-sm text-green-800 cursor-pointer'
                  >
                    Notarized Signature (Notary Required)
                  </label>
                </div>
              </div>
            </div>

            <div className='flex items-start space-x-2'>
              <Checkbox
                id='acknowledge'
                checked={acknowledged}
                onCheckedChange={checked => setAcknowledged(checked as boolean)}
              />
              <label
                htmlFor='acknowledge'
                className='text-sm text-green-800 leading-relaxed cursor-pointer'
              >
                I acknowledge that I am ready to sign this document and
                understand that this action will mark the document as executed.
              </label>
            </div>

            <Button
              onClick={handleSignDocument}
              disabled={!acknowledged || signDocumentMutation.isPending}
              className='w-full bg-green-600 hover:bg-green-700'
            >
              {signDocumentMutation.isPending ? (
                <>
                  <Clock className='h-4 w-4 mr-2 animate-spin' />
                  Signing Document...
                </>
              ) : (
                <>
                  <PenTool className='h-4 w-4 mr-2' />
                  Sign Document ({signatureType})
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Process Overview */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Shield className='h-5 w-5' />
            Signing Process Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='text-center p-4 bg-blue-50 rounded-lg'>
              <Printer className='h-8 w-8 text-blue-600 mx-auto mb-2' />
              <h3 className='font-semibold text-blue-900 mb-1'>1. Print</h3>
              <p className='text-xs text-blue-800'>
                Print the complete package on letter-sized paper, single-sided
              </p>
            </div>
            <div className='text-center p-4 bg-purple-50 rounded-lg'>
              <FileText className='h-8 w-8 text-purple-600 mx-auto mb-2' />
              <h3 className='font-semibold text-purple-900 mb-1'>
                2. Sign & Notarize
              </h3>
              <p className='text-xs text-purple-800'>
                Sign the document and have it notarized by a notary public
              </p>
            </div>
            <div className='text-center p-4 bg-orange-50 rounded-lg'>
              <Truck className='h-8 w-8 text-orange-600 mx-auto mb-2' />
              <h3 className='font-semibold text-orange-900 mb-1'>3. Ship</h3>
              <p className='text-xs text-orange-800'>
                Use the prepaid UPS label to ship to Welon Trust
              </p>
            </div>
            <div className='text-center p-4 bg-green-50 rounded-lg'>
              <CheckCircle className='h-8 w-8 text-green-600 mx-auto mb-2' />
              <h3 className='font-semibold text-green-900 mb-1'>4. Complete</h3>
              <p className='text-xs text-green-800'>
                Receive confirmation when your document is processed
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Important Notes */}
      <Alert>
        <AlertCircle className='h-4 w-4' />
        <AlertDescription>
          <strong>Important Notes:</strong>
          <ul className='mt-2 space-y-1 text-sm'>
            <li>
              • Notarization fees typically range from $5-$15 (paid by you)
            </li>
            <li>• Use libraries or print shops if you don't have a printer</li>
            <li>
              • The prepaid label covers standard shipping; upgrades are at your
              expense
            </li>
            <li>
              • You'll receive email updates when your document is shipped,
              received, and processed
            </li>
          </ul>
        </AlertDescription>
      </Alert>

      {/* Find Notary */}
      <Card className='border-amber-200 bg-amber-50'>
        <CardContent className='p-4'>
          <h3 className='font-semibold text-amber-900 mb-2 flex items-center gap-2'>
            <MapPin className='h-4 w-4' />
            Need to Find a Notary?
          </h3>
          <p className='text-sm text-amber-800 mb-3'>
            Use these resources to find a notary public in your area:
          </p>
          <div className='flex flex-wrap gap-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                window.open(
                  'https://www.nationalnotary.org/find-a-notary',
                  '_blank'
                )
              }
              className='border-amber-300 text-amber-700 hover:bg-amber-100'
            >
              National Notary Association
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                window.open('https://www.notarycafe.com', '_blank')
              }
              className='border-amber-300 text-amber-700 hover:bg-amber-100'
            >
              Notary Cafe
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function DocumentSignPage() {
  return (
    <Suspense
      fallback={
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      }
    >
      <DocumentSignContent />
    </Suspense>
  );
}
