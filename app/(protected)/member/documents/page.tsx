'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DocumentStatusCard } from '@/components/documents/document-status-card';
import { Document } from '@/types/documents';
import { FileText, Search, Filter, Plus, Eye, PenTool } from 'lucide-react';
import routes from '@/utils/routes';
import { useUserContextOptional } from '@/components/welon-trust/user-context';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  getUserDocuments,
  getWelonTrustDocuments,
  getDocumentsForSelectedUser,
  DocumentResponse,
  resubmitDocument,
  sendDocumentToWelon,
} from '@/lib/api/documents';
import { useAuth } from '@/context/AuthContext';
import { isWelonTrust } from '@/lib/utils/admin-utils';
import {
  buildFullHtml,
  generateAndDownloadPDF,
  generatePdfFilename,
} from '@/lib/utils/pdf-generation';
import { toast } from 'sonner';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

function DocumentsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { selectedUser } = useUserContextOptional();
  const { user, userRoles } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [showUploadModal, setShowUploadModal] = useState(false);

  //LOADING STATES
  const [isGeneratingPdf, setIsGeneratingPdf] = useState<boolean>(false);
  const [resubmittingDocuments, setResubmittingDocuments] = useState<
    Set<string>
  >(new Set());
  const [shippingDocuments, setShippingDocuments] = useState<Set<string>>(
    new Set()
  );

  // Determine if this is a Welon Trust user using the utility function
  const isWelonTrustUser = isWelonTrust(userRoles);

  // Determine which function to use based on user role and selected user
  const getQueryFunction = () => {
    if (!isWelonTrustUser) {
      return getUserDocuments;
    }

    // For Welon Trust users, check if a user is selected
    if (selectedUser) {
      return () => getDocumentsForSelectedUser(selectedUser.id);
    }

    // Default to all assigned users' documents
    return getWelonTrustDocuments;
  };

  // Query for real documents - use different function based on user role and selected user
  const {
    data: realDocuments,
    isLoading: realDocumentsLoading,
    error: realDocumentsError,
    refetch: refetchDocuments,
  } = useQuery({
    queryKey: isWelonTrustUser
      ? selectedUser
        ? ['selected-user-documents', selectedUser.id]
        : ['welon-trust-documents']
      : ['user-documents'],
    queryFn: getQueryFunction(),
    enabled: !!user, // Only fetch for authenticated users
    refetchOnWindowFocus: false,
  });

  // Mutation for resubmitting rejected documents
  const resubmitMutation = useMutation({
    mutationFn: (docId: string) => resubmitDocument(docId),
  });

  // Handle URL filter parameter
  useEffect(() => {
    const filterParam = searchParams.get('filter');
    if (filterParam && filterParam !== 'all') {
      setStatusFilter(filterParam);
    }
  }, [searchParams]);

  // Load real documents from database
  useEffect(() => {
    if (realDocuments) {
      // Convert real documents from database
      const convertedDocuments: Document[] = realDocuments.map(
        (doc: DocumentResponse) => ({
          id: doc.id,
          title: doc.title,
          type: doc.type as Document['type'],
          status: doc.status as Document['status'],
          dateCreated: doc.dateCreated,
          lastModified: doc.lastModified || doc.dateCreated,
          version: doc.version,
          content: doc.content,
          userId: doc.userId,
          fileUrl: doc.fileUrl,
          trackingNumber: doc.trackingNumber,
          signatureType: doc.signatureType as Document['signatureType'],
          executionDate: doc.executionDate,
          rejectionReason: doc.rejectionReason,
        })
      );

      setDocuments(convertedDocuments);
      setFilteredDocuments(convertedDocuments);
    } else if (!realDocuments && !realDocumentsLoading) {
      // No documents yet
      setDocuments([]);
      setFilteredDocuments([]);
    }
  }, [realDocuments, realDocumentsLoading]);

  // Determine loading state
  const loading = realDocumentsLoading;

  // Filter documents based on search and filters
  useEffect(() => {
    let filtered = documents;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(
        doc =>
          doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doc.type.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(doc => doc.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(doc => doc.type === typeFilter);
    }

    setFilteredDocuments(filtered);
  }, [documents, searchTerm, statusFilter, typeFilter]);

  const handlePreview = (document: Document) => {
    router.push(`${routes.documentsReview}?id=${document.id}&mode=preview`);
  };

  const handleReview = (document: Document) => {
    router.push(`${routes.documentsReview}?id=${document.id}`);
  };

  const handleSign = (document: Document) => {
    router.push(`${routes.documentsSign}?id=${document.id}`);
  };

  const handleResubmit = async (document: Document) => {
    try {
      // Add document to resubmitting set
      setResubmittingDocuments(prev => new Set(prev).add(document.id));

      await resubmitMutation.mutateAsync(document.id);

      toast.success(
        `Document "${document.title}" has been resubmitted for review`
      );
      refetchDocuments(); // Refresh document data to show updated status
    } catch (error) {
      console.error('Error resubmitting document:', error);
      toast.error('Failed to resubmit document. Please try again.');
    } finally {
      // Remove document from resubmitting set
      setResubmittingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handleShip = async (document: Document) => {
    try {
      // Add document to shipping set
      setShippingDocuments(prev => new Set(prev).add(document.id));

      // Get current user
      const currentUser = await getCurrentUser();

      // Get current user data
      const { data: userList, errors: listErrors } =
        await client.models.User.list({
          filter: { cognitoId: { eq: currentUser.userId } },
          selectionSet: [
            'id',
            'cognitoId',
            'firstName',
            'lastName',
            'phoneNumber',
            'assignedWelonTrustId',
          ],
        });

      if (listErrors) {
        console.error('❌ User query errors:', listErrors);
        throw new Error('Failed to fetch user data');
      }

      if (!userList || userList.length === 0) {
        console.error('❌ No user found with cognitoId:', currentUser.userId);
        throw new Error('User not found');
      }

      const userData = userList[0];

      const { data: userAddresses, errors: userAddressErrors } =
        await client.models.UserAddress.list({
          filter: { userId: { eq: userData.id } },
        });

      if (userAddressErrors) {
        console.error('❌ User address query errors:', userAddressErrors);
        throw new Error('Failed to fetch user address');
      }

      const userAddress =
        userAddresses?.find(addr => addr.isDefault) || userAddresses?.[0];
      if (!userAddress) {
        console.error('❌ No address found for user:', userData.id);
        throw new Error(
          'User address not found. Please save your address first.'
        );
      }

      if (!userData.assignedWelonTrustId) {
        console.error(
          '❌ No assignedWelonTrustId found for user:',
          userData.id
        );
        throw new Error('No assigned Welon Trust found');
      }

      let welonUser = null;

      const { data: welonUserList, errors: welonErrors } =
        await client.models.User.list({
          filter: { cognitoId: { eq: userData.assignedWelonTrustId } },
          selectionSet: [
            'id',
            'cognitoId',
            'firstName',
            'lastName',
            'phoneNumber',
            'role',
          ],
        });

      if (welonErrors) {
        console.error('❌ Method 1 errors:', welonErrors);
      }

      welonUser = welonUserList?.[0];

      // Second try: Search by ID (fallback if assignedWelonTrustId stores ID instead of cognitoId)
      if (!welonUser) {
        const { data: welonUserById, errors: welonByIdErrors } =
          await client.models.User.get(
            {
              id: userData.assignedWelonTrustId,
            },
            {
              selectionSet: [
                'id',
                'cognitoId',
                'firstName',
                'lastName',
                'phoneNumber',
                'role',
              ],
            }
          );

        if (welonByIdErrors) {
          console.error('❌ Method 2 errors:', welonByIdErrors);
        }

        welonUser = welonUserById;
      }

      if (!welonUser) {
        console.error(
          '❌ No Welon Trust user found with either cognitoId or ID:',
          userData.assignedWelonTrustId
        );

        // Debug: Check all WelonTrust users
        const { data: allWelonUsers } = await client.models.User.list({
          filter: { role: { eq: 'WelonTrust' } },
          selectionSet: ['id', 'cognitoId', 'firstName', 'lastName', 'role'],
        });

        throw new Error('Assigned Welon Trust user not found');
      }

      const { data: welonAddresses, errors: welonAddressErrors } =
        await client.models.UserAddress.list({
          filter: { userId: { eq: welonUser.id } },
        });

      if (welonAddressErrors) {
        console.error(
          '❌ Welon Trust address query errors:',
          welonAddressErrors
        );
        throw new Error('Failed to fetch Welon Trust address');
      }

      const welonAddress =
        welonAddresses?.find(addr => addr.isDefault) || welonAddresses?.[0];
      if (!welonAddress) {
        console.error(
          '❌ No address found for Welon Trust user:',
          welonUser.id
        );
        throw new Error('Welon Trust address not found');
      }

      // Prepare addresses for UPS API
      const fromAddress = {
        name: `${userData.firstName} ${userData.lastName}`,
        phone: userData.phoneNumber || '',
        addressLine1: userAddress.addressLine1,
        addressLine2: userAddress.addressLine2 || '',
        city: userAddress.city,
        stateProvinceCode: userAddress.stateProvinceCode,
        postalCode: userAddress.postalCode,
        countryCode: userAddress.countryCode,
      };

      const toAddress = {
        name: `${welonUser.firstName} ${welonUser.lastName}`,
        phone: welonUser.phoneNumber || '',
        addressLine1: welonAddress.addressLine1,
        addressLine2: welonAddress.addressLine2 || '',
        city: welonAddress.city,
        stateProvinceCode: welonAddress.stateProvinceCode,
        postalCode: welonAddress.postalCode,
        countryCode: welonAddress.countryCode,
      };

      // Create UPS shipping label
      const upsResponse = await fetch('/api/ups/create-label', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromAddress,
          toAddress,
          serviceCode: '03', // UPS Ground
          packageOptions: {
            description: 'Legal Documents',
            weight: '1',
          },
        }),
      });

      if (!upsResponse.ok) {
        const errorData = await upsResponse.json();
        throw new Error('Failed to create shipping label: ' + errorData.error);
      }

      const upsResponse_data = await upsResponse.json();

      // Extract the actual data from the API response
      const upsResult = upsResponse_data.data || upsResponse_data;

      const shippingLabelData: any = {
        userId: userData.id,
        assignedWelonTrustId: welonUser.id, // Add WelonTrust ID so they can see the package
        trackingNumber: upsResult.trackingNumber,
        labelUrl: upsResult.labelUrl,
        fromAddress: JSON.stringify(fromAddress),
        toAddress: JSON.stringify(toAddress),
        serviceCode: '03',
        cost: JSON.stringify(upsResult.cost),
        status: 'created',
        createdAt: new Date().toISOString(),
      };

      // Only add optional fields if they have values
      if (document.id) {
        shippingLabelData.documentId = document.id;
      }
      if (upsResult.estimatedDelivery) {
        shippingLabelData.estimatedDelivery = upsResult.estimatedDelivery;
      }

      const { data: shippingLabel, errors: labelErrors } =
        await client.models.ShippingLabel.create(shippingLabelData);

      if (labelErrors) {
        console.error('Error saving shipping label:', labelErrors);
        throw new Error('Failed to save shipping label');
      }

      // Update document status to 'shipped'
      const { errors: docErrors } = await client.models.Document.update({
        id: document.id,
        status: 'shipped',
        trackingNumber: upsResult.trackingNumber,
      });

      if (docErrors) {
        console.error('Error updating document status:', docErrors);
      }

      toast.success(
        `Document "${document.title}" has been shipped! Tracking: ${upsResult.trackingNumber}`
      );
      refetchDocuments(); // Refresh document data to show updated status
    } catch (error) {
      console.error('Error shipping document:', error);
      toast.error(
        'Failed to ship document: ' +
          (error instanceof Error ? error.message : 'Unknown error')
      );
    } finally {
      // Remove document from shipping set
      setShippingDocuments(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const handleDownload = async (document: Document) => {
    try {
      setIsGeneratingPdf(true);
      const fullHtmlContent = buildFullHtml(document);
      const filename = generatePdfFilename(document);
      await generateAndDownloadPDF(fullHtmlContent, {
        filename,
        format: 'A4',
        orientation: 'portrait',
        margin: {
          top: '2cm',
          right: '1.5cm',
          bottom: '2cm',
          left: '1.5cm',
        },
        printBackground: true,
        scale: 1,
      });
      toast.success('Downloading started successfully.');
    } catch (error) {
      console.error(error);
      toast.error('Something went wrong.');
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const handleUploadDocument = () => {
    if (!selectedUser) {
      alert('Please select a member first');
      return;
    }
    setShowUploadModal(true);
  };

  const handleUploadSuccess = (newDocument: Document) => {
    // Add the new document to the list
    const updatedDocuments = [...documents, newDocument];
    setDocuments(updatedDocuments);
    setFilteredDocuments(updatedDocuments);
    alert(`Document "${newDocument.title}" uploaded successfully!`);
  };

  const handleUpdateStatus = (
    document: Document,
    newStatus: Document['status']
  ) => {
    // In real implementation, this would call API to update status
    const updatedDocuments = documents.map(doc =>
      doc.id === document.id
        ? { ...doc, status: newStatus, lastModified: new Date().toISOString() }
        : doc
    );
    setDocuments(updatedDocuments);
    alert(`Document status updated to: ${newStatus}`);
  };

  // Map database status to display status for counting
  const mapStatusForCounting = (status: string) => {
    switch (status) {
      case 'draft':
        return 'ready_for_review';
      case 'signed':
        return 'ready_for_signing';
      case 'shipped':
        return 'signed';
      case 'received':
        return 'approved';
      case 'archived':
        return 'archived';
      default:
        return status;
    }
  };

  const getStatusCounts = () => {
    if (isWelonTrustUser) {
      // For Welon Trust users, all documents are signed, so show simpler counts
      return {
        total: documents.length,
        ready_for_review: 0,
        ready_for_signing: 0,
        signed: documents.length, // All documents are signed for Welon Trust
        approved: 0,
      };
    }

    const counts = {
      total: documents.length,
      ready_for_review: documents.filter(
        d => mapStatusForCounting(d.status) === 'ready_for_review'
      ).length,
      ready_for_signing: documents.filter(
        d => mapStatusForCounting(d.status) === 'ready_for_signing'
      ).length,
      signed: documents.filter(d => mapStatusForCounting(d.status) === 'signed')
        .length,
      approved: documents.filter(
        d => mapStatusForCounting(d.status) === 'approved'
      ).length,
    };
    return counts;
  };

  const statusCounts = getStatusCounts();
  const filterParam = searchParams.get('filter');
  const isFiltered = filterParam && filterParam !== 'all';

  const getFilterTitle = () => {
    if (isWelonTrustUser) {
      if (selectedUser) {
        return `Signed Documents - ${selectedUser.name}`;
      }
      return 'Signed Documents - Assigned Members';
    }
    if (filterParam === 'ready_for_review') return 'Documents Ready for Review';
    if (filterParam === 'ready_for_signing')
      return 'Documents Ready for Signing';
    return 'Document Management';
  };

  const getFilterDescription = () => {
    if (isWelonTrustUser) {
      if (selectedUser) {
        return `View signed documents from ${selectedUser.name}`;
      }
      return 'View signed documents from members assigned to you';
    }
    if (filterParam === 'ready_for_review')
      return 'Review your documents before proceeding to signing';
    if (filterParam === 'ready_for_signing')
      return 'Sign your approved documents';
    return 'Manage your estate planning documents';
  };

  if (loading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading your documents...</p>
        </div>
      </div>
    );
  }

  // Show error state for documents query
  if (realDocumentsError) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <div className='text-red-500 text-6xl mb-4'>⚠️</div>
          <h3 className='text-lg font-medium text-red-800 mb-2'>
            Error Loading Documents
          </h3>
          <p className='text-red-600 mb-4'>
            {realDocumentsError instanceof Error
              ? realDocumentsError.message
              : 'Failed to load your documents. Please try again.'}
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant='outline'
            className='border-red-300 text-red-700 hover:bg-red-50'
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // // Show error state for real documents query
  // if (!isWelonTrust && realDocumentsError) {
  //   return (
  //     <div className='flex items-center justify-center min-h-[400px]'>
  //       <div className='text-center'>
  //         <div className='text-red-500 text-6xl mb-4'>⚠️</div>
  //         <h3 className='text-lg font-medium text-red-800 mb-2'>
  //           Error Loading Documents
  //         </h3>
  //         <p className='text-red-600 mb-4'>
  //           {realDocumentsError instanceof Error
  //             ? realDocumentsError.message
  //             : 'Failed to load your documents. Please try again.'}
  //         </p>
  //         <Button
  //           onClick={() => window.location.reload()}
  //           variant='outline'
  //           className='border-red-300 text-red-700 hover:bg-red-50'
  //         >
  //           Try Again
  //         </Button>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div id={'documents-to-print'} className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold text-[var(--foreground)]'>
            {getFilterTitle()}
          </h1>
          <p className='text-muted-foreground mt-1'>{getFilterDescription()}</p>
          {isFiltered && (
            <div className='mt-2'>
              <Badge
                variant='outline'
                className='bg-blue-50 text-blue-700 border-blue-200'
              >
                Filtered from Dashboard
              </Badge>
            </div>
          )}
        </div>
        <div className='flex items-center gap-2'>
          {isFiltered && (
            <Button
              variant='outline'
              onClick={() => router.push(routes.documentsManage)}
              className='flex items-center gap-2'
            >
              <Filter className='h-4 w-4' />
              Clear Filter
            </Button>
          )}
          <Button
            variant='outline'
            onClick={() => refetchDocuments()}
            disabled={realDocumentsLoading}
            className='flex items-center gap-2'
          >
            <Search className='h-4 w-4' />
            {realDocumentsLoading ? 'Refreshing...' : 'Refresh'}
          </Button>
          {!isWelonTrustUser && (
            <Button
              onClick={() => router.push('/dashboard/member/documents/create')}
              className='flex items-center gap-2'
            >
              <Plus className='h-4 w-4' />
              Create New Document
            </Button>
          )}
        </div>
      </div>

      {/* Quick Actions for Filtered Views */}
      {isFiltered && (
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
          <h3 className='font-semibold text-blue-900 mb-2'>Quick Actions</h3>
          <div className='flex flex-wrap gap-2'>
            {filterParam === 'ready_for_review' && (
              <>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() => {
                    const firstDoc = filteredDocuments.find(
                      d => d.status === 'ready_for_review'
                    );
                    if (firstDoc) handleReview(firstDoc);
                  }}
                  disabled={
                    !filteredDocuments.some(
                      d => d.status === 'ready_for_review'
                    )
                  }
                  className='border-blue-300 text-blue-700 hover:bg-blue-100'
                >
                  <Eye className='h-4 w-4 mr-2' />
                  Review First Document
                </Button>
              </>
            )}
            {filterParam === 'ready_for_signing' && (
              <>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={() => {
                    const firstDoc = filteredDocuments.find(
                      d => d.status === 'ready_for_signing'
                    );
                    if (firstDoc) handleSign(firstDoc);
                  }}
                  disabled={
                    !filteredDocuments.some(
                      d => d.status === 'ready_for_signing'
                    )
                  }
                  className='border-green-300 text-green-700 hover:bg-green-100'
                >
                  <PenTool className='h-4 w-4 mr-2' />
                  Sign First Document
                </Button>
              </>
            )}
          </div>
        </div>
      )}

      {/* Status Overview */}
      <div
        className={`grid grid-cols-1 gap-4 ${isWelonTrustUser ? 'md:grid-cols-2' : 'md:grid-cols-5'}`}
      >
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>
                {statusCounts.total}
              </div>
              <div className='text-sm text-muted-foreground'>
                {isWelonTrustUser
                  ? selectedUser
                    ? `${selectedUser.name}'s Signed Documents`
                    : 'Total Signed Documents'
                  : 'Total Documents'}
              </div>
            </div>
          </CardContent>
        </Card>
        {!isWelonTrustUser && (
          <>
            <Card>
              <CardContent className='p-4'>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-orange-600'>
                    {statusCounts.ready_for_review}
                  </div>
                  <div className='text-sm text-muted-foreground'>
                    Ready for Review
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className='p-4'>
                <div className='text-center'>
                  <div className='text-2xl font-bold text-green-600'>
                    {statusCounts.ready_for_signing}
                  </div>
                  <div className='text-sm text-muted-foreground'>
                    Ready for Signing
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-purple-600'>
                {statusCounts.signed}
              </div>
              <div className='text-sm text-muted-foreground'>
                {isWelonTrustUser
                  ? selectedUser
                    ? `From ${selectedUser.name}`
                    : 'From Assigned Members'
                  : 'Signed'}
              </div>
            </div>
          </CardContent>
        </Card>
        {!isWelonTrustUser && (
          <Card>
            <CardContent className='p-4'>
              <div className='text-center'>
                <div className='text-2xl font-bold text-emerald-600'>
                  {statusCounts.approved}
                </div>
                <div className='text-sm text-muted-foreground'>Approved</div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Filter className='h-5 w-5' />
            Filter Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder='Search documents...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Filter by status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Statuses</SelectItem>
                <SelectItem value='draft'>Draft</SelectItem>
                <SelectItem value='ready_for_review'>
                  Ready for Review
                </SelectItem>
                <SelectItem value='ready_for_signing'>
                  Ready for Signing
                </SelectItem>
                <SelectItem value='signed'>Signed</SelectItem>
                <SelectItem value='shipped'>Shipped</SelectItem>
                <SelectItem value='received'>Received</SelectItem>
                <SelectItem value='approved'>Approved</SelectItem>
                <SelectItem value='archived'>Archived</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder='Filter by type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Types</SelectItem>
                <SelectItem value='Will'>Will</SelectItem>
                <SelectItem value='Trust'>Trust</SelectItem>
                <SelectItem value='POA'>Power of Attorney</SelectItem>
                <SelectItem value='HealthcareDirective'>
                  Healthcare Directive
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Documents List */}
      <div className='space-y-4'>
        {filteredDocuments.length === 0 ? (
          <Card>
            <CardContent className='p-8 text-center'>
              <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
              <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
                No documents found
              </h3>
              <p className='text-muted-foreground mb-4'>
                {documents.length === 0
                  ? isWelonTrustUser
                    ? selectedUser
                      ? `No signed documents available from ${selectedUser.name}.`
                      : 'No signed documents available from your assigned members.'
                    : "You haven't created any documents yet."
                  : 'Try adjusting your search or filter criteria.'}
              </p>
              {documents.length === 0 && !isWelonTrustUser && (
                <Button
                  onClick={() =>
                    router.push('/dashboard/member/documents/create')
                  }
                  className='flex items-center gap-2'
                >
                  <Plus className='h-4 w-4' />
                  Create Your First Document
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredDocuments.map(document => (
            <DocumentStatusCard
              key={document.id}
              document={document}
              isDownloadingPdf={isGeneratingPdf}
              isResubmitting={resubmittingDocuments.has(document.id)}
              isShipping={shippingDocuments.has(document.id)}
              onPreview={() => handlePreview(document)}
              onReview={() => handleReview(document)}
              onSign={() => handleSign(document)}
              onDownload={() => handleDownload(document)}
              onResubmit={() => handleResubmit(document)}
              onShip={() => handleShip(document)}
            />
          ))
        )}
      </div>
    </div>
  );
}

export default function DocumentsPage() {
  return (
    <Suspense
      fallback={
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
            <p className='text-muted-foreground'>Loading...</p>
          </div>
        </div>
      }
    >
      <DocumentsContent />
    </Suspense>
  );
}
