'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Shield,
  FileText,
  AlertTriangle,
  Eye,
  Users,
  Info,
} from 'lucide-react';
import { Headline } from '@/components/ui/brand/typography';
import { Subhead } from '@workspace/ui/brand';

export default function LinkedAccountDashboard() {
  const router = useRouter();

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-6xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>Linked Account Dashboard</Headline>
          <Subhead className='text-muted-foreground'>
            You have limited access to shared documents and emergency features
          </Subhead>
        </div>

        {/* Access Level Info */}
        <Alert className='mb-8 border-blue-200 bg-blue-50'>
          <Info className='h-4 w-4 text-blue-600' />
          <AlertTitle className='text-blue-800'>
            Limited Access Account
          </AlertTitle>
          <AlertDescription className='text-blue-700'>
            Your access is limited to specific documents and emergency features
            as configured by the account owner.
          </AlertDescription>
        </Alert>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {/* Shared Documents */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <FileText className='h-5 w-5' />
                Shared Documents
              </CardTitle>
              <CardDescription>
                Documents you have permission to view
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                  <div>
                    <p className='font-medium'>Joint Trust Agreement</p>
                    <p className='text-sm text-muted-foreground'>
                      Last updated: Jan 15, 2025
                    </p>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Badge variant='outline'>View Only</Badge>
                    <Button size='sm' variant='outline'>
                      <Eye className='h-4 w-4 mr-2' />
                      View
                    </Button>
                  </div>
                </div>

                <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                  <div>
                    <p className='font-medium'>Shared Property Deed</p>
                    <p className='text-sm text-muted-foreground'>
                      Last updated: Dec 20, 2024
                    </p>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Badge variant='outline'>View Only</Badge>
                    <Button size='sm' variant='outline'>
                      <Eye className='h-4 w-4 mr-2' />
                      View
                    </Button>
                  </div>
                </div>
              </div>

              <div className='mt-4'>
                <Button
                  variant='outline'
                  className='w-full'
                  onClick={() =>
                    router.push('/dashboard/member/shared-documents')
                  }
                >
                  View All Shared Documents
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Emergency Access */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Shield className='h-5 w-5' />
                Emergency Access
              </CardTitle>
              <CardDescription>
                Emergency contacts and access features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                <div className='flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200'>
                  <div>
                    <p className='font-medium text-green-800'>
                      Emergency Access Active
                    </p>
                    <p className='text-sm text-green-600'>
                      You can access emergency documents
                    </p>
                  </div>
                  <Button
                    size='sm'
                    onClick={() => router.push('/emergency/documents')}
                  >
                    Access
                  </Button>
                </div>

                <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                  <div>
                    <p className='font-medium'>Emergency Contacts</p>
                    <p className='text-sm text-muted-foreground'>
                      View emergency contact list
                    </p>
                  </div>
                  <Button size='sm' variant='outline'>
                    <Users className='h-4 w-4 mr-2' />
                    View
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Dead Man's Switch */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <AlertTriangle className='h-5 w-5' />
                Dead Man's Switch
              </CardTitle>
              <CardDescription>
                Automated safety monitoring status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                <div className='flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200'>
                  <div>
                    <p className='font-medium text-blue-800'>
                      Monitoring Active
                    </p>
                    <p className='text-sm text-blue-600'>
                      Last check-in: 2 days ago
                    </p>
                  </div>
                  <Badge
                    variant='outline'
                    className='text-blue-700 border-blue-300'
                  >
                    Active
                  </Badge>
                </div>

                <div className='text-sm text-muted-foreground'>
                  <p>
                    You can view the status of the automated safety monitoring
                    system.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Users className='h-5 w-5' />
                Account Information
              </CardTitle>
              <CardDescription>Your linked account details</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                <div className='flex justify-between'>
                  <span className='text-sm font-medium'>Account Type:</span>
                  <Badge variant='secondary'>Linked Account</Badge>
                </div>
                <div className='flex justify-between'>
                  <span className='text-sm font-medium'>Relationship:</span>
                  <span className='text-sm'>Spouse/Partner</span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-sm font-medium'>Access Level:</span>
                  <Badge variant='outline'>Limited</Badge>
                </div>
                <div className='flex justify-between'>
                  <span className='text-sm font-medium'>Linked Since:</span>
                  <span className='text-sm'>December 2024</span>
                </div>
              </div>

              <div className='mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200'>
                <p className='text-sm text-yellow-800'>
                  <strong>Note:</strong> Your access permissions are managed by
                  the primary account holder. Contact them if you need
                  additional access.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className='mt-8'>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks for linked accounts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <Button
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
                onClick={() => router.push('/emergency/documents')}
              >
                <Shield className='h-6 w-6' />
                <span>Emergency Access</span>
              </Button>

              <Button
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
                onClick={() =>
                  router.push('/dashboard/member/shared-documents')
                }
              >
                <FileText className='h-6 w-6' />
                <span>View Documents</span>
              </Button>

              <Button
                variant='outline'
                className='h-auto p-4 flex flex-col items-center gap-2'
                onClick={() =>
                  router.push('/dashboard/member/emergency-contacts')
                }
              >
                <Users className='h-6 w-6' />
                <span>Emergency Contacts</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
