'use client';

import React, { useState } from 'react';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  BookOpen,
  Video,
  FileText,
  Search,
  Clock,
  Play,
  Image,
  Bot,
  HelpCircle,
  Monitor,
} from 'lucide-react';
import { ContentStatus } from '@/types/education';
import { YouTubeThumbnail } from '@/components/education/youtube-thumbnail';

// Simple video player component for modal without title duplication
interface SimpleVideoPlayerProps {
  video: any;
  autoPlay?: boolean;
}

type PlayerSize = 'small' | 'medium' | 'large';

function SimpleVideoPlayer({
  video,
  autoPlay = false,
}: SimpleVideoPlayerProps) {
  const [playerSize, setPlayerSize] = useState<PlayerSize>('large');

  // Extract YouTube video ID from URL
  const getYouTubeVideoId = (url: string) => {
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : null;
  };

  // Get YouTube embed URL
  const getYouTubeEmbedUrl = (url: string) => {
    const videoId = getYouTubeVideoId(url);
    if (videoId) {
      const autoplayParam = autoPlay ? '&autoplay=1' : '';
      return `https://www.youtube.com/embed/${videoId}?rel=0${autoplayParam}&modestbranding=1&showinfo=0`;
    }
    return url;
  };

  // Get container classes based on player size
  const getContainerClasses = () => {
    switch (playerSize) {
      case 'small':
        return 'w-full max-w-md mx-auto';
      case 'medium':
        return 'w-full max-w-2xl mx-auto';
      case 'large':
        return 'w-full max-w-4xl mx-auto';
      default:
        return 'w-full max-w-4xl mx-auto';
    }
  };

  // Get size label
  const getSizeLabel = () => {
    switch (playerSize) {
      case 'small':
        return 'Small';
      case 'medium':
        return 'Medium';
      case 'large':
        return 'Large';
      default:
        return 'Large';
    }
  };

  // Cycle through sizes
  const cycleSizes = () => {
    const sizes: PlayerSize[] = ['small', 'medium', 'large'];
    const currentIndex = sizes.indexOf(playerSize);
    const nextIndex = (currentIndex + 1) % sizes.length;
    setPlayerSize(sizes[nextIndex]);
  };

  if (!video.contentUrl) {
    return (
      <div className='flex items-center justify-center h-64 bg-muted rounded-lg'>
        <p className='text-muted-foreground'>Video not available</p>
      </div>
    );
  }

  return (
    <div className='flex flex-col space-y-4 w-full'>
      {/* Size Controls Only */}
      <div className='flex items-center justify-end'>
        <div className='flex items-center space-x-2'>
          <span className='text-sm text-muted-foreground'>
            Size: {getSizeLabel()}
          </span>
          <Button
            variant='outline'
            size='sm'
            onClick={cycleSizes}
            className='flex items-center space-x-1'
          >
            <Monitor className='h-4 w-4' />
            <span>Resize</span>
          </Button>
        </div>
      </div>

      {/* Video Player */}
      <div className={getContainerClasses()}>
        <div className='relative w-full aspect-video rounded-lg overflow-hidden bg-black'>
          <iframe
            src={getYouTubeEmbedUrl(video.contentUrl)}
            title={video.title}
            className='w-full h-full'
            allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
            allowFullScreen
          />
        </div>
      </div>

      {/* Duration Info */}
      {video.duration && (
        <div className='text-center'>
          <p className='text-sm text-muted-foreground'>
            Duration: {Math.floor(video.duration / 60)}:
            {(video.duration % 60).toString().padStart(2, '0')}
          </p>
        </div>
      )}
    </div>
  );
}

export default function EducationalContentPage() {
  const { content, isLoading } = useEducationalContent();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  // Filter content based on search and category
  const filteredContent = content.filter(item => {
    const matchesSearch =
      searchTerm === '' ||
      item.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory =
      selectedCategory === 'all' || item.type === selectedCategory;

    // Only show published content on the main page
    const isPublished = item.status === ContentStatus.PUBLISHED;
    return matchesSearch && matchesCategory && isPublished;
  });

  // Group content by type
  const contentByType = {
    videos: filteredContent.filter(item => item.type === 'video'),
    articles: filteredContent.filter(item => item.type === 'article'),
    infographics: filteredContent.filter(item => item.type === 'infographic'),
    avatars: filteredContent.filter(item => item.type === 'avatar'),
    tooltips: filteredContent.filter(item => item.type === 'tooltip'),
  };

  // Get icon for content type
  const getContentIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className='h-5 w-5' />;
      case 'article':
        return <FileText className='h-5 w-5' />;
      case 'infographic':
        return <Image className='h-5 w-5' />;
      case 'avatar':
        return <Bot className='h-5 w-5' />;
      case 'tooltip':
        return <HelpCircle className='h-5 w-5' />;
      default:
        return <BookOpen className='h-5 w-5' />;
    }
  };

  // Format duration for videos
  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Format reading time for articles
  const formatReadingTime = (minutes?: number) => {
    if (!minutes) return 'N/A';
    return `${minutes} min read`;
  };

  // Get thumbnail for content (simplified since YouTube videos use dedicated component)
  const getThumbnail = (item: any) => {
    // If there's a custom thumbnail URL, use it first
    if (item.thumbnailUrl) {
      return item.thumbnailUrl;
    }

    // For infographics, use content URL if it's an image
    if (item.type === 'infographic' && item.contentUrl) {
      return item.contentUrl;
    }

    // For other content types with contentUrl
    if (item.contentUrl) {
      return item.contentUrl;
    }

    // Default placeholder
    return `data:image/svg+xml,<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="%23f3f4f6"/><text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="%239ca3af" text-anchor="middle" dy=".3em">${item.type || 'Content'}</text></svg>`;
  };

  // Handle video play
  const handleVideoPlay = (video: any) => {
    setSelectedVideo(video);
    setIsVideoModalOpen(true);
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4'></div>
          <p>Loading educational content...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 space-y-8'>
      <div className='flex flex-col space-y-2'>
        <h1 className='text-3xl font-bold'>Educational Content</h1>
        <p className='text-muted-foreground'>
          Learn about estate planning, wills, trusts, and more through our
          comprehensive educational resources.
        </p>
      </div>

      {/* Search and Filter */}
      <div className='flex flex-col sm:flex-row gap-4'>
        <div className='relative flex-1'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
          <Input
            placeholder='Search educational content...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='pl-10'
          />
        </div>
        <Select
          value={selectedCategory}
          onValueChange={(value: string) => setSelectedCategory(value)}
        >
          <SelectTrigger className='w-full sm:w-48'>
            <SelectValue placeholder='Filter by type' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>All Types</SelectItem>
            <SelectItem value='video'>Videos</SelectItem>
            <SelectItem value='ARTICLE' disabled className='opacity-50'>
              Articles (In development)
            </SelectItem>
            <SelectItem value='INFOGRAPHIC' disabled className='opacity-50'>
              Infographics (In development)
            </SelectItem>
            <SelectItem value='AVATAR' disabled className='opacity-50'>
              Interactive Guides (In development)
            </SelectItem>
            <SelectItem value='TOOLTIP' disabled className='opacity-50'>
              Quick Tips (In development)
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Content Tabs */}
      <Tabs defaultValue='all' className='w-full'>
        <TooltipProvider>
          <TabsList className='grid w-full grid-cols-6'>
            <TabsTrigger value='all'>All</TabsTrigger>

            <TabsTrigger value='videos'>Videos</TabsTrigger>

            <Tooltip>
              <TooltipTrigger asChild>
                <TabsTrigger
                  value='articles'
                  disabled
                  className='cursor-not-allowed opacity-50'
                >
                  Articles
                </TabsTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>In development</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <TabsTrigger
                  value='infographics'
                  disabled
                  className='cursor-not-allowed opacity-50'
                >
                  Infographics
                </TabsTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>In development</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <TabsTrigger
                  value='avatars'
                  disabled
                  className='cursor-not-allowed opacity-50'
                >
                  Guides
                </TabsTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>In development</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <TabsTrigger
                  value='tooltips'
                  disabled
                  className='cursor-not-allowed opacity-50'
                >
                  Tips
                </TabsTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>In development</p>
              </TooltipContent>
            </Tooltip>
          </TabsList>
        </TooltipProvider>

        {/* All Content */}
        <TabsContent value='all' className='mt-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {filteredContent.length === 0 ? (
              <div className='col-span-full text-center py-12'>
                <BookOpen className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
                <h3 className='text-lg font-semibold mb-2'>
                  No content available
                </h3>
                <p className='text-muted-foreground'>
                  {content.length === 0
                    ? 'No educational content has been published yet. Check back later for new resources.'
                    : 'Try adjusting your search terms or filters.'}
                </p>
              </div>
            ) : (
              filteredContent.map(item => (
                <Card
                  key={item.id}
                  className='hover:shadow-lg transition-shadow'
                >
                  {/* Thumbnail */}
                  {item.type === 'video' && item.contentUrl ? (
                    <YouTubeThumbnail
                      videoUrl={item.contentUrl}
                      title={item.title}
                      duration={item.duration || undefined}
                      className='w-full h-48 overflow-hidden rounded-t-lg'
                      onClick={() => handleVideoPlay(item)}
                    />
                  ) : (
                    <div
                      className='relative w-full h-48 overflow-hidden rounded-t-lg bg-muted cursor-pointer'
                      onClick={() => {
                        if (item.type === 'video') {
                          handleVideoPlay(item);
                        } else {
                          window.location.href = `/dashboard/education/${item.id}`;
                        }
                      }}
                    >
                      <img
                        src={getThumbnail(item)}
                        alt={item.title}
                        className='w-full h-full object-cover'
                        crossOrigin='anonymous'
                        loading='lazy'
                        onError={e => {
                          const target = e.target as HTMLImageElement;
                          target.src = `data:image/svg+xml,<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="%23f3f4f6"/><text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="%239ca3af" text-anchor="middle" dy=".3em">${item.type || 'Content'}</text></svg>`;
                        }}
                      />
                      {item.type === 'video' && (
                        <div className='absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 hover:bg-opacity-50 transition-all'>
                          <Play className='h-12 w-12 text-white drop-shadow-lg' />
                        </div>
                      )}
                      {item.type === 'video' && item.duration && (
                        <div className='absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded'>
                          {formatDuration(item.duration)}
                        </div>
                      )}
                    </div>
                  )}
                  <CardHeader>
                    <div className='flex items-center space-x-2 mb-3'>
                      {getContentIcon(item.type || '')}
                      <Badge variant='secondary' className='capitalize'>
                        {item.type?.toLowerCase()}
                      </Badge>
                    </div>
                    <CardTitle className='text-lg'>{item.title}</CardTitle>
                    <CardDescription>{item.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className='flex items-center text-sm text-muted-foreground mb-4'>
                      <Clock className='h-4 w-4 mr-2' />
                      <span>
                        {item.type === 'video'
                          ? formatDuration(item.duration || 0)
                          : item.type === 'article'
                            ? formatReadingTime(item.readingTime || 0)
                            : 'Quick view'}
                      </span>
                    </div>
                    <div className='flex space-x-2'>
                      <Button
                        className='flex-1'
                        onClick={() => {
                          if (item.type === 'video') {
                            handleVideoPlay(item);
                          } else {
                            // Navigate to content page for other types
                            window.location.href = `/dashboard/education/${item.id}`;
                          }
                        }}
                      >
                        {item.type === 'video' && (
                          <Play className='h-4 w-4 mr-2' />
                        )}
                        {item.type === 'article' && (
                          <BookOpen className='h-4 w-4 mr-2' />
                        )}
                        {item.type === 'infographic' && (
                          <Image className='h-4 w-4 mr-2' />
                        )}
                        {item.type === 'avatar' && (
                          <Bot className='h-4 w-4 mr-2' />
                        )}
                        {item.type === 'tooltip' && (
                          <HelpCircle className='h-4 w-4 mr-2' />
                        )}
                        {item.type === 'video'
                          ? 'Watch'
                          : item.type === 'article'
                            ? 'Read'
                            : item.type === 'infographic'
                              ? 'View'
                              : item.type === 'avatar'
                                ? 'Start'
                                : 'View'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        {/* Videos Tab */}
        <TabsContent value='videos' className='mt-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {contentByType.videos.map(video => (
              <Card
                key={video.id}
                className='hover:shadow-lg transition-shadow'
              >
                {/* Video Thumbnail */}
                {video.contentUrl ? (
                  <YouTubeThumbnail
                    videoUrl={video.contentUrl}
                    title={video.title}
                    duration={video.duration || undefined}
                    className='w-full h-48 overflow-hidden rounded-t-lg'
                    onClick={() => handleVideoPlay(video)}
                  />
                ) : (
                  <div className='relative w-full h-48 overflow-hidden rounded-t-lg bg-muted flex items-center justify-center'>
                    <div className='text-center p-4'>
                      <Play className='h-12 w-12 text-muted-foreground mx-auto mb-2' />
                      <p className='text-sm text-muted-foreground'>
                        No video URL
                      </p>
                    </div>
                  </div>
                )}
                <CardHeader>
                  <Badge variant='secondary' className='mb-3'>
                    Video
                  </Badge>
                  <CardTitle className='text-lg'>{video.title}</CardTitle>
                  <CardDescription>{video.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='flex items-center text-sm text-muted-foreground mb-4'>
                    <Clock className='h-4 w-4 mr-2' />
                    <span>{formatDuration(video.duration || 0)}</span>
                  </div>
                  <div className='flex space-x-2'>
                    <Button
                      className='flex-1'
                      onClick={() => handleVideoPlay(video)}
                    >
                      <Play className='h-4 w-4 mr-2' />
                      Watch Video
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Articles Tab */}
        <TabsContent value='articles' className='mt-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {contentByType.articles.map(article => (
              <Card
                key={article.id}
                className='hover:shadow-lg transition-shadow'
              >
                {/* Article Thumbnail */}
                <div className='relative w-full h-48 overflow-hidden rounded-t-lg'>
                  <img
                    src={getThumbnail(article)}
                    alt={article.title}
                    className='w-full h-full object-cover'
                    onError={e => {
                      (e.target as HTMLImageElement).src =
                        `data:image/svg+xml,<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="%23f3f4f6"/><text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="%239ca3af" text-anchor="middle" dy=".3em">300 × 200</text></svg>`;
                    }}
                  />
                  <div className='absolute inset-0 flex items-center justify-center bg-black bg-opacity-20'>
                    <FileText className='h-12 w-12 text-white' />
                  </div>
                </div>
                <CardHeader>
                  <Badge variant='secondary' className='mb-3'>
                    Article
                  </Badge>
                  <CardTitle className='text-lg'>{article.title}</CardTitle>
                  <CardDescription>{article.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='flex items-center justify-between text-sm text-muted-foreground mb-4'>
                    <div className='flex items-center space-x-2'>
                      <Clock className='h-4 w-4' />
                      <span>{formatReadingTime(article.readingTime || 0)}</span>
                    </div>
                  </div>
                  <div className='flex space-x-2'>
                    <Button
                      className='flex-1'
                      onClick={() =>
                        (window.location.href = `/dashboard/education/${article.id}`)
                      }
                    >
                      <BookOpen className='h-4 w-4 mr-2' />
                      Read Article
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Infographics Tab */}
        <TabsContent value='infographics' className='mt-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {contentByType.infographics.map(item => (
              <Card key={item.id} className='hover:shadow-lg transition-shadow'>
                {/* Infographic Thumbnail */}
                <div className='relative w-full h-48 overflow-hidden rounded-t-lg'>
                  <img
                    src={getThumbnail(item)}
                    alt={item.title}
                    className='w-full h-full object-cover'
                    onError={e => {
                      (e.target as HTMLImageElement).src =
                        `data:image/svg+xml,<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="%23f3f4f6"/><text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="%239ca3af" text-anchor="middle" dy=".3em">300 × 200</text></svg>`;
                    }}
                  />
                  <div className='absolute inset-0 flex items-center justify-center bg-black bg-opacity-20'>
                    <Image className='h-12 w-12 text-white' />
                  </div>
                </div>
                <CardHeader>
                  <Badge variant='secondary' className='mb-3'>
                    Infographic
                  </Badge>
                  <CardTitle className='text-lg'>{item.title}</CardTitle>
                  <CardDescription>{item.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className='w-full'>
                    <Image className='h-4 w-4 mr-2' />
                    View Infographic
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Guides Tab */}
        <TabsContent value='avatars' className='mt-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {contentByType.avatars.map(item => (
              <Card key={item.id} className='hover:shadow-lg transition-shadow'>
                {/* Avatar/Guide Thumbnail */}
                <div className='relative w-full h-48 overflow-hidden rounded-t-lg'>
                  <img
                    src={getThumbnail(item)}
                    alt={item.title}
                    className='w-full h-full object-cover'
                    onError={e => {
                      (e.target as HTMLImageElement).src =
                        '/api/placeholder/300/200';
                    }}
                  />
                  <div className='absolute inset-0 flex items-center justify-center bg-black bg-opacity-20'>
                    <Bot className='h-12 w-12 text-white' />
                  </div>
                </div>
                <CardHeader>
                  <Badge variant='secondary' className='mb-3'>
                    Interactive Guide
                  </Badge>
                  <CardTitle className='text-lg'>{item.title}</CardTitle>
                  <CardDescription>{item.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className='w-full'>
                    <Bot className='h-4 w-4 mr-2' />
                    Start Guide
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Tips Tab */}
        <TabsContent value='tooltips' className='mt-6'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {contentByType.tooltips.map(item => (
              <Card key={item.id} className='hover:shadow-lg transition-shadow'>
                {/* Tooltip Thumbnail */}
                <div className='relative w-full h-48 overflow-hidden rounded-t-lg'>
                  <img
                    src={getThumbnail(item)}
                    alt={item.title}
                    className='w-full h-full object-cover'
                    onError={e => {
                      (e.target as HTMLImageElement).src =
                        '/api/placeholder/300/200';
                    }}
                  />
                  <div className='absolute inset-0 flex items-center justify-center bg-black bg-opacity-20'>
                    <HelpCircle className='h-12 w-12 text-white' />
                  </div>
                </div>
                <CardHeader>
                  <Badge variant='secondary' className='mb-3'>
                    Quick Tip
                  </Badge>
                  <CardTitle className='text-lg'>{item.title}</CardTitle>
                  <CardDescription>{item.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className='w-full'>
                    <HelpCircle className='h-4 w-4 mr-2' />
                    View Tip
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Video Modal */}
      <Dialog open={isVideoModalOpen} onOpenChange={setIsVideoModalOpen}>
        <DialogContent
          className='max-h-[90vh]'
          style={{ maxWidth: '55vw', width: '55vw' }}
        >
          <DialogHeader>
            <DialogTitle>{selectedVideo?.title}</DialogTitle>
            <DialogDescription>{selectedVideo?.description}</DialogDescription>
          </DialogHeader>
          <div className='w-full'>
            {selectedVideo && (
              <SimpleVideoPlayer video={selectedVideo} autoPlay={true} />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
