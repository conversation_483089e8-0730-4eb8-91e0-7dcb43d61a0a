'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import routes from '@/utils/routes';
import {
  EmergencyContact,
  DMSConfiguration,
} from '@/components/emergency/types';
import { UserDocumentsList } from '@/app/components/user-documents/user-documents-list';
import { useAuth } from '@/app/context/AuthContext';
import { useEmergencyContacts } from '@/hooks/useEmergencyContacts';
import { useDeadMansSwitch } from '@/hooks/useDeadMansSwitch';
import { useQuery } from '@tanstack/react-query';
import { getUserDocuments } from '@/lib/api/documents';
import { fetchUserProfileByCognitoId } from '@/lib/data/users';

interface LivingDocument {
  id: string;
  title: string;
  type: string;
  description: string;
  content: string;
  createdAt: string;
  lastUpdated: string;
  nextReview: string;
  reviewFrequency: string;
  status: 'current' | 'review-soon' | 'overdue';
}

export default function DashboardPage() {
  const router = useRouter();
  const { user } = useAuth();

  // Fetch real emergency contacts
  const { contacts, loading: contactsLoading } = useEmergencyContacts();

  // Fetch real DMS configuration
  const { config: dmsConfig, loading: dmsLoading } = useDeadMansSwitch();

  // Fetch user documents
  const { data: documents = [], isLoading: documentsLoading } = useQuery({
    queryKey: ['user-documents'],
    queryFn: getUserDocuments,
    enabled: !!user,
  });

  // Fetch user profile
  const { data: userProfile } = useQuery({
    queryKey: ['userProfile', user?.userId],
    queryFn: () => fetchUserProfileByCognitoId(user?.userId || ''),
    enabled: !!user?.userId,
  });

  const [, setLivingDocuments] = useState<LivingDocument[]>([]);

  // Helper function to format dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Helper function to get check-in interval description
  const getCheckInInterval = (
    frequency?: string,
    customDays?: number | null
  ) => {
    if (!frequency) return '';

    switch (frequency) {
      case 'WEEKLY':
        return '7 days';
      case 'BIWEEKLY':
        return '14 days';
      case 'MONTHLY':
        return '30 days';
      case 'CUSTOM':
        return customDays ? `${customDays} days` : 'Custom interval';
      default:
        return frequency.toLowerCase();
    }
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <h1 className='text-3xl font-bold mb-8 text-[var(--foreground)]'>
        Welcome to Your Dashboard
      </h1>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* User Documents Section - Real documents assigned by Welon Trust */}
        <div className='lg:col-span-2'>
          <UserDocumentsList />
        </div>

        {/* Estate Planning Documents Section */}
        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Your Documents</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Manage your estate planning documents
              </p>
            </div>
            <Button
              variant='default'
              size='lg'
              onClick={() => router.push(routes.member.interview)}
            >
              Start Interview
            </Button>
          </div>

          {/* Real document status */}
          <div className='space-y-2 mb-3 flex-grow'>
            {documentsLoading ? (
              <div className='bg-[var(--background)] border-l-4 border-gray-400 p-2 rounded-r-lg text-sm'>
                <p>📄 Loading documents...</p>
              </div>
            ) : documents.length > 0 ? (
              <div className='bg-[var(--eggplant)]/10 border-l-4 border-[var(--eggplant)] p-2 rounded-r-lg text-sm'>
                <p>
                  📄 {documents.length} document
                  {documents.length !== 1 ? 's' : ''} available
                </p>
              </div>
            ) : (
              <div className='bg-[var(--lemon)]/10 border-l-4 border-[var(--lemon)] p-2 rounded-r-lg text-sm'>
                <p>
                  📄 No documents yet. Start your interview to create documents.
                </p>
              </div>
            )}
          </div>

          <div className='grid grid-cols-1 gap-2 mt-auto'>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push(routes.member.documents)}
            >
              Manage Documents
            </Button>
            {/* <Button
              variant='outline'
              size='default'
              onClick={() => router.push('/dashboard/member/documents/create')}
            >
              Create New
            </Button>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push(routes.documentsManageReview)}
            >
              Review Documents
            </Button>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push(routes.documentsManageSign)}
            >
              Sign Documents
            </Button> */}
          </div>
        </div>

        {/* Shared Documents Section */}
        {/* <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Shared Documents</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Collaborate on joint estate planning
              </p>
            </div>
            <Button
              variant='outline'
              size='default'
              onClick={() => router.push('/dashboard/member/shared-documents')}
            >
              View All
            </Button>
          </div>

          <div className='bg-blue-50 border-l-4 border-blue-400 p-2 rounded-r-lg mb-3 text-sm flex-grow'>
            <p>
              💡 Link accounts to create shared documents like joint trusts.
            </p>
          </div>

          <div className='flex gap-2 mt-auto'>
            <Button
              variant='secondary'
              size='default'
              className='flex-1'
              onClick={() =>
                router.push('/dashboard/member/settings/linked-accounts')
              }
            >
              Link Account
            </Button>
            <Button
              variant='default'
              size='default'
              className='flex-1'
              onClick={() => router.push('/dashboard/member/shared-documents')}
            >
              Shared Docs
            </Button>
          </div>
        </div> */}

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Emergency Contacts</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Manage who can access your information
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push(routes.member.emergencyContacts)}
            >
              {contacts.length > 0 ? 'Manage' : 'Add'} Contacts
            </Button>
          </div>

          {contacts.length > 0 ? (
            <div className='bg-[var(--lime)]/20 border-l-4 border-[var(--lime)] p-2 rounded-r-lg text-sm'>
              <p>
                ✅ You've added {contacts.length} emergency contact
                {contacts.length !== 1 ? 's' : ''}.
              </p>
            </div>
          ) : (
            <div className='bg-[var(--lemon)]/10 border-l-4 border-[var(--lemon)] p-2 rounded-r-lg text-sm'>
              <p>⚠️ No emergency contacts added yet.</p>
            </div>
          )}

          {contacts.length > 0 && contacts[0] && (
            <div className='mt-2'>
              <div className='flex items-center p-2 bg-gray-50 rounded-lg border border-gray-200'>
                <div className='w-6 h-6 rounded-full bg-dark-blue  text-[var(--off-black)] flex items-center justify-center mr-2 text-xs'>
                  {contacts[0].fullName.charAt(0)}
                </div>
                <div>
                  <p className='font-medium text-[var(--off-black)] text-sm'>
                    {contacts[0].fullName}
                  </p>
                  <p className='text-xs text-[var(--off-black)]'>
                    {contacts[0].relationship}
                  </p>
                </div>
                {contacts[0].isPrimaryForType && (
                  <span className='ml-auto bg-success-green text-white text-xs px-2 py-0.5 rounded-full'>
                    Primary
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Dead Man's Switch</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Automated safety check system
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push(routes.member.deadMansSwitch)}
            >
              {dmsLoading
                ? 'Loading...'
                : dmsConfig
                  ? dmsConfig.status === 'ACTIVE'
                    ? 'Manage'
                    : 'Resume'
                  : 'Configure'}
            </Button>
          </div>

          {dmsLoading ? (
            <div className='bg-[var(--background)] border-l-4 border-gray-400 p-2 rounded-r-lg text-sm'>
              <p>⏳ Loading Dead Man's Switch status...</p>
            </div>
          ) : dmsConfig?.status === 'ACTIVE' ? (
            <div className='bg-[var(--lime)]/20 border-l-4 border-[var(--lime)] p-2 rounded-r-lg text-sm space-y-1'>
              <p>✅ Your Dead Man's Switch is active and working.</p>
              {dmsConfig.lastCheckIn && (
                <p className='text-xs text-[var(--foreground)]'>
                  Last confirmed: {formatDate(dmsConfig.lastCheckIn)}
                </p>
              )}
              {dmsConfig.nextCheckIn && (
                <p className='text-xs text-[var(--foreground)]'>
                  Next check-in: {formatDate(dmsConfig.nextCheckIn)} (
                  {getCheckInInterval(
                    dmsConfig.frequency,
                    dmsConfig.customFrequencyDays
                  )}
                  )
                </p>
              )}
            </div>
          ) : (
            <div className='bg-[var(--lemon)]/10 border-l-4 border-[var(--lemon)] p-2 rounded-r-lg text-sm'>
              <p>⚠️ Your Dead Man's Switch is not configured yet.</p>
            </div>
          )}
        </div>

        {/* <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Subscription & Billing</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Manage your subscription
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/billing')}
            >
              Manage Billing
            </Button>
          </div>

          <div className='bg-gray-50 p-2 rounded-lg border border-gray-200 text-sm'>
            <div className='flex items-center justify-between'>
              <span className='font-medium'>Basic Plan</span>
              <span className='bg-dark-blue text-white px-2 py-0.5 rounded-full text-xs'>
                Monthly
              </span>
            </div>
          </div>
        </div>

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Account Linking</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Share access with trusted individuals
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/account-linking')}
            >
              Manage Links
            </Button>
          </div>

          <div className='bg-amber-50 border-l-4 border-amber-400 p-2 rounded-r-lg text-sm'>
            <p>⚠️ No linked accounts yet.</p>
          </div>
        </div> */}

        <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Account Settings</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Manage your profile and preferences
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/settings')}
            >
              Settings
            </Button>
          </div>

          <div className='bg-gray-50 p-2 rounded-lg border border-gray-200 text-sm'>
            <div className='flex items-center'>
              <div className='w-8 h-8 rounded-full bg-dark-blue text-[var(--off-black)] flex items-center justify-center mr-2 text-sm font-medium'>
                {userProfile
                  ? `${userProfile.firstName.charAt(0)}${userProfile.lastName.charAt(0)}`
                  : user?.signInDetails?.loginId?.charAt(0)?.toUpperCase() ||
                    'U'}
              </div>
              <div>
                <p className='font-medium text-[var(--off-black)] text-sm'>
                  {userProfile
                    ? `${userProfile.firstName} ${userProfile.lastName}`
                    : user?.signInDetails?.loginId || 'User'}
                </p>
                <p className='text-xs text-[var(--off-black)]'>
                  {userProfile?.email ||
                    user?.signInDetails?.loginId ||
                    'No email'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* <div className='bg-background p-6 rounded-lg shadow-sm h-full flex flex-col'>
          <div className='flex justify-between items-center mb-2'>
            <div>
              <h2 className='text-lg font-semibold'>Educational Content</h2>
              <p className='text-[var(--custom-gray-dark)] text-xs'>
                Videos, articles & guides
              </p>
            </div>
            <Button
              variant='default'
              size='default'
              onClick={() => router.push('/dashboard/educational-content')}
            >
              Browse Content
            </Button>
          </div>

          <div className='bg-green-50 p-2 rounded-lg border border-green-200 text-sm'>
            <p>
              📚 Expand your knowledge with our comprehensive library of estate
              planning resources.
            </p>
          </div>
        </div> */}
      </div>
    </div>
  );
}
