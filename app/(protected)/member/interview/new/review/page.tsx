'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useInterviewNew } from '@/components/interview/interview-new-context';
import { InterviewQuestion } from '@/lib/api/interview-new-user';
import { RotateCcw } from 'lucide-react';
import routes from '@/utils/routes';
import { useQuery } from '@tanstack/react-query';
import { DocumentPreviewClient } from '@/app/dashboard/member/documents/preview/[id]/document-preview-client';
import { useAuth } from '@/context/AuthContext';
import { finalizeDocuments } from '@/lib/utils/document-create/finalize-documents';
import {
  getMemberAvailableTemplates,
  getMemberInterviewAnswers,
} from '@/lib/api/member-documents';

const ResponseSection: React.FC<{
  title: string;
  questions: InterviewQuestion[];
  getAnswerForQuestion: (questionId: string) => string | undefined;
  onEdit: (questionId: string) => void;
}> = ({ title, questions, getAnswerForQuestion, onEdit }) => {
  return (
    <div className='mb-8'>
      <h2 className='text-xl font-bold text-black-6c mb-4 border-b pb-2'>
        {title}
      </h2>
      <div className='space-y-4'>
        {questions.map(question => {
          const response = getAnswerForQuestion(question.questionId);
          if (!response) return null;

          return (
            <div
              key={question.questionId}
              className='flex justify-between items-start'
            >
              <div>
                <p className='font-medium'>{question.text}</p>
                <p className='text-[var(--custom-gray-medium)]'>{response}</p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Document preview component
const DocumentPreview: React.FC<{
  getAnswerForQuestion: (questionId: string) => string | undefined;
}> = ({ getAnswerForQuestion }) => {
  const { data: templates, isLoading: isLoadingTemplate } = useQuery({
    queryKey: ['templates'],
    queryFn: getMemberAvailableTemplates,
  });

  const { data: userAnswers, isLoading: isLoadingAnswerers } = useQuery({
    queryKey: ['users-answers'],
    queryFn: getMemberInterviewAnswers,
  });

  if (isLoadingTemplate || isLoadingAnswerers) {
    return null;
  }

  if (!templates || !userAnswers) {
    return null;
  }

  const documentType = 'Document';
  const documentState = 'Unknown State';
  const templateName = `${documentState} ${documentType}`;

  return templates.map(template => {
    const templateContent = template.latestVersion?.content || '';

    return (
      <DocumentPreviewClient
        templateId={template.id}
        documentType={documentType}
        documentState={documentState}
        templateName={templateName}
        templateContent={templateContent}
        template={template}
        answersMapping={userAnswers}
        isPreviewMode={true}
      />
    );
  });
};

const ReviewContent: React.FC = () => {
  const router = useRouter();
  const { userId } = useAuth();

  const [isFinalizing, setIsFinalizing] = useState<boolean>(false);
  const [showPreview, setShowPreview] = useState<boolean>(false);

  const {
    getAnswerForQuestion,
    goToQuestionById,
    getVisibleQuestions,
    isComplete,
    resetInterview,
    isLoadingUserProgress,
    isLoading,
  } = useInterviewNew();

  // Get only visible questions (those that should be shown based on conditional logic)
  const visibleQuestions = getVisibleQuestions();

  const handleEdit = (questionId: string) => {
    goToQuestionById(questionId);
    router.push('/dashboard/member/interview/new');
  };

  const handleResetInterview = async () => {
    await resetInterview();
    router.push(routes.member.interview);
  };

  const handleFinalize = async () => {
    try {
      if (!userId) return;
      setIsFinalizing(true);
      await finalizeDocuments(userId);
      router.push(routes.member.documents);
    } catch (error) {
      console.error(error);
    } finally {
      setIsFinalizing(false);
    }
  };

  if (isLoading || isLoadingUserProgress) {
    return null;
  }

  if (!isComplete) {
    router.push(routes.member.interview);
    return null;
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-black-6c mb-2'>
            Review Your Information
          </h1>
          <p className='text-[var(--custom-gray-medium)]'>
            Please review the information you've provided. You can edit any
            section by clicking the "Edit" button.
          </p>
        </div>

        <div className='mb-8 flex justify-end space-x-4'>
          <Button
            variant='outline'
            onClick={() => setShowPreview(!showPreview)}
          >
            {showPreview ? 'Hide Document Preview' : 'Show Document Preview'}
          </Button>
        </div>

        {showPreview ? (
          <div className='mb-8'>
            <DocumentPreview getAnswerForQuestion={getAnswerForQuestion} />
          </div>
        ) : (
          <>
            <ResponseSection
              title='Your Responses'
              questions={visibleQuestions}
              getAnswerForQuestion={getAnswerForQuestion}
              onEdit={handleEdit}
            />

            <div className='mt-12 flex justify-between'>
              <Button
                variant='outline'
                onClick={async () => {
                  if (
                    confirm(
                      'Are you sure you want to retake the interview? This will reset all your current answers.'
                    )
                  ) {
                    await handleResetInterview();
                  }
                }}
                className='flex items-center gap-2'
              >
                <RotateCcw className='w-4 h-4' />
                Retake Interview
              </Button>

              <Button disabled={isFinalizing} onClick={handleFinalize}>
                {isFinalizing ? 'Loading...' : 'Finalize Documents'}
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default function ReviewPage() {
  return <ReviewContent />;
}
