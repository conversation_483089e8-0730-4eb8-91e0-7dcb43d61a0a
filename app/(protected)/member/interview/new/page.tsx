'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useInterviewNew } from '@/components/interview/interview-new-context';
import { QuestionCard } from '@/components/interview/question-new';
import { useAuth } from '@/app/context/AuthContext';
import { RotateCcw, Save } from 'lucide-react';
import routes from '@/utils/routes';

const InterviewContent: React.FC = () => {
  const {
    currentInterview,
    isComplete,
    isLoading,
    isLoadingUserProgress,
    error,
    resetInterview,
  } = useInterviewNew();
  const router = useRouter();

  if (isLoading || isLoadingUserProgress) {
    return null;
  }

  if (isComplete) {
    router.push(routes.member.interviewReview);
    return null;
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        {/* Header */}
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 mb-2'>
            {currentInterview?.name || 'Estate Planning Interview'}
          </h1>
          <p className='text-gray-600'>
            {currentInterview?.description ||
              "Let's walk through your estate plan together. We'll ask you a series of questions to help create your personalized estate documents."}
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert className='mb-6 border-red-200 bg-red-50'>
            <AlertDescription className='text-red-700'>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Question Card */}
        <QuestionCard />

        {/* Action Buttons */}
        <div className='mt-8 flex justify-between'>
          <Button
            variant='outline'
            size='default'
            onClick={() => {
              if (
                confirm(
                  'Are you sure you want to save and exit? Your progress will be saved.'
                )
              ) {
                router.push('/dashboard');
              }
            }}
            className='flex items-center gap-2'
          >
            <Save className='w-4 h-4' />
            Save and Exit
          </Button>

          <Button
            variant='destructive'
            size='default'
            onClick={() => {
              if (
                confirm(
                  'Are you sure you want to reset the interview? All your answers will be lost.'
                )
              ) {
                resetInterview();
              }
            }}
            className='flex items-center gap-2'
          >
            <RotateCcw className='w-4 h-4' />
            Reset Interview
          </Button>
        </div>
      </div>
    </div>
  );
};

const InterviewPageContent: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();

  // Show authentication required message
  if (!user) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <Card>
            <CardHeader>
              <CardTitle>Authentication Required</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-gray-600 mb-4'>
                You need to be logged in to access the interview.
              </p>
              <Button onClick={() => router.push('/login')}>Go to Login</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return <InterviewContent />;
};

export default function NewInterviewPage() {
  return <InterviewPageContent />;
}
