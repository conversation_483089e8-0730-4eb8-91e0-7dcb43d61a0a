'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Shield,
  FileText,
  Eye,
  Download,
  Lock,
  Clock,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useLivingDocuments } from '@/hooks/useLivingDocuments';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';

const documentTypeLabels = {
  EmergencyContacts: 'Emergency Contacts',
  PetCare: 'Pet Care Instructions',
  DigitalAssets: 'Digital Assets',
  EndOfLifeWishes: 'End of Life Wishes',
  MedicalDirectives: 'Medical Directives',
  Other: 'Other',
};

export default function VaultPage() {
  const { documents, loading, error, getDocumentStatus } = useLivingDocuments();
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [selectedDocument, setSelectedDocument] = useState<any>(null);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'current':
        return <CheckCircle className='h-4 w-4 text-green-500' />;
      case 'due-soon':
        return <Clock className='h-4 w-4 text-yellow-500' />;
      case 'overdue':
        return <AlertCircle className='h-4 w-4 text-red-500' />;
      default:
        return <CheckCircle className='h-4 w-4 text-green-500' />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current':
        return 'text-green-600';
      case 'due-soon':
        return 'text-yellow-600';
      case 'overdue':
        return 'text-red-600';
      default:
        return 'text-green-600';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  // Filter documents - only show active documents in vault
  const filteredDocuments = documents.filter(document => {
    const typeMatch =
      filterType === 'all' || document.documentType === filterType;
    const statusMatch =
      filterStatus === 'all' || document.status === filterStatus;
    const isActive = document.status === 'Active'; // Only show active documents in vault
    return typeMatch && statusMatch && isActive;
  });

  if (loading) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='max-w-4xl mx-auto'>
          <h1 className='text-3xl font-bold mb-8'>Document Vault</h1>
          <p>Loading vault...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-4xl mx-auto'>
        <div className='flex items-center gap-3 mb-8'>
          <Shield className='h-8 w-8 text-blue-600' />
          <div>
            <h1 className='text-3xl font-bold'>Document Vault</h1>
            <p className='text-[var(--custom-gray-medium)] mt-2'>
              Secure storage for your active living documents with encryption
              and access controls.
            </p>
          </div>
        </div>

        {/* Security Info Card */}
        <Card className='mb-6 border-blue-200 bg-blue-50'>
          <CardContent className='pt-6'>
            <div className='flex items-start space-x-3'>
              <Lock className='h-5 w-5 text-blue-600 mt-0.5' />
              <div>
                <h3 className='font-medium text-blue-900'>Secure Vault</h3>
                <p className='text-sm text-blue-700 mt-1'>
                  All documents are encrypted at rest and in transit. Access is
                  logged and monitored for security. Only active documents are
                  displayed in the vault.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filters */}
        <div className='flex gap-4 mb-6'>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className='w-48'>
              <SelectValue placeholder='Filter by type' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Types</SelectItem>
              {Object.entries(documentTypeLabels).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Error State */}
        {error && (
          <Card className='mb-6'>
            <CardContent className='pt-6'>
              <div className='text-red-500'>
                <p>Error loading vault: {error}</p>
                <p className='text-sm mt-1'>
                  Please try again later or contact support.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Documents List */}
        {filteredDocuments.length === 0 ? (
          <Card>
            <CardContent className='pt-6'>
              <div className='text-center py-8'>
                <Shield className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)] mb-4' />
                <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
                  No active documents in vault
                </h3>
                <p className='text-[var(--custom-gray-medium)] mb-6'>
                  {documents.length === 0
                    ? 'Create living documents to see them in your secure vault.'
                    : 'No active documents match your current filters.'}
                </p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className='space-y-4'>
            {filteredDocuments.map(document => {
              const status = getDocumentStatus(document);
              return (
                <Card
                  key={document.id}
                  className='bg-background border-l-4 border-l-blue-500'
                >
                  <CardContent className='p-6'>
                    <div className='flex justify-between items-start'>
                      <div className='flex-1'>
                        <div className='flex items-center gap-2 mb-2'>
                          <h3 className='text-lg font-semibold'>
                            {document.title}
                          </h3>
                          <Badge variant='outline' className='text-xs'>
                            {documentTypeLabels[document.documentType]}
                          </Badge>
                          <Badge
                            variant='outline'
                            className='text-xs bg-green-50 text-green-700 border-green-200'
                          >
                            Active
                          </Badge>
                          {document.isTemplate && (
                            <Badge
                              variant='outline'
                              className='text-xs border-blue-500 text-blue-600'
                            >
                              Template
                            </Badge>
                          )}
                        </div>

                        <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-[var(--custom-gray-medium)] mb-4'>
                          <div>
                            <span className='font-medium'>Version:</span>{' '}
                            {document.version}
                          </div>
                          <div>
                            <span className='font-medium'>Last Updated:</span>{' '}
                            {formatDate(document.updatedAt)}
                          </div>
                          <div>
                            <span className='font-medium'>Next Review:</span>{' '}
                            {formatDate(document.nextReviewDate)}
                          </div>
                          <div
                            className={`flex items-center gap-1 ${getStatusColor(status)}`}
                          >
                            {getStatusIcon(status)}
                            <span className='capitalize'>
                              {status.replace('-', ' ')}
                            </span>
                          </div>
                        </div>

                        <div className='flex items-center gap-2 text-sm text-[var(--custom-gray-medium)]'>
                          <Lock className='h-4 w-4' />
                          <span>Encrypted and secured</span>
                          <span>•</span>
                          <span>Created {formatDate(document.createdAt)}</span>
                        </div>
                      </div>

                      <div className='flex gap-2 ml-4'>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => setSelectedDocument(document)}
                            >
                              <Eye className='h-4 w-4 mr-2' />
                              View
                            </Button>
                          </DialogTrigger>
                          <DialogContent className='sm:max-w-[600px] max-h-[80vh] overflow-y-auto'>
                            <DialogHeader>
                              <DialogTitle>{document.title}</DialogTitle>
                              <DialogDescription>
                                {documentTypeLabels[document.documentType]} •
                                Version {document.version}
                              </DialogDescription>
                            </DialogHeader>
                            <div className='mt-4'>
                              <div className='mb-4 p-3 bg-gray-50 rounded-md'>
                                <div className='grid grid-cols-2 gap-4 text-sm'>
                                  <div>
                                    <span className='font-medium'>Status:</span>{' '}
                                    {document.status}
                                  </div>
                                  <div>
                                    <span className='font-medium'>
                                      Review Frequency:
                                    </span>{' '}
                                    {document.reminderFrequency}
                                  </div>
                                  <div>
                                    <span className='font-medium'>
                                      Last Updated:
                                    </span>{' '}
                                    {formatDate(document.updatedAt)}
                                  </div>
                                  <div>
                                    <span className='font-medium'>
                                      Next Review:
                                    </span>{' '}
                                    {formatDate(document.nextReviewDate)}
                                  </div>
                                </div>
                              </div>
                              <div>
                                <label className='block text-sm font-medium mb-2'>
                                  Content:
                                </label>
                                <Textarea
                                  value={document.content}
                                  readOnly
                                  className='min-h-[200px] bg-gray-50'
                                />
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Vault Statistics */}
        {filteredDocuments.length > 0 && (
          <Card className='mt-8'>
            <CardHeader>
              <CardTitle className='text-lg'>Vault Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-center'>
                <div>
                  <div className='text-2xl font-bold text-blue-600'>
                    {filteredDocuments.length}
                  </div>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Active Documents
                  </div>
                </div>
                <div>
                  <div className='text-2xl font-bold text-green-600'>
                    {
                      filteredDocuments.filter(
                        doc => getDocumentStatus(doc) === 'current'
                      ).length
                    }
                  </div>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Up to Date
                  </div>
                </div>
                <div>
                  <div className='text-2xl font-bold text-yellow-600'>
                    {
                      filteredDocuments.filter(
                        doc => getDocumentStatus(doc) === 'due-soon'
                      ).length
                    }
                  </div>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Due Soon
                  </div>
                </div>
                <div>
                  <div className='text-2xl font-bold text-red-600'>
                    {
                      filteredDocuments.filter(
                        doc => getDocumentStatus(doc) === 'overdue'
                      ).length
                    }
                  </div>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Overdue
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
