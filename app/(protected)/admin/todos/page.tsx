'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { generateClient } from 'aws-amplify/data';
import { fetchAuthSession } from 'aws-amplify/auth';
import type { Schema } from '@/amplify/data/resource';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  Plus,
  Trash2,
  CheckSquare,
  Loader2,
  AlertTriangle,
} from 'lucide-react';
import { toast } from 'sonner';

const client = generateClient<Schema>();

interface Todo {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

export default function TodosPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [newTodoContent, setNewTodoContent] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // Fetch todos
  const {
    data: todos = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['todos'],
    queryFn: async () => {
      try {
        // Get current user's JWT token
        const session = await fetchAuthSession();
        const token = session.tokens?.accessToken?.toString();

        if (!token) {
          throw new Error('No auth token available');
        }

        // Create client with lambda auth mode and JWT token
        const lambdaClient = generateClient<Schema>({
          authMode: 'lambda',
          authToken: token,
        });

        const { data, errors } = await lambdaClient.models.Todo.list();
        console.log('===> ERRPRS', errors);
        if (errors) {
          throw new Error(errors[0].message);
        }
        return data as Todo[];
      } catch (error) {
        console.error('Error fetching todos:', error);
        throw error;
      }
    },
  });

  console.log('===> ERROR', error);

  // Create todo mutation
  const createTodoMutation = useMutation({
    mutationFn: async (content: string) => {
      const session = await fetchAuthSession();
      const token = session.tokens?.accessToken?.toString();

      const { data, errors } = await client.models.Todo.create(
        {
          content,
        },
        {
          authMode: 'lambda',
          authToken: token,
        }
      );
      if (errors) {
        throw new Error(errors[0].message);
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['todos'] });
      setNewTodoContent('');
      setIsCreating(false);
      toast.success('Todo created successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create todo: ${error.message}`);
    },
  });

  // Delete todo mutation
  const deleteTodoMutation = useMutation({
    mutationFn: async (id: string) => {
      const { errors } = await client.models.Todo.delete({ id });
      if (errors) {
        throw new Error(errors[0].message);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['todos'] });
      toast.success('Todo deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete todo: ${error.message}`);
    },
  });

  const handleCreateTodo = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTodoContent.trim()) {
      toast.error('Please enter todo content');
      return;
    }
    createTodoMutation.mutate(newTodoContent.trim());
  };

  const handleDeleteTodo = (id: string) => {
    if (confirm('Are you sure you want to delete this todo?')) {
      deleteTodoMutation.mutate(id);
    }
  };

  // if (error) {
  //   return (
  //     <div className='container mx-auto py-8'>
  //       <Alert variant='destructive'>
  //         <AlertTriangle className='h-4 w-4' />
  //         <AlertDescription>
  //           Failed to load todos: {(error as Error).message}
  //         </AlertDescription>
  //       </Alert>
  //     </div>
  //   );
  // }

  return (
    <div className='container mx-auto py-8 space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.push('/admin/dashboard')}
            className='flex items-center space-x-2'
          >
            <ArrowLeft className='h-4 w-4' />
            <span>Back to Dashboard</span>
          </Button>
          <div>
            <h1 className='text-3xl font-bold'>Todos Management</h1>
            <p className='text-muted-foreground'>
              Demo page for managing todos from the demo models schema
            </p>
          </div>
        </div>
      </div>

      {/* Create Todo Form */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Plus className='h-5 w-5' />
            <span>Create New Todo</span>
          </CardTitle>
          <CardDescription>Add a new todo item to the list</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleCreateTodo} className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='content'>Todo Content</Label>
              <Input
                id='content'
                type='text'
                placeholder='Enter todo content...'
                value={newTodoContent}
                onChange={e => setNewTodoContent(e.target.value)}
                disabled={createTodoMutation.isPending}
              />
            </div>
            <Button
              type='submit'
              disabled={createTodoMutation.isPending || !newTodoContent.trim()}
              className='flex items-center space-x-2'
            >
              {createTodoMutation.isPending ? (
                <Loader2 className='h-4 w-4 animate-spin' />
              ) : (
                <Plus className='h-4 w-4' />
              )}
              <span>
                {createTodoMutation.isPending ? 'Creating...' : 'Create Todo'}
              </span>
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Todos List */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <CheckSquare className='h-5 w-5' />
            <span>Todos List</span>
          </CardTitle>
          <CardDescription>
            All todos from the demo models schema ({todos.length} total)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className='flex items-center justify-center py-8'>
              <Loader2 className='h-6 w-6 animate-spin' />
              <span className='ml-2'>Loading todos...</span>
            </div>
          ) : todos.length === 0 ? (
            <div className='text-center py-8 text-muted-foreground'>
              <CheckSquare className='h-12 w-12 mx-auto mb-4 opacity-50' />
              <p>No todos found. Create your first todo above!</p>
            </div>
          ) : (
            <div className='space-y-3'>
              {todos.map(todo => (
                <div
                  key={todo.id}
                  className='flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors'
                >
                  <div className='flex-1'>
                    <p className='font-medium'>{todo.content}</p>
                    <p className='text-sm text-muted-foreground'>
                      Created: {new Date(todo.createdAt).toLocaleString()}
                    </p>
                  </div>
                  <Button
                    variant='destructive'
                    size='sm'
                    onClick={() => handleDeleteTodo(todo.id)}
                    disabled={deleteTodoMutation.isPending}
                    className='flex items-center space-x-1'
                  >
                    {deleteTodoMutation.isPending ? (
                      <Loader2 className='h-4 w-4 animate-spin' />
                    ) : (
                      <Trash2 className='h-4 w-4' />
                    )}
                    <span>Delete</span>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
