'use client';

import React, { useState } from 'react';
import { useP<PERSON><PERSON>, useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  Calendar,
  User,
  FileText,
  Tag,
  AlertTriangle,
  Loader2,
  Settings,
  Save,
} from 'lucide-react';
import { adminLivingDocumentTemplatesAPI } from '@/lib/api/admin-living-document-templates';
import routes from '@/utils/routes';
import type { Schema } from '@/amplify/data/resource';
import { LivingDocumentQuestionBuilder } from '@/components/dashboard/admin/living-documents-builder/question-builder';
import type { LivingDocumentQuestion } from '@/types/living-document-builder';
import { toast } from 'sonner';
import { StatusBadge } from '@/components/ui/living-document-status-badge';

type LivingDocumentTemplate = Schema['LivingDocumentTemplate']['type'];

export default function EditLivingDocumentTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const templateId = params.id as string;
  const [questions, setQuestions] = useState<LivingDocumentQuestion[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const {
    data: template,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['admin-living-document-template', templateId],
    queryFn: () =>
      adminLivingDocumentTemplatesAPI.getLivingDocumentTemplate(templateId),
    enabled: !!templateId,
    retry: false,
  });

  // Initialize questions from template data
  React.useEffect(() => {
    if (template?.questions) {
      try {
        // Handle both string (legacy) and array (new) formats
        const parsedQuestions =
          typeof template.questions === 'string'
            ? JSON.parse(template.questions)
            : Array.isArray(template.questions)
              ? template.questions
              : [];
        setQuestions(parsedQuestions || []);
      } catch (error) {
        console.error('Error parsing template questions:', error);
        setQuestions([]);
      }
    } else {
      setQuestions([]);
    }
  }, [template]);

  // Save template mutation
  const saveTemplateMutation = useMutation({
    mutationFn: async () => {
      if (!template) throw new Error('Template not found');

      return adminLivingDocumentTemplatesAPI.updateLivingDocumentTemplate(
        templateId,
        {
          questions: questions,
        }
      );
    },
    onSuccess: () => {
      setHasUnsavedChanges(false);
      toast.success('Template saved successfully');
      queryClient.invalidateQueries({
        queryKey: ['admin-living-document-template', templateId],
      });
    },
    onError: error => {
      toast.error('Failed to save template');
      console.error('Error saving template:', error);
    },
  });

  const handleQuestionsUpdated = (
    updatedQuestions: LivingDocumentQuestion[]
  ) => {
    setQuestions(updatedQuestions);
    setHasUnsavedChanges(true);
  };

  const handleSaveTemplate = () => {
    saveTemplateMutation.mutate();
  };

  const getDocumentTypeLabel = (documentType: string) => {
    const typeLabels: { [key: string]: string } = {
      EmergencyContacts: 'Emergency Contacts',
      PetCare: 'Pet Care Instructions',
      DigitalAssets: 'Digital Assets',
      EndOfLifeWishes: 'End of Life Wishes',
      MedicalDirectives: 'Medical Directives',
      Other: 'Other',
    };
    return typeLabels[documentType] || documentType;
  };

  const handleBack = () => {
    router.push(routes.admin.livingDocumentsBuilder);
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className='max-w-7xl mx-auto'>
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center space-x-4'>
            <Button
              variant='outline'
              size='sm'
              onClick={handleBack}
              className='flex items-center space-x-2'
            >
              <ArrowLeft className='h-4 w-4' />
              <span>Back to Templates</span>
            </Button>
            <div>
              <h1 className='text-3xl font-bold text-[var(--foreground)]'>
                Template Details
              </h1>
              <p className='text-[var(--custom-gray-medium)] mt-1'>
                Loading template information...
              </p>
            </div>
          </div>
        </div>

        <Card>
          <CardContent className='flex items-center justify-center py-12'>
            <div className='text-center space-y-4'>
              <Loader2 className='h-8 w-8 animate-spin mx-auto text-[var(--custom-gray-medium)]' />
              <p className='text-[var(--custom-gray-medium)]'>
                Loading template details...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className='max-w-7xl mx-auto'>
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center space-x-4'>
            <Button
              variant='outline'
              size='sm'
              onClick={handleBack}
              className='flex items-center space-x-2'
            >
              <ArrowLeft className='h-4 w-4' />
              <span>Back to Templates</span>
            </Button>
            <div>
              <h1 className='text-3xl font-bold text-[var(--foreground)]'>
                Template Details
              </h1>
              <p className='text-[var(--custom-gray-medium)] mt-1'>
                Error loading template
              </p>
            </div>
          </div>
        </div>

        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>
            {error instanceof Error
              ? error.message
              : 'Failed to load template. Please try again.'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Template not found
  if (!template) {
    return (
      <div className='max-w-7xl mx-auto'>
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center space-x-4'>
            <Button
              variant='outline'
              size='sm'
              onClick={handleBack}
              className='flex items-center space-x-2'
            >
              <ArrowLeft className='h-4 w-4' />
              <span>Back to Templates</span>
            </Button>
            <div>
              <h1 className='text-3xl font-bold text-[var(--foreground)]'>
                Template Details
              </h1>
              <p className='text-[var(--custom-gray-medium)] mt-1'>
                Template not found
              </p>
            </div>
          </div>
        </div>

        <Alert>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>
            The requested template could not be found. It may have been deleted
            or you may not have permission to view it.
          </AlertDescription>
        </Alert>

        <div className='mt-6 text-center'>
          <Button onClick={handleBack} variant='outline'>
            Return to Templates List
          </Button>
        </div>
      </div>
    );
  }

  // Success state - render the template
  return (
    <div className='max-w-7xl mx-auto space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleBack}
            className='flex items-center space-x-2'
          >
            <ArrowLeft className='h-4 w-4' />
            <span>Back to Templates</span>
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--foreground)]'>
              Edit Template: {template.title}
            </h1>
            <p className='text-[var(--custom-gray-medium)] mt-1'>
              Configure template information and questions
            </p>
          </div>
        </div>
        <div className='flex items-center space-x-2'>
          {hasUnsavedChanges && (
            <span className='text-sm text-amber-600'>Unsaved changes</span>
          )}
          <Button
            onClick={handleSaveTemplate}
            disabled={saveTemplateMutation.isPending || !hasUnsavedChanges}
          >
            <Save className='h-4 w-4 mr-2' />
            {saveTemplateMutation.isPending ? 'Saving...' : 'Save Template'}
          </Button>
        </div>
      </div>

      {/* Template Information Card */}
      <Card>
        <CardHeader>
          <div className='flex items-start justify-between'>
            <div className='space-y-2'>
              <CardTitle className='text-2xl flex items-center space-x-3'>
                {template.icon && (
                  <span className='text-2xl'>{template.icon}</span>
                )}
                <span>{template.title}</span>
              </CardTitle>
              {template.description && (
                <CardDescription className='text-base'>
                  {template.description}
                </CardDescription>
              )}
            </div>
            <StatusBadge status={template.status || 'Draft'} />
          </div>
        </CardHeader>
        <CardContent className='space-y-6'>
          {/* Metadata Grid */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
            <div className='flex items-center space-x-2 text-sm'>
              <div>
                <Tag className='h-5 w-5 text-[var(--custom-gray-medium)]' />
              </div>
              <div>
                <p className='font-medium'>Document Type</p>
                <p className='text-[var(--custom-gray-medium)]'>
                  {getDocumentTypeLabel(template.documentType)}
                </p>
              </div>
            </div>

            <div className='flex items-center space-x-2 text-sm'>
              <div>
                <FileText className='h-5 w-5 text-[var(--custom-gray-medium)]' />
              </div>
              <div>
                <p className='font-medium'>Version</p>
                <p className='text-[var(--custom-gray-medium)]'>
                  {template.version || 1}
                </p>
              </div>
            </div>

            <div className='flex items-center space-x-2 text-sm'>
              <div>
                <User className='h-5 w-5 text-[var(--custom-gray-medium)]' />
              </div>
              <div>
                <p className='font-medium'>Created By</p>
                <p className='text-[var(--custom-gray-medium)]'>
                  {template.createdByEmail || 'Unknown'}
                </p>
              </div>
            </div>

            <div className='flex items-center space-x-2 text-sm'>
              <div>
                <Calendar className='h-5 w-5 text-[var(--custom-gray-medium)]' />
              </div>
              <div>
                <p className='font-medium'>Created</p>
                <p className='text-[var(--custom-gray-medium)]'>
                  {formatDate(template.createdAt)}
                </p>
              </div>
            </div>
          </div>

          {/* Last Updated */}
          {template.updatedAt && template.updatedAt !== template.createdAt && (
            <div className='border-t pt-4'>
              <div className='flex items-center space-x-2 text-sm'>
                <Calendar className='h-5 w-5 text-[var(--custom-gray-medium)]' />
                <div>
                  <p className='font-medium'>Last Updated</p>
                  <p className='text-[var(--custom-gray-medium)]'>
                    {formatDate(template.updatedAt)}
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Template Content Card */}
      <LivingDocumentQuestionBuilder
        templateId={templateId}
        questions={questions}
        onQuestionsUpdated={handleQuestionsUpdated}
      />
    </div>
  );
}
