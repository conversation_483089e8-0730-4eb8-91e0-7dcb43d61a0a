'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PlusCircle, AlertTriangle, FileText } from 'lucide-react';
import { LivingDocumentTemplateTable } from '@/components/dashboard/admin/living-documents-builder/template-table';
import { LivingDocumentTemplateDialog } from '@/components/dashboard/admin/living-documents-builder/template-dialog';
import { useQuery } from '@tanstack/react-query';
import { adminLivingDocumentTemplatesAPI } from '@/lib/api/admin-living-document-templates';
import routes from '@/utils/routes';

export default function LivingDocumentsBuilderPage() {
  const router = useRouter();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const {
    data: templates = [],
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['admin-living-document-templates'],
    queryFn: adminLivingDocumentTemplatesAPI.listLivingDocumentTemplates,
  });

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setShowCreateDialog(true);
  };

  const handleEditTemplate = (template: any) => {
    setEditingTemplate(template);
    setShowCreateDialog(true);
  };

  const handleBuildTemplate = (template: any) => {
    router.push(routes.admin.livingDocumentsBuilderEdit(template.id));
  };

  const handleDeleteTemplate = async (template: any) => {
    setActionLoading(template.id);
    try {
      await adminLivingDocumentTemplatesAPI.deleteLivingDocumentTemplate(
        template.id
      );
      await refetch();
    } catch (err) {
      setError('Failed to delete template. Please try again.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleSaveTemplate = async (templateData: any) => {
    try {
      if (templateData.id) {
        // Update existing template
        await adminLivingDocumentTemplatesAPI.updateLivingDocumentTemplate(
          templateData.id,
          {
            documentType: templateData.documentType,
            title: templateData.title,
            description: templateData.description,
            content: templateData.content,
            status: templateData.status,
            icon: templateData.icon,
          }
        );
      } else {
        // Create new template
        await adminLivingDocumentTemplatesAPI.createLivingDocumentTemplate({
          documentType: templateData.documentType,
          title: templateData.title,
          description: templateData.description,
          content: templateData.content,
          status: templateData.status,
          icon: templateData.icon,
        });
      }
      await refetch();
      setShowCreateDialog(false);
      setEditingTemplate(null);
    } catch (err) {
      throw new Error('Failed to save template. Please try again.');
    }
  };

  // TODO MODIFY LOADING UI
  if (isLoading) {
    return null;
  }

  return (
    <div className='max-w-7xl mx-auto'>
      {/* Header */}
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h1 className='text-3xl font-bold text-[var(--foreground)] flex items-center'>
            <FileText className='mr-3 h-8 w-8 text-blue-600' />
            Living Documents Builder
          </h1>
          <p className='text-[var(--custom-gray-medium)] mt-1'>
            Create and manage templates for living documents
          </p>
        </div>
        <div className='flex items-center space-x-2'>
          <Button onClick={handleCreateTemplate} variant='default' size='sm'>
            <PlusCircle className='mr-2 h-4 w-4' />
            Create New Template
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive' className='mb-6'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Templates List */}
      <Card>
        <CardHeader>
          <CardTitle>Document Templates</CardTitle>
          <CardDescription>
            Manage your living document templates and their configurations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <LivingDocumentTemplateTable
            templates={templates}
            loading={false}
            actionLoading={actionLoading}
            onEdit={handleEditTemplate}
            onBuild={handleBuildTemplate}
            onDelete={handleDeleteTemplate}
          />
        </CardContent>
      </Card>

      {/* Create/Edit Template Dialog */}
      <LivingDocumentTemplateDialog
        isOpen={showCreateDialog}
        onClose={() => {
          setShowCreateDialog(false);
          setEditingTemplate(null);
        }}
        onSave={handleSaveTemplate}
        template={editingTemplate}
      />
    </div>
  );
}
