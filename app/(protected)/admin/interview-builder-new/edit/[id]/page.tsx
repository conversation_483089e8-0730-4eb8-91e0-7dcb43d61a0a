'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  MessageSquare,
  AlertTriangle,
  Save,
  Loader2,
} from 'lucide-react';
import {
  UpdateInterviewNewRequest,
  InterviewFormData,
  ValidationErrors,
} from '@/types/interview-builder-new';
import {
  getInterviewWithVersion,
  updateInterview,
  validateInterviewData,
} from '@/lib/api/interview-builder-new';

export default function EditInterviewPage() {
  const router = useRouter();
  const params = useParams();
  const interviewId = params.id as string;

  const [formData, setFormData] = useState<InterviewFormData>({
    name: '',
    description: '',
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [saving, setSaving] = useState(false);

  // Fetch interview data
  const {
    data: interviewData,
    isLoading,
    error: fetchError,
  } = useQuery({
    queryKey: ['interview-with-version', interviewId],
    queryFn: () => getInterviewWithVersion(interviewId),
    enabled: !!interviewId,
  });

  const interview = interviewData?.interview;

  // Update form data when interview is loaded
  useEffect(() => {
    if (interview) {
      setFormData({
        name: interview.name,
        description: interview.description || '',
      });
    }
  }, [interview]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field when user starts typing
    if (errors[name as keyof ValidationErrors]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name as keyof ValidationErrors];
        return newErrors;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!interview) return;

    // Validate form data
    const validationErrors = validateInterviewData(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setSaving(true);
      setErrors({});

      const updateData: UpdateInterviewNewRequest = {
        id: interview.id,
        name: formData.name,
        description: formData.description || undefined,
      };

      await updateInterview(updateData);

      // Redirect back to main interview builder page
      router.push('/admin/interview-builder-new');
    } catch (err) {
      setErrors({ submit: 'Failed to update interview. Please try again.' });
      console.error('Error updating interview:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.push('/admin/interview-builder-new');
  };

  // Loading state
  if (isLoading) {
    return (
      <div className='max-w-4xl mx-auto'>
        <div className='flex items-center justify-center h-64'>
          <div className='text-center'>
            <Loader2 className='animate-spin h-8 w-8 text-blue-600 mx-auto' />
            <p className='mt-2 text-[var(--custom-gray-medium)]'>
              Loading interview details...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (fetchError || !interview) {
    return (
      <div className='max-w-4xl mx-auto'>
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center space-x-4'>
            <Button onClick={handleCancel} variant='outline' size='sm'>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to Interview Builder
            </Button>
            <div>
              <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)] flex items-center'>
                <MessageSquare className='mr-3 h-8 w-8 text-blue-600' />
                Edit Interview
              </h1>
            </div>
          </div>
        </div>

        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>
            Failed to load interview details. The interview may not exist or you
            may not have permission to access it.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className='max-w-4xl mx-auto'>
      {/* Header */}
      <div className='flex items-center justify-between mb-6'>
        <div className='flex items-center space-x-4'>
          <Button onClick={handleCancel} variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Interview Builder
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)] flex items-center'>
              <MessageSquare className='mr-3 h-8 w-8 text-blue-600' />
              Edit Interview
            </h1>
            <p className='text-[var(--custom-gray-medium)] mt-1'>
              Update interview information and settings
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Interview Details</CardTitle>
          <CardDescription>
            Update the basic information about your interview
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            {errors.submit && (
              <Alert variant='destructive'>
                <AlertTriangle className='h-4 w-4' />
                <AlertDescription>{errors.submit}</AlertDescription>
              </Alert>
            )}

            {/* Interview Name */}
            <div className='space-y-2'>
              <Label htmlFor='name' className='text-sm font-medium'>
                Interview Name *
              </Label>
              <Input
                id='name'
                name='name'
                type='text'
                value={formData.name}
                onChange={handleInputChange}
                placeholder='Enter interview name'
                className={errors.name ? 'border-red-500' : ''}
                disabled={saving}
              />
              {errors.name && (
                <p className='text-sm text-red-600'>{errors.name}</p>
              )}
            </div>

            {/* Interview Description */}
            <div className='space-y-2'>
              <Label htmlFor='description' className='text-sm font-medium'>
                Description
              </Label>
              <Textarea
                id='description'
                name='description'
                value={formData.description}
                onChange={handleInputChange}
                placeholder='Enter interview description (optional)'
                rows={4}
                className={errors.description ? 'border-red-500' : ''}
                disabled={saving}
              />
              {errors.description && (
                <p className='text-sm text-red-600'>{errors.description}</p>
              )}
            </div>

            {/* Action Buttons */}
            <div className='flex justify-end space-x-3 pt-6'>
              <Button
                type='button'
                variant='outline'
                onClick={handleCancel}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={saving} className='min-w-[120px]'>
                {saving ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className='mr-2 h-4 w-4' />
                    Update Interview
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
