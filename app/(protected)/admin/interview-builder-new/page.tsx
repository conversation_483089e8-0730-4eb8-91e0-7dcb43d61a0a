'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MessageSquare,
  PlusCircle,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  AlertTriangle,
  Settings,
  Eye,
} from 'lucide-react';
import {
  getInterviewsList,
  deleteInterview,
} from '@/lib/api/interview-builder-new';
import { InterviewWithLatestVersion } from '@/types/interview-builder-new';

export default function InterviewBuilderNewPage() {
  const router = useRouter();
  const queryClient = useQueryClient();

  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Fetch interviews
  const {
    data: interviews = [],
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['interviews-new'],
    queryFn: getInterviewsList,
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: deleteInterview,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['interviews-new'] });
    },
    onError: error => {
      setError('Failed to delete interview. Please try again.');
      console.error('Delete error:', error);
    },
  });

  // Filter interviews based on search
  const filteredInterviews = interviews.filter(
    interview =>
      interview.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (interview.description &&
        interview.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleCreateInterview = () => {
    router.push('/admin/interview-builder-new/create');
  };

  const handleEditInterview = (interview: InterviewWithLatestVersion) => {
    router.push(`/admin/interview-builder-new/edit/${interview.id}`);
  };

  const handleBuildInterview = (interview: InterviewWithLatestVersion) => {
    router.push(`/admin/interview-builder-new/build/${interview.id}`);
  };

  const handleDeleteInterview = async (
    interview: InterviewWithLatestVersion
  ) => {
    if (
      window.confirm(
        `Are you sure you want to delete "${interview.name}"? This action cannot be undone.`
      )
    ) {
      deleteMutation.mutate(interview.id);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className='max-w-7xl mx-auto'>
      {/* Header */}
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h1 className='text-3xl font-bold text-[var(--foreground)] flex items-center'>
            <MessageSquare className='mr-3 h-8 w-8 text-blue-600' />
            Interview Builder - New
          </h1>
          <p className='text-[var(--custom-gray-medium)] mt-1'>
            Manage interviews with optimized data structure
          </p>
        </div>
        <Button onClick={handleCreateInterview} variant='default' size='sm'>
          <PlusCircle className='mr-2 h-4 w-4' />
          Create New Interview
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive' className='mb-6'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Search */}
      <Card className='mb-6'>
        <CardContent className='pt-6'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
            <Input
              placeholder='Search interviews...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
        </CardContent>
      </Card>

      {/* Interview List */}
      <Card>
        <CardHeader>
          <CardTitle>Interviews</CardTitle>
          <CardDescription>
            Manage your interview templates and questions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className='space-y-3'>
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className='h-16 bg-gray-100 rounded animate-pulse'
                />
              ))}
            </div>
          ) : filteredInterviews.length === 0 ? (
            <div className='text-center py-12'>
              <MessageSquare className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)]' />
              <h3 className='mt-2 text-sm font-medium text-[var(--custom-gray-dark)]'>
                {searchTerm ? 'No interviews found' : 'No interviews yet'}
              </h3>
              <p className='mt-1 text-sm text-[var(--custom-gray-medium)]'>
                {searchTerm
                  ? 'Try adjusting your search terms.'
                  : 'Get started by creating your first interview.'}
              </p>
              {!searchTerm && (
                <div className='mt-6'>
                  <Button onClick={handleCreateInterview}>
                    <PlusCircle className='mr-2 h-4 w-4' />
                    Create First Interview
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Interview Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Questions</TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead className='text-right'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInterviews.map(interview => (
                  <TableRow key={interview.id}>
                    <TableCell>
                      <div>
                        <div className='font-medium text-[var(--custom-gray-dark)]'>
                          {interview.name}
                        </div>
                        {interview.description && (
                          <div className='text-sm text-[var(--custom-gray-medium)] mt-1'>
                            {interview.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={interview.isActive ? 'default' : 'secondary'}
                      >
                        {interview.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className='text-sm'>
                        {interview.questionsCount} questions
                      </div>
                    </TableCell>
                    <TableCell>
                      {interview.latestVersionNumber ? (
                        <Badge variant='outline'>
                          v{interview.latestVersionNumber}
                        </Badge>
                      ) : (
                        <span className='text-sm text-gray-500'>
                          No versions
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className='text-sm text-[var(--custom-gray-medium)]'>
                        {formatDate(interview.updatedAt)}
                      </div>
                    </TableCell>
                    <TableCell className='text-right'>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' size='sm'>
                            <MoreHorizontal className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuItem
                            onClick={() => handleBuildInterview(interview)}
                          >
                            <Settings className='mr-2 h-4 w-4' />
                            Build Interview
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEditInterview(interview)}
                          >
                            <Edit className='mr-2 h-4 w-4' />
                            Edit Details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteInterview(interview)}
                            className='text-red-600'
                          >
                            <Trash2 className='mr-2 h-4 w-4' />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
