'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  Save,
  Eye,
  MessageSquare,
  AlertTriangle,
  FileText,
  Settings,
} from 'lucide-react';
import { InterviewVersion, UpdateVersionRequest } from '@/types/interview-builder';
import {
  getInterviewSetWithLastVersion,
  getInterviewsList,
  getVersion,
  updateVersion,
} from '@/lib/api/interview-builder';
import {
  YAMLEditor,
  QuestionBuilder,
  YAMLInterviewPreview,
} from '@/components/dashboard/admin/interview-builder';
import { useQuery } from '@tanstack/react-query';

export default function BuildVersionPage() {
  const router = useRouter();
  const params = useParams();
  const versionId = params.id as string;

  const { data: interviewSet, isLoading, refetch } =
    useQuery({
      queryKey: ['interview-set', versionId],
      queryFn: () => getInterviewSetWithLastVersion(versionId),
    });

  console.log('===> interviewSet', interviewSet);

  const [version, setVersion] = useState<InterviewVersion | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('questions');
  const [yamlContent, setYamlContent] = useState('');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    if (versionId) {
      loadVersion();
    }
  }, [versionId]);

  const loadVersion = async () => {
    try {
      setLoading(true);
      setError(null);
      const versionData = await getVersion(versionId);
      if (!versionData) {
        setError('Version not found.');
        return;
      }
      setVersion(versionData);
      setYamlContent(versionData.yamlContent || '');
    } catch (err) {
      setError('Failed to load version. Please try again.');
      console.error('Error loading version:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!version) return;

    try {
      setSaving(true);
      setError(null);

      const updateData: UpdateVersionRequest = {
        yamlContent,
        status: version.status,
      };

      await updateVersion(versionId, updateData);
      setHasUnsavedChanges(false);
      
      // Reload version to get updated data
      await loadVersion();
    } catch (err) {
      setError('Failed to save version. Please try again.');
      console.error('Error saving version:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleYAMLChange = (newYaml: string) => {
    setYamlContent(newYaml);
    setHasUnsavedChanges(true);
  };

  const handlePreview = () => {
    if (version) {
      router.push(`/admin/interview-builder/preview/${version.id}`);
    }
  };

  const handleBack = () => {
    if (hasUnsavedChanges) {
      if (confirm('You have unsaved changes. Are you sure you want to leave?')) {
        router.push('/admin/interview-builder');
      }
    } else {
      router.push('/admin/interview-builder');
    }
  };

  if (isLoading) {
    return (
      <div className='max-w-7xl mx-auto'>
        <div className='flex items-center justify-center h-64'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto'></div>
            <p className='mt-2 text-[var(--custom-gray-medium)]'>Loading version...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !version) {
    return (
      <div className='max-w-7xl mx-auto'>
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error || 'Version not found'}</AlertDescription>
        </Alert>
        <div className='mt-4'>
          <Button
            onClick={() => router.push('/admin/interview-builder')}
            variant='outline'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Interview Builder
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='max-w-7xl mx-auto'>
      {/* Header */}
      <div className='flex items-center justify-between mb-6'>
        <div className='flex items-center space-x-4'>
          <Button onClick={handleBack} variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)] flex items-center'>
              <MessageSquare className='mr-3 h-8 w-8 text-blue-600' />
              Edit: {interviewSet?.name}
            </h1>
            <p className='text-[var(--custom-gray-medium)] mt-1'>{interviewSet?.description}</p>
          </div>
        </div>
        <div className='flex items-center space-x-2'>
          <Button
            onClick={handlePreview}
            variant='outline'
            size='sm'
          >
            <Eye className='mr-2 h-4 w-4' />
            Preview
          </Button>
          <Button
            onClick={handleSave}
            disabled={saving || !hasUnsavedChanges}
            size='sm'
          >
            <Save className='mr-2 h-4 w-4' />
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant='destructive' className='mb-6'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Unsaved Changes Warning */}
      {hasUnsavedChanges && (
        <Alert className='mb-6'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>
            You have unsaved changes. Don't forget to save your work.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Version Builder</CardTitle>
          <CardDescription>
            Build and edit your interview version using the tools below
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='questions'>Question Builder</TabsTrigger>
              <TabsTrigger value='preview'>Live Preview</TabsTrigger>
            </TabsList>

            <TabsContent value='questions' className='mt-6'>
              <QuestionBuilder
                interviewId={interviewSet?.id || ''}
                versionId={interviewSet?.lastestVersionId || ''}
                questions={interviewSet?.questions || []}
                onQuestionsUpdated={loadVersion}
                refetch={refetch}
              />
            </TabsContent>

            <TabsContent value='preview' className='mt-6'>
              <YAMLInterviewPreview
                yamlContent={yamlContent}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
