'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  MessageSquare,
  AlertTriangle,
  Eye,
  Play,
} from 'lucide-react';
import { Interview, InterviewQuestion } from '@/types/interview-builder';
import { getInterview, getQuestions } from '@/lib/api/interview-builder';
import { InterviewPreview } from '@/components/dashboard/admin/interview-builder/interview-preview';

export default function InterviewPreviewPage() {
  const router = useRouter();
  const params = useParams();
  const interviewId = params.id as string;

  const [interview, setInterview] = useState<Interview | null>(null);
  const [questions, setQuestions] = useState<InterviewQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (interviewId) {
      loadData();
    }
  }, [interviewId]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [interviewData, questionsData] = await Promise.all([
        getInterview(),
        getQuestions(interviewId),
      ]);

      if (interviewData) {
        setInterview(interviewData.interview);
        setQuestions(questionsData.questions);
      } else {
        setError('Interview not found');
      }
    } catch (err) {
      setError('Failed to load interview. Please try again.');
      console.error('Error loading interview:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className='max-w-7xl mx-auto'>
        <div className='animate-pulse space-y-6'>
          <div className='h-8 bg-gray-200 rounded w-1/3'></div>
          <div className='h-32 bg-gray-200 rounded'></div>
          <div className='h-96 bg-gray-200 rounded'></div>
        </div>
      </div>
    );
  }

  if (error || !interview) {
    return (
      <div className='max-w-7xl mx-auto'>
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>{error || 'Interview not found'}</AlertDescription>
        </Alert>
        <div className='mt-4'>
          <Button
            onClick={() => router.push('/admin/interview-builder')}
            variant='outline'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Interview Builder
          </Button>
        </div>
      </div>
    );
  }

  // Get the current version
  const currentVersion = interview.versions.find(
    v => v.version === interview.currentVersion
  );
  if (!currentVersion) {
    return (
      <div className='max-w-7xl mx-auto'>
        <Alert variant='destructive'>
          <AlertTriangle className='h-4 w-4' />
          <AlertDescription>Current version not found</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className='max-w-7xl mx-auto'>
      {/* Header */}
      <div className='flex items-center justify-between mb-6'>
        <div className='flex items-center space-x-4'>
          <Button
            onClick={() => router.push('/admin/interview-builder')}
            variant='outline'
            size='sm'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)] flex items-center'>
              <Eye className='mr-3 h-8 w-8 text-blue-600' />
              Preview: {currentVersion.name}
            </h1>
            <p className='text-[var(--custom-gray-medium)] mt-1'>
              {currentVersion.description}
            </p>
          </div>
        </div>
        <div className='flex items-center space-x-2'>
          <Button
            onClick={() =>
              router.push(`/admin/interview-builder/edit/${interview.id}`)
            }
            variant='outline'
            size='sm'
          >
            Edit Interview
          </Button>
        </div>
      </div>

      {/* Interview Info */}
      <Card className='mb-6'>
        <CardHeader>
          <CardTitle className='flex items-center'>
            <MessageSquare className='mr-2 h-5 w-5' />
            Interview Information
          </CardTitle>
          <CardDescription>
            Preview how this interview will appear to members
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div>
              <div className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                Status
              </div>
              <div className='mt-1'>
                {currentVersion.status === 'published' ? (
                  <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800'>
                    <Play className='mr-1 h-3 w-3' />
                    Published
                  </span>
                ) : (
                  <span className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-[var(--custom-gray-dark)]'>
                    Draft
                  </span>
                )}
              </div>
            </div>
            <div>
              <div className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                Questions
              </div>
              <div className='mt-1 text-lg font-semibold'>
                {questions.length}
              </div>
            </div>
            <div>
              <div className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                Estimated Duration
              </div>
              <div className='mt-1 text-lg font-semibold'>
                {currentVersion.estimatedDuration} minutes
              </div>
            </div>
            <div>
              <div className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                Target Template
              </div>
              <div className='mt-1 text-sm'>
                {currentVersion.targetTemplate || 'No template specified'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview Component */}
      <InterviewPreview interview={interview} questions={questions} />
    </div>
  );
}
