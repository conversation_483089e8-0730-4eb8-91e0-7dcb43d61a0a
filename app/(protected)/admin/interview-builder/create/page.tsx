'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ArrowLeft,
  Save,
  MessageSquare,
  AlertTriangle,
  FileText,
  Settings,
} from 'lucide-react';
import {
  CreateVersionRequest,
  VersionFormData,
  InterviewVersion,
} from '@/types/interview-builder';
import {
  createVersion,
  validateVersionData,
  getVersions,
  createInterview,
} from '@/lib/api/interview-builder';

const TEMPLATE_OPTIONS = [
  { value: 'basic-estate-plan', label: 'Basic Estate Plan' },
  { value: 'advanced-trust', label: 'Advanced Trust Planning' },
  { value: 'healthcare-directives', label: 'Healthcare Directives' },
  { value: 'business-succession', label: 'Business Succession' },
  { value: 'charitable-planning', label: 'Charitable Planning' },
  { value: 'special-needs', label: 'Special Needs Planning' },
];

export default function CreateVersionPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<VersionFormData>({
    name: '',
    description: '',
    targetTemplate: '',
  });
  const [availableVersions, setAvailableVersions] = useState<
    InterviewVersion[]
  >([]);
  const [selectedParentVersion, setSelectedParentVersion] =
    useState<string>('');
  const [loading, setLoading] = useState(true);

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadAvailableVersions();
  }, []);

  const loadAvailableVersions = async () => {
    try {
      setLoading(true);
      const response = await getVersions(1, 100); // Get all versions
      setAvailableVersions(response.versions);
    } catch (err) {
      console.error('Error loading versions:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (field: keyof VersionFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    const validationErrors = validateVersionData(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setSaving(true);
      setErrors({});

      const createData: CreateVersionRequest = {
        name: formData.name,
        description: formData.description,
        targetTemplate: formData.targetTemplate || undefined,
        parentVersionId: selectedParentVersion || undefined,
      };

      const newVersion = await createInterview(createData);

      // Redirect to build page to start editing the version
      router.push(`/admin/interview-builder/build/${newVersion.id}`);
    } catch (err) {
      setErrors({ submit: 'Failed to create version. Please try again.' });
      console.error('Error creating version:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.push('/admin/interview-builder');
  };

  return (
    <div className='max-w-4xl mx-auto'>
      {/* Header */}
      <div className='flex items-center justify-between mb-6'>
        <div className='flex items-center space-x-4'>
          <Button onClick={handleCancel} variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Interview Builder
          </Button>
          <div>
            <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)] flex items-center'>
              <MessageSquare className='mr-3 h-8 w-8 text-blue-600' />
              Create New Version
            </h1>
            <p className='text-[var(--custom-gray-medium)] mt-1'>
              Create a new version of the interview or duplicate from an
              existing version
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className='space-y-6'>
        {errors.submit && (
          <Alert variant='destructive'>
            <AlertTriangle className='h-4 w-4' />
            <AlertDescription>{errors.submit}</AlertDescription>
          </Alert>
        )}

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center'>
              <FileText className='mr-2 h-5 w-5' />
              Basic Information
            </CardTitle>
            <CardDescription>Interview details and metadata</CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='space-y-2'>
              <Label htmlFor='name'>
                Version Name <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='name'
                value={formData.name}
                onChange={e => handleFieldChange('name', e.target.value)}
                placeholder='e.g., Advanced Estate Planning v2'
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className='text-sm text-red-500'>{errors.name}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>
                Description <span className='text-red-500'>*</span>
              </Label>
              <Textarea
                id='description'
                value={formData.description}
                onChange={e => handleFieldChange('description', e.target.value)}
                placeholder="Describe what's new or different in this version..."
                rows={4}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <p className='text-sm text-red-500'>{errors.description}</p>
              )}
            </div>

            {/*<div className='space-y-2'>*/}
            {/*  <Label htmlFor='parentVersion'>Parent Version (Optional)</Label>*/}
            {/*  <Select*/}
            {/*    value={selectedParentVersion}*/}
            {/*    onValueChange={setSelectedParentVersion}*/}
            {/*  >*/}
            {/*    <SelectTrigger>*/}
            {/*      <SelectValue placeholder='Select a version to duplicate from (optional)' />*/}
            {/*    </SelectTrigger>*/}
            {/*    <SelectContent>*/}
            {/*      <SelectItem value=''>Create from scratch</SelectItem>*/}
            {/*      {availableVersions.map(version => (*/}
            {/*        <SelectItem key={version.id} value={version.id}>*/}
            {/*          v{version.version} - {version.name} ({version.status})*/}
            {/*        </SelectItem>*/}
            {/*      ))}*/}
            {/*    </SelectContent>*/}
            {/*  </Select>*/}
            {/*  <p className='text-sm text-[var(--custom-gray-medium)]'>*/}
            {/*    Choose an existing version to duplicate its content, or create*/}
            {/*    from scratch.*/}
            {/*  </p>*/}
            {/*</div>*/}

            {/*<div className='space-y-2'>*/}
            {/*  <Label htmlFor='targetTemplate'>Target Template</Label>*/}
            {/*  <Select*/}
            {/*    value={formData.targetTemplate}*/}
            {/*    onValueChange={value =>*/}
            {/*      handleFieldChange('targetTemplate', value)*/}
            {/*    }*/}
            {/*  >*/}
            {/*    <SelectTrigger>*/}
            {/*      <SelectValue placeholder='Select a document template (optional)' />*/}
            {/*    </SelectTrigger>*/}
            {/*    <SelectContent>*/}
            {/*      <SelectItem value=''>No specific template</SelectItem>*/}
            {/*      {TEMPLATE_OPTIONS.map(template => (*/}
            {/*        <SelectItem key={template.value} value={template.value}>*/}
            {/*          {template.label}*/}
            {/*        </SelectItem>*/}
            {/*      ))}*/}
            {/*    </SelectContent>*/}
            {/*  </Select>*/}
            {/*  <p className='text-sm text-[var(--custom-gray-medium)]'>*/}
            {/*    Link this interview to a specific document template for*/}
            {/*    automatic data mapping.*/}
            {/*  </p>*/}
            {/*</div>*/}
          </CardContent>
        </Card>

        {/* Version Guidelines */}
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center'>
              <Settings className='mr-2 h-5 w-5' />
              Version Guidelines
            </CardTitle>
            <CardDescription>
              Best practices for creating interview versions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-3 text-sm text-[var(--custom-gray-medium)]'>
              <div className='flex items-start space-x-2'>
                <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                <p>
                  Each version maintains complete history and can be
                  independently published
                </p>
              </div>
              <div className='flex items-start space-x-2'>
                <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                <p>
                  Duplicate from existing versions to build upon previous work
                </p>
              </div>
              <div className='flex items-start space-x-2'>
                <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                <p>
                  Use YAML editing for complex conditional logic and branching
                </p>
              </div>
              <div className='flex items-start space-x-2'>
                <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                <p>
                  Visual diagrams automatically generate from YAML structure
                </p>
              </div>
              <div className='flex items-start space-x-2'>
                <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0'></div>
                <p>Test thoroughly before publishing to ensure proper flow</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className='flex justify-between'>
          <Button
            type='button'
            variant='outline'
            onClick={handleCancel}
            disabled={saving}
          >
            Cancel
          </Button>
          <Button type='submit' disabled={saving || loading}>
            <Save className='mr-2 h-4 w-4' />
            {saving ? 'Creating...' : 'Create Version'}
          </Button>
        </div>
      </form>
    </div>
  );
}
