'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ArrowLeft, History, FileEdit, Eye, AlertTriangle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

// Define the type for template version
type TemplateVersion = {
  id: string;
  version: string;
  timestamp: string;
  summary: string;
  legalUpdateId: string | null;
  startDate: string;
  endDate: string | null;
  content: string;
};

// Define the type for template versions map
type TemplateVersionsMap = {
  [key: string]: TemplateVersion[];
};

// Mock template version history data
const templateVersions: TemplateVersionsMap = {
  '1': [
    {
      id: '1-v3',
      version: '1.0',
      timestamp: '2023-01-01T10:30:00Z',
      summary: 'Initial version of California Will template',
      legalUpdateId: null,
      startDate: '2023-01-01',
      endDate: null,
      content: `LAST WILL AND TESTAMENT OF [Member_NAME]

I, [Member_NAME], residing at [Member_ADDRESS], being of sound mind, hereby make, publish, and declare this to be my Last Will and Testament, hereby revoking all wills and codicils heretofore made by me.

ARTICLE I: PERSONAL INFORMATION
I declare that I was born on [Member_DOB].

ARTICLE II: APPOINTMENT OF EXECUTOR
I appoint [EXECUTOR] as the Executor of this Will. If [EXECUTOR] is unable or unwilling to serve, then I appoint [BENEFICIARY] as the alternate Executor.

ARTICLE III: DISTRIBUTION OF PROPERTY
I give, devise, and bequeath all of my property, real, personal, and mixed, of whatever kind and wherever situated, which I may own or have the right to dispose of at the time of my death to [BENEFICIARY].

ARTICLE IV: WITNESSES
This Will shall be signed by me and witnessed by [WITNESS] and [WITNESS], who shall sign in my presence and in the presence of each other.

IN WITNESS WHEREOF, I have hereunto set my hand this [TODAY_DATE].

____________________________
[Member_NAME], Testator

WITNESSES:
____________________________
[WITNESS], Witness

____________________________
[WITNESS], Witness`,
    },
    {
      id: '1-v2',
      version: '0.9',
      timestamp: '2022-11-15T14:45:00Z',
      summary: 'Draft version with updated witness requirements',
      legalUpdateId: 'LU-2022-11',
      startDate: '2022-11-15',
      endDate: '2023-01-01',
      content: `LAST WILL AND TESTAMENT OF [Member_NAME]

I, [Member_NAME], residing at [Member_ADDRESS], being of sound mind, hereby make, publish, and declare this to be my Last Will and Testament, hereby revoking all wills and codicils heretofore made by me.

ARTICLE I: PERSONAL INFORMATION
I declare that I was born on [Member_DOB].

ARTICLE II: APPOINTMENT OF EXECUTOR
I appoint [EXECUTOR] as the Executor of this Will.

ARTICLE III: DISTRIBUTION OF PROPERTY
I give, devise, and bequeath all of my property, real, personal, and mixed, of whatever kind and wherever situated, which I may own or have the right to dispose of at the time of my death to [BENEFICIARY].

ARTICLE IV: WITNESSES
This Will shall be signed by me and witnessed by [WITNESS] and [WITNESS], who shall sign in my presence and in the presence of each other.

IN WITNESS WHEREOF, I have hereunto set my hand this [TODAY_DATE].

____________________________
[Member_NAME], Testator

WITNESSES:
____________________________
[WITNESS], Witness

____________________________
[WITNESS], Witness`,
    },
    {
      id: '1-v1',
      version: '0.8',
      timestamp: '2022-09-20T09:15:00Z',
      summary: 'Initial draft of California Will template',
      legalUpdateId: null,
      startDate: '2022-09-20',
      endDate: '2022-11-15',
      content: `LAST WILL AND TESTAMENT OF [Member_NAME]

I, [Member_NAME], residing at [Member_ADDRESS], being of sound mind, hereby make, publish, and declare this to be my Last Will and Testament.

ARTICLE I: PERSONAL INFORMATION
I declare that I was born on [Member_DOB].

ARTICLE II: APPOINTMENT OF EXECUTOR
I appoint [EXECUTOR] as the Executor of this Will.

ARTICLE III: DISTRIBUTION OF PROPERTY
I give, devise, and bequeath all of my property to [BENEFICIARY].

ARTICLE IV: WITNESSES
This Will shall be signed by me and witnessed by [WITNESS] and [WITNESS].

IN WITNESS WHEREOF, I have hereunto set my hand this [TODAY_DATE].

____________________________
[Member_NAME], Testator

WITNESSES:
____________________________
[WITNESS], Witness

____________________________
[WITNESS], Witness`,
    },
  ],
};

// Define the type for template metadata
type TemplateMetadata = {
  id: string;
  state: string;
  type: string;
  currentVersion: string;
  status: string;
};

// Define the type for template metadata map
type TemplateMetadataMap = {
  [key: string]: TemplateMetadata;
};

// Mock template metadata
const templateMetadata: TemplateMetadataMap = {
  '1': {
    id: '1',
    state: 'California',
    type: 'Will',
    currentVersion: '1.0',
    status: 'Active',
  },
};

export default function TemplateHistoryPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;

  const [template, setTemplate] = useState<TemplateMetadata | null>(null);
  const [versions, setVersions] = useState<TemplateVersion[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [selectedVersion, setSelectedVersion] =
    useState<TemplateVersion | null>(null);

  useEffect(() => {
    // Load template metadata
    if (templateMetadata[id]) {
      setTemplate(templateMetadata[id]);
    } else {
      setError('Template not found');
    }

    // Load template versions
    if (templateVersions[id]) {
      setVersions(templateVersions[id]);
    } else {
      setVersions([]);
    }
  }, [id]);

  const handleViewVersion = (version: TemplateVersion) => {
    setSelectedVersion(version);
  };

  const handleRestoreVersion = (versionId: string) => {
    // In a real implementation, this would create a new draft from the selected version
    router.push(`/admin/templates/edit/${id}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateTimeString: string) => {
    return new Date(dateTimeString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex items-center mb-6'>
        <Button
          variant='outline'
          size='sm'
          onClick={() => router.push('/admin/templates')}
          className='mr-4'
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Templates
        </Button>
        <div>
          <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)]'>
            {template
              ? `Version History: ${template.state} ${template.type}`
              : 'Template History'}
          </h1>
          <p className='text-[var(--custom-gray-medium)] mt-1'>
            View all versions and changes for this template
          </p>
        </div>
      </div>

      {error && (
        <Alert variant='destructive' className='mb-6'>
          <AlertTriangle className='h-4 w-4' />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {template && (
        <Card className='mb-6'>
          <CardHeader>
            <CardTitle>Template Information</CardTitle>
            <CardDescription>
              Current details about this template
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  State
                </p>
                <p>{template.state}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Document Type
                </p>
                <p>{template.type}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Current Version
                </p>
                <p>v{template.currentVersion}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Status
                </p>
                <p>{template.status}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Version History</CardTitle>
          <CardDescription>All versions of this template</CardDescription>
        </CardHeader>
        <CardContent>
          {versions.length === 0 ? (
            <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
              No version history found for this template.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Version</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>End Date</TableHead>
                  <TableHead>Summary</TableHead>
                  <TableHead>Legal Update</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {versions.map(version => (
                  <TableRow key={version.id}>
                    <TableCell>v{version.version}</TableCell>
                    <TableCell>{formatDateTime(version.timestamp)}</TableCell>
                    <TableCell>{formatDate(version.startDate)}</TableCell>
                    <TableCell>
                      {version.endDate
                        ? formatDate(version.endDate)
                        : 'Current'}
                    </TableCell>
                    <TableCell>{version.summary}</TableCell>
                    <TableCell>{version.legalUpdateId || 'N/A'}</TableCell>
                    <TableCell>
                      <div className='flex space-x-1'>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant='outline'
                              size='sm'
                              onClick={() => handleViewVersion(version)}
                              className='h-8 w-8 p-0'
                            >
                              <Eye className='h-4 w-4' />
                              <span className='sr-only'>View</span>
                            </Button>
                          </DialogTrigger>
                          <DialogContent className='max-w-4xl max-h-[80vh] overflow-y-auto'>
                            <DialogHeader>
                              <DialogTitle>
                                Template Version v{version.version}
                              </DialogTitle>
                              <DialogDescription>
                                Created on {formatDateTime(version.timestamp)}
                              </DialogDescription>
                            </DialogHeader>
                            <div className='border p-6 rounded-md bg-background whitespace-pre-wrap font-serif'>
                              {version.content}
                            </div>
                          </DialogContent>
                        </Dialog>

                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleRestoreVersion(version.id)}
                          className='h-8 w-8 p-0'
                        >
                          <FileEdit className='h-4 w-4' />
                          <span className='sr-only'>Restore</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
