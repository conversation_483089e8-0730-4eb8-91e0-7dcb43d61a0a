'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  Send,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
} from 'lucide-react';

// Mock data for template updates
const templateUpdates = [
  {
    id: 'update-1',
    templateId: '1',
    state: 'California',
    type: 'Will',
    oldVersion: '0.9',
    newVersion: '1.0',
    updateType: 'Language Change',
    updateDate: '2023-01-01',
    affectedMembers: 12,
    notifiedMembers: 12,
    updatedMembers: 10,
    pendingMembers: 2,
    status: 'In Progress',
  },
  {
    id: 'update-2',
    templateId: '2',
    state: 'California',
    type: 'Trust',
    oldVersion: '2.0',
    newVersion: '2.1',
    updateType: 'Member Information Change',
    updateDate: '2023-02-15',
    affectedMembers: 8,
    notifiedMembers: 8,
    updatedMembers: 5,
    pendingMembers: 3,
    status: 'In Progress',
  },
  {
    id: 'update-3',
    templateId: '3',
    state: 'New York',
    type: 'Healthcare POA',
    oldVersion: '1.1',
    newVersion: '1.2',
    updateType: 'Language Change',
    updateDate: '2023-03-10',
    affectedMembers: 5,
    notifiedMembers: 5,
    updatedMembers: 5,
    pendingMembers: 0,
    status: 'Completed',
  },
];

// Mock data for affected members
const affectedMembers = {
  'update-1': [
    {
      id: 'member-1',
      name: 'John Doe',
      email: '<EMAIL>',
      documentId: 'doc-101',
      status: 'Updated',
      updateDate: '2023-01-05',
    },
    {
      id: 'member-2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      documentId: 'doc-102',
      status: 'Updated',
      updateDate: '2023-01-03',
    },
    {
      id: 'member-3',
      name: 'Robert Johnson',
      email: '<EMAIL>',
      documentId: 'doc-103',
      status: 'Updated',
      updateDate: '2023-01-10',
    },
    {
      id: 'member-4',
      name: 'Emily Davis',
      email: '<EMAIL>',
      documentId: 'doc-104',
      status: 'Updated',
      updateDate: '2023-01-07',
    },
    {
      id: 'member-5',
      name: 'Michael Wilson',
      email: '<EMAIL>',
      documentId: 'doc-105',
      status: 'Updated',
      updateDate: '2023-01-12',
    },
    {
      id: 'member-6',
      name: 'Sarah Brown',
      email: '<EMAIL>',
      documentId: 'doc-106',
      status: 'Updated',
      updateDate: '2023-01-08',
    },
    {
      id: 'member-7',
      name: 'David Miller',
      email: '<EMAIL>',
      documentId: 'doc-107',
      status: 'Updated',
      updateDate: '2023-01-15',
    },
    {
      id: 'member-8',
      name: 'Jennifer Taylor',
      email: '<EMAIL>',
      documentId: 'doc-108',
      status: 'Updated',
      updateDate: '2023-01-06',
    },
    {
      id: 'member-9',
      name: 'James Anderson',
      email: '<EMAIL>',
      documentId: 'doc-109',
      status: 'Updated',
      updateDate: '2023-01-09',
    },
    {
      id: 'member-10',
      name: 'Lisa Thomas',
      email: '<EMAIL>',
      documentId: 'doc-110',
      status: 'Updated',
      updateDate: '2023-01-11',
    },
    {
      id: 'member-11',
      name: 'Richard Harris',
      email: '<EMAIL>',
      documentId: 'doc-111',
      status: 'Pending',
      updateDate: null,
    },
    {
      id: 'member-12',
      name: 'Patricia Clark',
      email: '<EMAIL>',
      documentId: 'doc-112',
      status: 'Pending',
      updateDate: null,
    },
  ],
  'update-2': [
    {
      id: 'member-13',
      name: 'William Lee',
      email: '<EMAIL>',
      documentId: 'doc-201',
      status: 'Updated',
      updateDate: '2023-02-20',
    },
    {
      id: 'member-14',
      name: 'Elizabeth Walker',
      email: '<EMAIL>',
      documentId: 'doc-202',
      status: 'Updated',
      updateDate: '2023-02-18',
    },
    {
      id: 'member-15',
      name: 'Thomas Hall',
      email: '<EMAIL>',
      documentId: 'doc-203',
      status: 'Updated',
      updateDate: '2023-02-25',
    },
    {
      id: 'member-16',
      name: 'Susan Allen',
      email: '<EMAIL>',
      documentId: 'doc-204',
      status: 'Updated',
      updateDate: '2023-02-22',
    },
    {
      id: 'member-17',
      name: 'Charles Young',
      email: '<EMAIL>',
      documentId: 'doc-205',
      status: 'Updated',
      updateDate: '2023-02-28',
    },
    {
      id: 'member-18',
      name: 'Karen Scott',
      email: '<EMAIL>',
      documentId: 'doc-206',
      status: 'Pending',
      updateDate: null,
    },
    {
      id: 'member-19',
      name: 'Joseph Green',
      email: '<EMAIL>',
      documentId: 'doc-207',
      status: 'Pending',
      updateDate: null,
    },
    {
      id: 'member-20',
      name: 'Nancy Baker',
      email: '<EMAIL>',
      documentId: 'doc-208',
      status: 'Pending',
      updateDate: null,
    },
  ],
  'update-3': [
    {
      id: 'member-21',
      name: 'Daniel Adams',
      email: '<EMAIL>',
      documentId: 'doc-301',
      status: 'Updated',
      updateDate: '2023-03-12',
    },
    {
      id: 'member-22',
      name: 'Margaret Nelson',
      email: '<EMAIL>',
      documentId: 'doc-302',
      status: 'Updated',
      updateDate: '2023-03-11',
    },
    {
      id: 'member-23',
      name: 'Paul Carter',
      email: '<EMAIL>',
      documentId: 'doc-303',
      status: 'Updated',
      updateDate: '2023-03-15',
    },
    {
      id: 'member-24',
      name: 'Betty Mitchell',
      email: '<EMAIL>',
      documentId: 'doc-304',
      status: 'Updated',
      updateDate: '2023-03-14',
    },
    {
      id: 'member-25',
      name: 'Donald Roberts',
      email: '<EMAIL>',
      documentId: 'doc-305',
      status: 'Updated',
      updateDate: '2023-03-13',
    },
  ],
};

export default function TemplatePropagationPage() {
  const router = useRouter();
  const [selectedUpdate, setSelectedUpdate] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');

  const handleSelectUpdate = (updateId: string) => {
    setSelectedUpdate(updateId);
    setSearchTerm('');
    setStatusFilter('All');
  };

  const handleSendReminders = () => {
    // In a real implementation, this would send reminders to pending members
    alert('Reminders sent to pending members');
  };

  const handleRegenerateDocuments = () => {
    // In a real implementation, this would regenerate documents for pending members
    alert('Documents regenerated for pending members');
  };

  // Filter members based on search term and status
  const filteredMembers =
    selectedUpdate && selectedUpdate in affectedMembers
      ? affectedMembers[selectedUpdate as keyof typeof affectedMembers].filter(
          member =>
            (member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              member.email.toLowerCase().includes(searchTerm.toLowerCase())) &&
            (statusFilter === 'All' || member.status === statusFilter)
        )
      : [];

  // Get selected update details
  const selectedUpdateDetails = selectedUpdate
    ? templateUpdates.find(update => update.id === selectedUpdate)
    : null;

  return (
    <div className='max-w-7xl mx-auto'>
      <div className='flex items-center mb-6'>
        <Button
          variant='outline'
          size='sm'
          onClick={() => router.push('/admin/templates')}
          className='mr-4'
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back to Templates
        </Button>
        <div>
          <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)]'>
            Template Update Propagation
          </h1>
          <p className='text-[var(--custom-gray-medium)] mt-1'>
            Monitor and manage template updates across user documents
          </p>
        </div>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        <div className='md:col-span-1'>
          <Card>
            <CardHeader>
              <CardTitle>Recent Template Updates</CardTitle>
              <CardDescription>
                Select an update to view affected members
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {templateUpdates.map(update => (
                  <div
                    key={update.id}
                    className={`p-4 border rounded-md cursor-pointer transition-colors ${
                      selectedUpdate === update.id
                        ? 'border-green-2010c bg-green-2010c/10'
                        : 'hover:border-gray-300'
                    }`}
                    onClick={() => handleSelectUpdate(update.id)}
                  >
                    <div className='flex justify-between items-start mb-2'>
                      <h3 className='font-medium'>
                        {update.state} {update.type}
                      </h3>
                      <Badge
                        variant={
                          update.status === 'Completed' ? 'default' : 'outline'
                        }
                        className={
                          update.status === 'Completed' ? 'bg-green-2010c' : ''
                        }
                      >
                        {update.status}
                      </Badge>
                    </div>
                    <p className='text-sm text-[var(--custom-gray-medium)] mb-1'>
                      v{update.oldVersion} → v{update.newVersion}
                    </p>
                    <p className='text-sm text-[var(--custom-gray-medium)] mb-1'>
                      Type: {update.updateType}
                    </p>
                    <p className='text-sm text-[var(--custom-gray-medium)] mb-1'>
                      Updated: {update.updateDate}
                    </p>
                    <div className='mt-2 flex justify-between text-sm'>
                      <span>{update.affectedMembers} members affected</span>
                      <span>
                        {update.updatedMembers}/{update.affectedMembers} updated
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='md:col-span-2'>
          {selectedUpdate ? (
            <>
              <Card className='mb-6'>
                <CardHeader>
                  <CardTitle>Update Details</CardTitle>
                  <CardDescription>
                    Information about the selected template update
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        State
                      </p>
                      <p>{selectedUpdateDetails?.state}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Document Type
                      </p>
                      <p>{selectedUpdateDetails?.type}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Version Change
                      </p>
                      <p>
                        v{selectedUpdateDetails?.oldVersion} → v
                        {selectedUpdateDetails?.newVersion}
                      </p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Update Type
                      </p>
                      <p>{selectedUpdateDetails?.updateType}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Update Date
                      </p>
                      <p>{selectedUpdateDetails?.updateDate}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Status
                      </p>
                      <p>{selectedUpdateDetails?.status}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Affected Members
                      </p>
                      <p>{selectedUpdateDetails?.affectedMembers}</p>
                    </div>
                    <div>
                      <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                        Updated Members
                      </p>
                      <p>
                        {selectedUpdateDetails?.updatedMembers}/
                        {selectedUpdateDetails?.affectedMembers}
                      </p>
                    </div>
                  </div>

                  {(selectedUpdateDetails?.pendingMembers ?? 0) > 0 && (
                    <div className='mt-4 flex space-x-4'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={handleSendReminders}
                      >
                        <Send className='mr-2 h-4 w-4' />
                        Send Reminders
                      </Button>
                      {selectedUpdateDetails?.updateType ===
                        'Language Change' && (
                        <Button
                          variant='default'
                          size='sm'
                          onClick={handleRegenerateDocuments}
                        >
                          <RefreshCw className='mr-2 h-4 w-4' />
                          Auto-Regenerate Documents
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Affected Members</CardTitle>
                  <CardDescription>
                    Members whose documents need to be updated
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='flex justify-between mb-4'>
                    <div className='w-1/2 mr-2'>
                      <Input
                        placeholder='Search by name or email'
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <div className='w-1/2 ml-2'>
                      <Select
                        value={statusFilter}
                        onValueChange={setStatusFilter}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Filter by status' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='All'>All Statuses</SelectItem>
                          <SelectItem value='Updated'>Updated</SelectItem>
                          <SelectItem value='Pending'>Pending</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {filteredMembers.length === 0 ? (
                    <div className='text-center py-8 text-[var(--custom-gray-medium)]'>
                      No members found matching your filters.
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Member Name</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Document ID</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Update Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredMembers.map(member => (
                          <TableRow key={member.id}>
                            <TableCell>{member.name}</TableCell>
                            <TableCell>{member.email}</TableCell>
                            <TableCell>{member.documentId}</TableCell>
                            <TableCell>
                              <div className='flex items-center'>
                                {member.status === 'Updated' ? (
                                  <CheckCircle className='mr-2 h-4 w-4 text-green-500' />
                                ) : (
                                  <Clock className='mr-2 h-4 w-4 text-amber-500' />
                                )}
                                {member.status}
                              </div>
                            </TableCell>
                            <TableCell>
                              {member.updateDate || 'Pending'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <div className='flex items-center justify-center h-full min-h-[300px] border border-dashed rounded-lg'>
              <div className='text-center p-6'>
                <AlertCircle className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)] mb-4' />
                <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-1'>
                  No Update Selected
                </h3>
                <p className='text-[var(--custom-gray-medium)]'>
                  Select a template update from the list to view affected
                  members.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
