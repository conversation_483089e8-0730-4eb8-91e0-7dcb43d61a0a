'use client';

import React, { useState } from 'react';
import { useAdminAccess } from '@/lib/hooks/useAdminAccess';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserPlus } from 'lucide-react';
import { InvitesTable } from '@/components/dashboard/admin/invites-table';
import { InviteUserModal } from '@/components/dashboard/admin/invite-user-modal';

export default function InvitesPage() {
  const { canManageUsers, userRoles } = useAdminAccess();
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  // Additional security check at component level
  if (!canManageUsers) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                You don't have permission to manage invitations.
              </p>
              <p className='text-sm text-muted-foreground mt-2'>
                This feature requires full administrator privileges.
              </p>
              {userRoles.length > 0 && (
                <p className='text-sm text-muted-foreground mt-1'>
                  Your roles: {userRoles.join(', ')}
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className='h-full space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Invitations</h1>
          <p className='text-muted-foreground'>
            Send invitations to new Welon Trust members and manage existing invitations.
          </p>
        </div>
        <Button onClick={() => setIsInviteModalOpen(true)}>
          <UserPlus className='mr-2 h-4 w-4' />
          Send Invitation
        </Button>
      </div>

      {/* Invites Table */}
      <InvitesTable />

      {/* Invite User Modal */}
      <InviteUserModal
        open={isInviteModalOpen}
        onOpenChange={setIsInviteModalOpen}
        onInviteSent={() => {
          // The table will automatically refresh via the hook
        }}
      />
    </div>
  );
}
