'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  RoleManagementTable,
  SubRole,
} from '@/components/dashboard/admin/role-management-table';
import { PermissionEditor } from '@/components/dashboard/admin/permission-editor';
import {
  BulkRoleEditor,
  BulkRoleUpdate,
} from '@/components/dashboard/admin/bulk-role-editor';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

import {
  ArrowLeft,
  Shield,
  Users,
  Settings,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';

export default function RoleManagementPage() {
  const router = useRouter();
  const [selectedRole, setSelectedRole] = useState<SubRole | null>(null);
  const [isPermissionEditorOpen, setIsPermissionEditorOpen] = useState(false);
  const [isBulkEditorOpen, setIsBulkEditorOpen] = useState(false);
  const [selectedRolesForBulk, setSelectedRolesForBulk] = useState<SubRole[]>(
    []
  );
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const handleEditRole = (role: SubRole) => {
    setSelectedRole(role);
    setIsPermissionEditorOpen(true);
  };

  const handleBulkEdit = (selectedRoles: SubRole[]) => {
    setSelectedRolesForBulk(selectedRoles);
    setIsBulkEditorOpen(true);
  };

  const handleSaveRole = (updatedRole: SubRole) => {
    // In a real implementation, this would make an API call
    console.log('Saving role:', updatedRole);

    setNotification({
      type: 'success',
      message: `Role "${updatedRole.name}" has been updated successfully.`,
    });

    // Auto-hide notification after 5 seconds
    setTimeout(() => setNotification(null), 5000);
  };

  const handleSaveBulkChanges = (updates: BulkRoleUpdate[]) => {
    // In a real implementation, this would make API calls for bulk updates
    console.log('Applying bulk updates:', updates);

    setNotification({
      type: 'success',
      message: `Bulk changes applied to ${updates.length} role(s) successfully.`,
    });

    // Auto-hide notification after 5 seconds
    setTimeout(() => setNotification(null), 5000);
  };

  return (
    <div>
      {/* Header */}
      <div className='mb-8'>
        <div className='flex items-center space-x-4 mb-4'>
          <Button
            variant='outline'
            onClick={() => router.push('/admin')}
            className='flex items-center space-x-2'
          >
            <ArrowLeft className='h-4 w-4' />
            <span>Back to Admin</span>
          </Button>
        </div>

        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold mb-2 text-[var(--custom-gray-dark)]'>
              Role Management
            </h1>
            <p className='text-lg text-[var(--custom-gray-medium)]'>
              Manage roles, subroles, and permission assignments for the
              Childfree Legacy system
            </p>
          </div>

          <div className='flex items-center space-x-2'>
            <Badge variant='outline' className='flex items-center space-x-1'>
              <Shield className='h-3 w-3' />
              <span>RBAC Enabled</span>
            </Badge>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <Alert
          className={`mb-6 ${
            notification.type === 'success'
              ? 'border-green-200 bg-green-50'
              : 'border-red-200 bg-red-50'
          }`}
        >
          {notification.type === 'success' ? (
            <CheckCircle className='h-4 w-4 text-green-600' />
          ) : (
            <AlertTriangle className='h-4 w-4 text-red-600' />
          )}
          <AlertDescription
            className={
              notification.type === 'success'
                ? 'text-green-800'
                : 'text-red-800'
            }
          >
            {notification.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Key Information Cards */}
      <div className='grid md:grid-cols-3 gap-6 mb-8'>
        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='text-lg flex items-center space-x-2'>
              <Shield className='h-5 w-5 text-blue-600' />
              <span>Role-Based Access Control</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-sm text-[var(--custom-gray-medium)]'>
              The system uses hierarchical roles with granular permissions to
              ensure secure access to sensitive estate planning data.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='text-lg flex items-center space-x-2'>
              <Users className='h-5 w-5 text-green-600' />
              <span>Master Roles</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-1 text-sm'>
              <div className='flex justify-between'>
                <span>Administrator</span>
                <Badge variant='outline'>4 subroles</Badge>
              </div>
              <div className='flex justify-between'>
                <span>Welon Trust</span>
                <Badge variant='outline'>4 subroles</Badge>
              </div>
              <div className='flex justify-between'>
                <span>Member</span>
                <Badge variant='outline'>1 subrole</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='text-lg flex items-center space-x-2'>
              <Settings className='h-5 w-5 text-purple-600' />
              <span>Compliance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-sm text-[var(--custom-gray-medium)]'>
              All role changes are logged for HIPAA and SOC2 compliance. Least
              privilege principle is enforced.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Role Management Table */}
      <RoleManagementTable
        onEditRole={handleEditRole}
        onBulkEdit={handleBulkEdit}
      />

      {/* Permission Editor Dialog */}
      <PermissionEditor
        role={selectedRole}
        isOpen={isPermissionEditorOpen}
        onClose={() => {
          setIsPermissionEditorOpen(false);
          setSelectedRole(null);
        }}
        onSave={handleSaveRole}
      />

      {/* Bulk Role Editor Dialog */}
      <BulkRoleEditor
        selectedRoles={selectedRolesForBulk}
        isOpen={isBulkEditorOpen}
        onClose={() => {
          setIsBulkEditorOpen(false);
          setSelectedRolesForBulk([]);
        }}
        onSave={handleSaveBulkChanges}
      />

      {/* Help Section */}
      <Card className='mt-8'>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <AlertTriangle className='h-5 w-5 text-amber-500' />
            <span>Important Notes</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-3 text-sm text-[var(--custom-gray-medium)]'>
            <div className='flex items-start space-x-2'>
              <div className='w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0'></div>
              <p>
                <strong>Permission Conflicts:</strong> The system automatically
                detects and prevents conflicting permission assignments.
              </p>
            </div>
            <div className='flex items-start space-x-2'>
              <div className='w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0'></div>
              <p>
                <strong>Bulk Operations:</strong> Use bulk edit to apply
                permission changes to multiple roles simultaneously.
              </p>
            </div>
            <div className='flex items-start space-x-2'>
              <div className='w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0'></div>
              <p>
                <strong>Audit Trail:</strong> All role and permission changes
                are logged for compliance and security auditing.
              </p>
            </div>
            <div className='flex items-start space-x-2'>
              <div className='w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0'></div>
              <p>
                <strong>User Impact:</strong> Changes to roles affect all users
                assigned to those roles immediately.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
