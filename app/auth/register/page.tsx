'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Eye, EyeOff } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { customSignUp } from '@/app/utils/customSignUp';

// List of all US states
const US_STATES = [
  { code: 'AL', name: 'Alabama' },
  { code: 'AK', name: 'Alaska' },
  { code: 'AZ', name: 'Arizona' },
  { code: 'AR', name: 'Arkansas' },
  { code: 'CA', name: 'California' },
  { code: 'CO', name: 'Colorado' },
  { code: 'CT', name: 'Connecticut' },
  { code: 'DE', name: 'Delaware' },
  { code: 'FL', name: 'Florida' },
  { code: 'GA', name: 'Georgia' },
  { code: 'HI', name: 'Hawaii' },
  { code: 'ID', name: 'Idaho' },
  { code: 'IL', name: 'Illinois' },
  { code: 'IN', name: 'Indiana' },
  { code: 'IA', name: 'Iowa' },
  { code: 'KS', name: 'Kansas' },
  { code: 'KY', name: 'Kentucky' },
  { code: 'LA', name: 'Louisiana' },
  { code: 'ME', name: 'Maine' },
  { code: 'MD', name: 'Maryland' },
  { code: 'MA', name: 'Massachusetts' },
  { code: 'MI', name: 'Michigan' },
  { code: 'MN', name: 'Minnesota' },
  { code: 'MS', name: 'Mississippi' },
  { code: 'MO', name: 'Missouri' },
  { code: 'MT', name: 'Montana' },
  { code: 'NE', name: 'Nebraska' },
  { code: 'NV', name: 'Nevada' },
  { code: 'NH', name: 'New Hampshire' },
  { code: 'NJ', name: 'New Jersey' },
  { code: 'NM', name: 'New Mexico' },
  { code: 'NY', name: 'New York' },
  { code: 'NC', name: 'North Carolina' },
  { code: 'ND', name: 'North Dakota' },
  { code: 'OH', name: 'Ohio' },
  { code: 'OK', name: 'Oklahoma' },
  { code: 'OR', name: 'Oregon' },
  { code: 'PA', name: 'Pennsylvania' },
  { code: 'RI', name: 'Rhode Island' },
  { code: 'SC', name: 'South Carolina' },
  { code: 'SD', name: 'South Dakota' },
  { code: 'TN', name: 'Tennessee' },
  { code: 'TX', name: 'Texas' },
  { code: 'UT', name: 'Utah' },
  { code: 'VT', name: 'Vermont' },
  { code: 'VA', name: 'Virginia' },
  { code: 'WA', name: 'Washington' },
  { code: 'WV', name: 'West Virginia' },
  { code: 'WI', name: 'Wisconsin' },
  { code: 'WY', name: 'Wyoming' },
  { code: 'DC', name: 'District of Columbia' },
];

export default function RegisterPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [state, setState] = useState('');
  const [stateSearchTerm, setStateSearchTerm] = useState('');
  const [filteredStates, setFilteredStates] = useState(US_STATES);
  const [phone, setPhone] = useState('');
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [inviteToken, setInviteToken] = useState<string | null>(null);
  const [verificationSent, setVerificationSent] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');

  // Handle invite data from URL parameters
  useEffect(() => {
    const emailParam = searchParams.get('email');
    const tokenParam = searchParams.get('inviteToken');

    if (emailParam && tokenParam) {
      // This is an invite registration
      setEmail(emailParam);
      setInviteToken(tokenParam);
    }
  }, [searchParams]);
  const [verificationDigits, setVerificationDigits] = useState([
    '',
    '',
    '',
    '',
    '',
    '',
  ]);
  const verificationInputRefs = [
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
    useRef<HTMLInputElement>(null),
  ];
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Filter states based on search term
  useEffect(() => {
    if (stateSearchTerm.trim() === '') {
      setFilteredStates(US_STATES);
    } else {
      const searchTermLower = stateSearchTerm.toLowerCase();
      const filtered = US_STATES.filter(state =>
        state.name.toLowerCase().includes(searchTermLower)
      );
      setFilteredStates(filtered);
    }
  }, [stateSearchTerm]);

  // Handle verification code input
  const handleVerificationDigitChange = (index: number, value: string) => {
    // Only allow numbers
    if (value && !/^\d*$/.test(value)) return;

    // Update the digit at the specified index
    const newDigits = [...verificationDigits];
    newDigits[index] = value.slice(0, 1); // Only take the first character
    setVerificationDigits(newDigits);

    // Update the combined verification code
    setVerificationCode(newDigits.join(''));

    // Auto-focus next input if a digit was entered
    if (value && index < 5) {
      verificationInputRefs[index + 1]?.current?.focus();
    }
  };

  // Handle backspace in verification code inputs
  const handleVerificationKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    // If backspace is pressed and the current field is empty, focus the previous field
    if (e.key === 'Backspace' && !verificationDigits[index] && index > 0) {
      verificationInputRefs[index - 1]?.current?.focus();
    }
  };

  // Password strength indicators
  const hasMinLength = password.length >= 8;
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password);
  const isPasswordStrong =
    hasMinLength && hasUppercase && hasLowercase && hasNumber && hasSpecialChar;
  const passwordsMatch = password === confirmPassword && password !== '';

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Step 1 validation
      if (step === 1) {
        if (!email || !password || !confirmPassword) {
          throw new Error('Please fill in all required fields');
        }

        if (!isPasswordStrong) {
          throw new Error('Password does not meet the strength requirements');
        }

        if (!passwordsMatch) {
          throw new Error('Passwords do not match');
        }

        // Simulate checking if email exists
        await new Promise(resolve => setTimeout(resolve, 1000));

        // For invites, skip step 2 and go directly to account creation
        if (inviteToken) {
          // Create account directly for invited users
          try {
            const result = await customSignUp({
              email,
              password,
              firstName,
              lastName,
              state,
              birthdayDate: new Date(dateOfBirth),
              phone,
              inviteToken: inviteToken || undefined,
            });

            if (!result.success) {
              throw new Error(result.error || 'Failed to create account');
            }

            // Show verification step
            setVerificationSent(true);
            setStep(3);
            console.log('Successfully moved to step 3');
          } catch (innerErr) {
            console.error('Error during account creation:', innerErr);
            throw new Error(
              innerErr instanceof Error
                ? innerErr.message
                : 'Failed to create account'
            );
          }
        } else {
          // Move to step 2 for regular users
          setStep(2);
        }

        setLoading(false);
        return;
      }

      // Step 2 validation
      if (step === 2) {
        console.log('Step 2 submission', {
          firstName,
          lastName,
          dateOfBirth,
          state,
          agreeTerms,
        });

        if (!firstName || !lastName || !dateOfBirth || !state) {
          console.log('Missing required fields');
          throw new Error('Please fill in all required fields');
        }

        // Validate date of birth (must be 18+)
        const birthDate = new Date(dateOfBirth);
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        const dayDiff = today.getDate() - birthDate.getDate();

        // Adjust age if birthday hasn't occurred this year
        if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
          age--;
        }

        console.log('Age validation:', {
          birthDate: dateOfBirth,
          today: today.toISOString().split('T')[0],
          calculatedAge: age,
          monthDiff,
          dayDiff,
        });

        if (age < 18) {
          console.log('Age validation failed', { age });
          throw new Error('You must be at least 18 years old to register');
        }

        if (!agreeTerms) {
          console.log('Terms not agreed');
          throw new Error(
            'You must agree to the Terms of Service and Privacy Policy'
          );
        }

        console.log('All validations passed, proceeding to create account');

        try {
          // Create account using customSignUp
          const result = await customSignUp({
            email,
            password,
            firstName,
            lastName,
            state,
            birthdayDate: new Date(dateOfBirth),
            phone,
            inviteToken: inviteToken || undefined,
          });

          if (!result.success) {
            throw new Error(result.error || 'Failed to create account');
          }

          // Show verification step
          setVerificationSent(true);
          setStep(3);
          console.log('Successfully moved to step 3');
        } catch (innerErr) {
          console.error('Error during account creation:', innerErr);
          throw new Error(
            innerErr instanceof Error
              ? innerErr.message
              : 'Failed to create account'
          );
        } finally {
          setLoading(false);
        }

        return;
      }

      // Step 3 validation (verification)
      if (step === 3) {
        if (!verificationCode || verificationCode.length !== 6) {
          throw new Error('Please enter a valid 6-digit verification code');
        }

        // Simulate verification
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Redirect to onboarding
        router.push('/onboarding');
      }
    } catch (err: any) {
      console.error('Registration error:', err);
      setError(err.message || 'An error occurred during registration');
      setLoading(false);
    }
  };

  return (
    <div className='flex min-h-screen flex-col items-center justify-center bg-warm-gray p-6'>
      <div className='w-full max-w-md space-y-8'>
        <div className='text-center'>
          <h1 className='text-3xl md:text-4xl font-bold text-text-black mb-4'>
            Childfree Legacy
          </h1>
          <h2 className='text-xl md:text-2xl text-text-black'>
            {inviteToken
              ? 'Complete your invitation'
              : step === 1
                ? 'Create your account'
                : step === 2
                  ? 'Complete your profile'
                  : 'Verify your email'}
          </h2>
          {inviteToken && (
            <p className='text-sm text-gray-600 mt-2'>
              You've been invited to join as a Welon Trust member. Please set
              your password to continue.
            </p>
          )}
        </div>

        {error && (
          <div className='bg-error-red/10 border-l-4 border-error-red text-error-red p-6 rounded-xl mb-6 text-lg'>
            {error}
          </div>
        )}

        <div className='bg-background p-8 rounded-xl shadow-md border border-soft-blue'>
          <div className='mb-8'>
            <div className='flex items-center justify-between mb-4'>
              {/* Step 1: Account */}
              <div
                className={`flex items-center ${
                  step === 1
                    ? 'text-primary-green font-bold'
                    : step > 1
                      ? 'text-primary-green'
                      : 'text-[var(--custom-gray-medium)]'
                }`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 text-lg font-bold
                  ${
                    step > 1
                      ? 'bg-primary-green'
                      : step === 1
                        ? 'bg-primary-green border-2 border-white shadow-md'
                        : 'bg-gray-200'
                  }`}
                >
                  {step > 1 ? (
                    // Checkmark for completed step
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-6 w-6 text-white'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                      strokeWidth={3}
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        d='M5 13l4 4L19 7'
                      />
                    </svg>
                  ) : (
                    // Number for current or future step
                    <span className=''>1</span>
                  )}
                </div>
                <span className='text-lg'>Account</span>
              </div>

              {/* Connector line */}
              <div
                className={`h-1 flex-1 mx-4 ${
                  step > 1 ? 'bg-primary-green' : 'bg-gray-200'
                }`}
              ></div>

              {/* Step 2: Profile */}
              <div
                className={`flex items-center ${
                  step === 2
                    ? 'text-primary-green font-bold'
                    : step > 2
                      ? 'text-primary-green'
                      : 'text-[var(--custom-gray-medium)]'
                }`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 text-lg font-bold
                  ${
                    step > 2
                      ? 'bg-primary-green'
                      : step === 2
                        ? 'bg-primary-green border-2 border-white shadow-md'
                        : 'bg-gray-200'
                  }`}
                >
                  {step > 2 ? (
                    // Checkmark for completed step
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-6 w-6 text-white'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                      strokeWidth={3}
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        d='M5 13l4 4L19 7'
                      />
                    </svg>
                  ) : (
                    // Number for current or future step
                    <span className={'text-[var(--custom-gray-medium)]'}>
                      2
                    </span>
                  )}
                </div>
                <span className='text-lg'>Profile</span>
              </div>

              {/* Connector line */}
              <div
                className={`h-1 flex-1 mx-4 ${
                  step > 2 ? 'bg-primary-green' : 'bg-gray-200'
                }`}
              ></div>

              {/* Step 3: Verify */}
              <div
                className={`flex items-center ${
                  step === 3
                    ? 'text-primary-green font-bold'
                    : step > 3
                      ? 'text-primary-green'
                      : 'text-[var(--custom-gray-medium)]'
                }`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 text-lg font-bold
                  ${
                    step > 3
                      ? 'bg-primary-green'
                      : step === 3
                        ? 'bg-primary-green border-2 border-white shadow-md'
                        : 'bg-gray-200'
                  }`}
                >
                  {step > 3 ? (
                    // Checkmark for completed step
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='h-6 w-6 text-white'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                      strokeWidth={3}
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        d='M5 13l4 4L19 7'
                      />
                    </svg>
                  ) : (
                    // Number for current or future step
                    <span
                      className={`${
                        step === 3
                          ? 'text-white'
                          : 'text-[var(--custom-gray-medium)]'
                      }`}
                    >
                      3
                    </span>
                  )}
                </div>
                <span className='text-lg'>Verify</span>
              </div>
            </div>
          </div>

          <form onSubmit={handleRegister} className='space-y-6'>
            {step === 1 && (
              <>
                {inviteToken && (
                  <div className='mb-4 p-3 bg-green-50 border border-green-200 rounded-lg'>
                    <p className='text-sm text-green-800'>
                      <strong>Welcome!</strong> You've been invited to join as a
                      Welon Trust member. Your email is pre-filled. Please
                      create a secure password to continue.
                    </p>
                  </div>
                )}
                <div className='space-y-2'>
                  <Label htmlFor='email' className='flex items-center'>
                    Email address <span className='text-error-red ml-1'>*</span>
                  </Label>
                  <Input
                    id='email'
                    type='email'
                    placeholder='<EMAIL>'
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    required
                    readOnly={!!inviteToken}
                    className={`w-full text-lg ${
                      !email && error
                        ? 'border-error-red focus:border-error-red'
                        : ''
                    } ${inviteToken ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                  />
                  {!email && error && (
                    <p className='text-error-red text-sm mt-1'>
                      Email address is required
                    </p>
                  )}
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='password' className='flex items-center'>
                    Password <span className='text-error-red ml-1'>*</span>
                  </Label>
                  <div className='relative'>
                    <Input
                      id='password'
                      type={showPassword ? 'text' : 'password'}
                      placeholder='••••••••'
                      value={password}
                      onChange={e => setPassword(e.target.value)}
                      required
                      className={`w-full text-lg pr-10 ${
                        !password && error
                          ? 'border-error-red focus:border-error-red'
                          : ''
                      }`}
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      onClick={() => setShowPassword(!showPassword)}
                      className='absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 min-w-0'
                      aria-label={
                        showPassword ? 'Hide password' : 'Show password'
                      }
                    >
                      {showPassword ? (
                        <EyeOff className='h-5 w-5' />
                      ) : (
                        <Eye className='h-5 w-5' />
                      )}
                    </Button>
                  </div>
                  {!password && error && (
                    <p className='text-error-red text-sm mt-1'>
                      Password is required
                    </p>
                  )}

                  <div className='mt-2 bg-blue-gray p-3 rounded-lg'>
                    <p className='font-semibold text-dark-blue text-sm mb-2'>
                      Password requirements:
                    </p>
                    <div className='grid grid-cols-2 gap-x-2 gap-y-1 text-sm'>
                      <div
                        className={`flex items-center ${
                          hasMinLength
                            ? 'text-success-green'
                            : 'text-text-black'
                        }`}
                      >
                        <div
                          className={`w-4 h-4 mr-1 rounded-full flex items-center justify-center ${
                            hasMinLength
                              ? 'bg-success-green text-white'
                              : 'bg-gray-200'
                          }`}
                        >
                          {hasMinLength && '✓'}
                        </div>
                        <span>8+ characters</span>
                      </div>
                      <div
                        className={`flex items-center ${
                          hasUppercase
                            ? 'text-success-green'
                            : 'text-text-black'
                        }`}
                      >
                        <div
                          className={`w-4 h-4 mr-1 rounded-full flex items-center justify-center ${
                            hasUppercase
                              ? 'bg-success-green text-white'
                              : 'bg-gray-200'
                          }`}
                        >
                          {hasUppercase && '✓'}
                        </div>
                        <span>Uppercase (A-Z)</span>
                      </div>
                      <div
                        className={`flex items-center ${
                          hasLowercase
                            ? 'text-success-green'
                            : 'text-text-black'
                        }`}
                      >
                        <div
                          className={`w-4 h-4 mr-1 rounded-full flex items-center justify-center ${
                            hasLowercase
                              ? 'bg-success-green text-white'
                              : 'bg-gray-200'
                          }`}
                        >
                          {hasLowercase && '✓'}
                        </div>
                        <span>Lowercase (a-z)</span>
                      </div>
                      <div
                        className={`flex items-center ${
                          hasNumber ? 'text-success-green' : 'text-text-black'
                        }`}
                      >
                        <div
                          className={`w-4 h-4 mr-1 rounded-full flex items-center justify-center ${
                            hasNumber
                              ? 'bg-success-green text-white'
                              : 'bg-gray-200'
                          }`}
                        >
                          {hasNumber && '✓'}
                        </div>
                        <span>Number (0-9)</span>
                      </div>
                      <div
                        className={`flex items-center ${
                          hasSpecialChar
                            ? 'text-success-green'
                            : 'text-text-black'
                        }`}
                      >
                        <div
                          className={`w-4 h-4 mr-1 rounded-full flex items-center justify-center ${
                            hasSpecialChar
                              ? 'bg-success-green text-white'
                              : 'bg-gray-200'
                          }`}
                        >
                          {hasSpecialChar && '✓'}
                        </div>
                        <span>Special character</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='space-y-2'>
                  <Label
                    htmlFor='confirm-password'
                    className='flex items-center'
                  >
                    Confirm password{' '}
                    <span className='text-error-red ml-1'>*</span>
                  </Label>
                  <div className='relative'>
                    <Input
                      id='confirm-password'
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder='••••••••'
                      value={confirmPassword}
                      onChange={e => setConfirmPassword(e.target.value)}
                      required
                      className={`w-full text-lg pr-10 ${
                        (!confirmPassword || !passwordsMatch) && error
                          ? 'border-error-red focus:border-error-red'
                          : ''
                      }`}
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      className='absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 min-w-0'
                      aria-label={
                        showConfirmPassword ? 'Hide password' : 'Show password'
                      }
                    >
                      {showConfirmPassword ? (
                        <EyeOff className='h-5 w-5' />
                      ) : (
                        <Eye className='h-5 w-5' />
                      )}
                    </Button>
                  </div>
                  {confirmPassword && (
                    <p
                      className={`text-base mt-1 ${
                        passwordsMatch ? 'text-success-green' : 'text-error-red'
                      }`}
                    >
                      {passwordsMatch
                        ? '✓ Passwords match'
                        : '✗ Passwords do not match'}
                    </p>
                  )}
                  {!confirmPassword && error && (
                    <p className='text-error-red text-sm mt-1'>
                      Confirm password is required
                    </p>
                  )}
                </div>
              </>
            )}

            {step === 2 && (
              <>
                {inviteToken && (
                  <div className='mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg'>
                    <p className='text-sm text-blue-800'>
                      <strong>Note:</strong> Your personal information has been
                      pre-filled from the invitation. You can modify any fields
                      if needed.
                    </p>
                  </div>
                )}
                <div className='grid grid-cols-2 gap-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='first-name' className='flex items-center'>
                      First name <span className='text-error-red ml-1'>*</span>
                    </Label>
                    <Input
                      id='first-name'
                      type='text'
                      placeholder='John'
                      value={firstName}
                      onChange={e => setFirstName(e.target.value)}
                      required
                      className={`text-lg ${
                        !firstName && error
                          ? 'border-error-red focus:border-error-red'
                          : ''
                      }`}
                    />
                    {!firstName && error && (
                      <p className='text-error-red text-sm mt-1'>
                        First name is required
                      </p>
                    )}
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='last-name' className='flex items-center'>
                      Last name <span className='text-error-red ml-1'>*</span>
                    </Label>
                    <Input
                      id='last-name'
                      type='text'
                      placeholder='Doe'
                      value={lastName}
                      onChange={e => setLastName(e.target.value)}
                      required
                      className={`text-lg ${
                        !lastName && error
                          ? 'border-error-red focus:border-error-red'
                          : ''
                      }`}
                    />
                    {!lastName && error && (
                      <p className='text-error-red text-sm mt-1'>
                        Last name is required
                      </p>
                    )}
                  </div>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='date-of-birth' className='flex items-center'>
                    Date of birth <span className='text-error-red ml-1'>*</span>
                  </Label>
                  <Input
                    id='date-of-birth'
                    type='date'
                    value={dateOfBirth}
                    onChange={e => setDateOfBirth(e.target.value)}
                    required
                    className={`w-full text-lg ${
                      !dateOfBirth && error
                        ? 'border-error-red focus:border-error-red'
                        : ''
                    }`}
                  />
                  {!dateOfBirth && error ? (
                    <p className='text-error-red text-sm mt-1'>
                      Date of birth is required
                    </p>
                  ) : (
                    <p className='text-sm text-[var(--custom-gray-medium)] mt-1'>
                      You must be at least 18 years old to register
                    </p>
                  )}
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='state' className='flex items-center'>
                    State of residence{' '}
                    <span className='text-error-red ml-1'>*</span>
                  </Label>
                  <div className='relative'>
                    <Label htmlFor='state-select' className='sr-only'>
                      State
                    </Label>
                    <select
                      id='state-select'
                      value={state}
                      onChange={e => {
                        console.log('State selected:', e.target.value);
                        setState(e.target.value);
                      }}
                      className={`w-full p-3 text-lg border rounded-md appearance-none ${
                        !state && error
                          ? 'border-error-red focus:border-error-red'
                          : 'border-input'
                      } focus:outline-none focus:ring-2 focus:ring-dark-blue`}
                    >
                      <option value=''>Select your state</option>
                      {US_STATES.map(stateOption => (
                        <option key={stateOption.code} value={stateOption.code}>
                          {stateOption.name}
                        </option>
                      ))}
                    </select>
                    <div className='absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none'>
                      <svg
                        className='h-5 w-5 text-[var(--custom-gray-medium)]'
                        xmlns='http://www.w3.org/2000/svg'
                        viewBox='0 0 20 20'
                        fill='currentColor'
                      >
                        <path
                          fillRule='evenodd'
                          d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z'
                          clipRule='evenodd'
                        />
                      </svg>
                    </div>
                  </div>
                  {!state && error && (
                    <p className='text-error-red text-sm mt-1'>
                      State is required
                    </p>
                  )}
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='phone'>Phone number (optional)</Label>
                  <Input
                    id='phone'
                    type='tel'
                    placeholder='(*************'
                    value={phone}
                    onChange={e => setPhone(e.target.value)}
                  />
                </div>

                <div className='flex items-start space-x-2 pt-2'>
                  <Checkbox
                    id='terms'
                    checked={agreeTerms}
                    onCheckedChange={checked =>
                      setAgreeTerms(checked as boolean)
                    }
                    className={`mt-1 ${
                      !agreeTerms && error ? 'border-error-red' : ''
                    }`}
                  />
                  <div>
                    <Label htmlFor='terms' className='text-base font-normal'>
                      I agree to the{' '}
                      <Link
                        href='/terms'
                        className='text-blue-600 hover:text-blue-800 hover:underline font-medium'
                      >
                        Terms of Service
                      </Link>{' '}
                      and{' '}
                      <Link
                        href='/privacy'
                        className='text-blue-600 hover:text-blue-800 hover:underline font-medium'
                      >
                        Privacy Policy
                      </Link>
                      <span className='text-error-red ml-1'>*</span>
                    </Label>
                    {!agreeTerms && error && (
                      <p className='text-error-red text-sm mt-1'>
                        You must agree to the terms to continue
                      </p>
                    )}
                  </div>
                </div>
              </>
            )}

            {step === 3 && (
              <div className='space-y-4'>
                <div className='text-center'>
                  <div className='bg-green-2010c/10 text-green-2010c p-3 rounded-full inline-flex mb-4'>
                    <span className='text-xl'>✓</span>
                  </div>
                  <h3 className='text-lg font-medium'>
                    Verification email sent!
                  </h3>
                  <p className='text-sm text-[var(--custom-gray-medium)] mt-2'>
                    We've sent a verification code to <strong>{email}</strong>
                  </p>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='verification-code-0'>Verification Code</Label>
                  <div className='flex justify-between gap-2'>
                    {verificationDigits.map((digit, index) => (
                      <div key={index} className='flex-1'>
                        <Input
                          id={`verification-code-${index}`}
                          ref={verificationInputRefs[index]}
                          type='text'
                          inputMode='numeric'
                          value={digit}
                          onChange={e =>
                            handleVerificationDigitChange(index, e.target.value)
                          }
                          onKeyDown={e => handleVerificationKeyDown(index, e)}
                          onPaste={e => {
                            e.preventDefault();
                            const pastedData = e.clipboardData.getData('text');
                            if (/^\d+$/.test(pastedData)) {
                              const digits = pastedData.slice(0, 6).split('');
                              const newDigits = [...verificationDigits];

                              digits.forEach((digit, i) => {
                                if (i < 6) newDigits[i] = digit;
                              });

                              setVerificationDigits(newDigits);
                              setVerificationCode(newDigits.join(''));

                              // Focus the next empty input or the last one if all filled
                              const nextEmptyIndex = newDigits.findIndex(
                                d => !d
                              );
                              if (nextEmptyIndex !== -1) {
                                verificationInputRefs[
                                  nextEmptyIndex
                                ]?.current?.focus();
                              } else if (digits.length < 6) {
                                verificationInputRefs[
                                  digits.length
                                ]?.current?.focus();
                              } else {
                                verificationInputRefs[5]?.current?.focus();
                              }
                            }
                          }}
                          maxLength={1}
                          className='w-full text-center text-xl font-bold p-3 h-14'
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div className='text-center text-sm'>
                  <p className='text-[var(--custom-gray-medium)]'>
                    Didn't receive the code?{' '}
                    <Button
                      type='button'
                      variant='link'
                      className='h-auto p-0 min-w-0'
                      onClick={() => {
                        // Simulate resending code
                        setLoading(true);
                        setTimeout(() => {
                          setLoading(false);
                        }, 1000);
                      }}
                    >
                      Resend
                    </Button>
                  </p>
                </div>
              </div>
            )}

            <div className='flex justify-between pt-4'>
              {step > 1 && (
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setStep(step - 1)}
                  disabled={loading}
                >
                  Back
                </Button>
              )}

              <Button
                type='submit'
                variant='default'
                className={step === 1 ? 'w-full' : ''}
                disabled={loading}
              >
                {loading
                  ? 'Processing...'
                  : step === 1
                    ? 'Continue'
                    : step === 2
                      ? 'Create Account'
                      : 'Verify & Continue'}
              </Button>
            </div>
          </form>
        </div>

        <div className='text-center mt-4'>
          <p className='text-sm text-[var(--custom-gray-medium)]'>
            Already have an account?{' '}
            <Link
              href='/auth/login'
              className='text-blue-600 hover:text-blue-800 hover:underline font-medium'
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
