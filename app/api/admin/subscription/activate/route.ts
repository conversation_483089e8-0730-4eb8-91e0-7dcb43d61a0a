import { NextRequest, NextResponse } from 'next/server';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';
import Stripe from 'stripe';

Amplify.configure(outputs, { ssr: true });

// Generate the client
const client = generateClient<Schema>();

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      cognitoId,
      plan,
      email,
      currency = 'usd',
      trialDays,
    } = await request.json();

    // Calculate amount based on plan
    const amount = plan === 'BASIC' ? 1000 : 2000; // $10 and $20 in cents

    // Validate required fields
    if (!userId || !cognitoId || !plan || !email) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, cognitoId, plan, email' },
        { status: 400 }
      );
    }

    // Validate plan
    if (!['BASIC', 'PRO'].includes(plan)) {
      return NextResponse.json(
        { error: 'Invalid plan. Must be BASIC or PRO' },
        { status: 400 }
      );
    }

    // Get admin user from request headers (you might want to implement proper admin auth)
    const adminCognitoId = request.headers.get('x-cognito-id');
    if (!adminCognitoId) {
      return NextResponse.json(
        { error: 'Admin authentication required' },
        { status: 401 }
      );
    }

    // Note: We assume user exists since this is called from user edit page

    // Create or get Stripe customer
    let stripeCustomer;
    const existingCustomers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      stripeCustomer = existingCustomers.data[0];
    } else {
      stripeCustomer = await stripe.customers.create({
        email: email,
        metadata: {
          userId: userId,
          cognitoId: cognitoId,
          admin_activated: 'true',
        },
      });
    }

    // Create Stripe product first
    const stripeProduct = await stripe.products.create({
      name: `${plan} Plan`,
      metadata: {
        plan: plan,
      },
    });

    // Calculate trial end date
    const trialEnd = new Date();
    trialEnd.setDate(trialEnd.getDate() + trialDays);

    let stripeSubscription;
    let stripePrice = null;

    if (amount > 0) {
      // For paid subscriptions, create price and subscription with payment
      stripePrice = await stripe.prices.create({
        product: stripeProduct.id,
        unit_amount: amount,
        currency: currency,
        recurring: {
          interval: 'month',
        },
      });

      stripeSubscription = await stripe.subscriptions.create({
        customer: stripeCustomer.id,
        items: [
          {
            price: stripePrice.id,
          },
        ],
        trial_end: Math.floor(trialEnd.getTime() / 1000),
        metadata: {
          userId: userId,
          cognitoId: cognitoId,
          plan: plan,
          admin_activated: 'true',
        },
      });
    } else {
      // For trial-only subscriptions (amount = 0), create subscription without price
      stripeSubscription = await stripe.subscriptions.create({
        customer: stripeCustomer.id,
        items: [
          {
            price_data: {
              currency: currency,
              product: stripeProduct.id,
              recurring: {
                interval: 'month',
              },
              unit_amount: 0, // Free trial
            },
          },
        ],
        trial_end: Math.floor(trialEnd.getTime() / 1000),
        metadata: {
          userId: userId,
          cognitoId: cognitoId,
          plan: plan,
          admin_activated: 'true',
          trial_only: 'true',
        },
      });
    }

    // Cancel any existing active subscriptions for this user
    const { data: existingSubscriptions } =
      await client.models.UserSubscription.list({
        filter: {
          cognitoId: {
            eq: cognitoId,
          },
          status: {
            eq: 'ACTIVE',
          },
        },
        authMode: 'iam',
      });

    // Cancel existing active subscriptions (both in Stripe and database)
    if (existingSubscriptions && existingSubscriptions.length > 0) {
      for (const subscription of existingSubscriptions) {
        // Cancel in Stripe first (if it has a valid Stripe subscription ID)
        if (
          subscription.stripeSubscriptionId &&
          !subscription.stripeSubscriptionId.startsWith('admin-sub-')
        ) {
          try {
            await stripe.subscriptions.cancel(
              subscription.stripeSubscriptionId
            );
            console.log(
              'Canceled Stripe subscription:',
              subscription.stripeSubscriptionId
            );
          } catch (error) {
            console.error(
              'Error canceling Stripe subscription:',
              subscription.stripeSubscriptionId,
              error
            );
            // Continue with database update even if Stripe cancellation fails
          }
        }

        // Update in database
        await client.models.UserSubscription.update(
          {
            id: subscription.id,
            status: 'CANCELED' as const,
            canceledAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            authMode: 'iam',
          }
        );

        console.log('Canceled database subscription:', subscription.id);
      }
    }

    // Calculate subscription period (fallback if Stripe doesn't provide dates)
    const currentPeriodStart = new Date();
    const currentPeriodEnd = new Date();
    currentPeriodEnd.setDate(currentPeriodEnd.getDate() + trialDays); // Use trial period instead of 1 month

    // Create new subscription with Stripe data
    const subscriptionData = {
      userId: userId,
      cognitoId: cognitoId,
      stripeCustomerId: stripeCustomer.id,
      stripeSubscriptionId: stripeSubscription.id,
      plan: plan as 'BASIC' | 'PRO',
      status: 'ACTIVE' as const,
      amount: amount,
      currency: currency,
      currentPeriodStart: (stripeSubscription as any).current_period_start
        ? new Date(
            (stripeSubscription as any).current_period_start * 1000
          ).toISOString()
        : currentPeriodStart.toISOString(),
      currentPeriodEnd: (stripeSubscription as any).trial_end
        ? new Date((stripeSubscription as any).trial_end * 1000).toISOString()
        : (stripeSubscription as any).current_period_end
          ? new Date(
              (stripeSubscription as any).current_period_end * 1000
            ).toISOString()
          : currentPeriodEnd.toISOString(),
      cancelAtPeriodEnd:
        (stripeSubscription as any).cancel_at_period_end || false,
      createdAt: new Date().toISOString(),
    };

    // Create subscription in database
    const { data: newSubscription } =
      await client.models.UserSubscription.create(subscriptionData, {
        authMode: 'iam',
      });

    console.log(
      'Admin-activated subscription created for user:',
      cognitoId,
      'Plan:',
      plan,
      'by admin:',
      adminCognitoId
    );

    return NextResponse.json({
      success: true,
      subscription: newSubscription,
      message: `Successfully activated ${plan} subscription for user`,
    });
  } catch (error) {
    console.error('Error activating admin subscription:', error);
    return NextResponse.json(
      { error: 'Failed to activate subscription' },
      { status: 500 }
    );
  }
}
