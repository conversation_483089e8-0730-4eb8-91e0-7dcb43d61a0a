import { NextRequest, NextResponse } from 'next/server';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';

Amplify.configure(outputs, { ssr: true });

// Generate the client
const client = generateClient<Schema>();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get admin user from request headers
    const adminCognitoId = request.headers.get('x-cognito-id');
    if (!adminCognitoId) {
      return NextResponse.json(
        { error: 'Admin authentication required' },
        { status: 401 }
      );
    }

    // Get user's subscriptions directly by userId
    console.log('Fetching subscriptions for userId:', userId);
    const { data: subscriptions, errors: subscriptionErrors } =
      await client.models.UserSubscription.list({
        filter: {
          userId: {
            eq: userId,
          },
        },
        authMode: 'iam',
      });

    if (subscriptionErrors) {
      console.error('Error fetching subscriptions:', subscriptionErrors);
      return NextResponse.json(
        { error: 'Error fetching subscriptions from database' },
        { status: 500 }
      );
    }

    if (subscriptionErrors) {
      console.error('Error fetching subscriptions:', subscriptionErrors);
    }

    console.log('Found subscriptions:', subscriptions?.length || 0);

    // Find active subscription
    const activeSubscription = subscriptions?.find(
      sub => sub.status === 'ACTIVE'
    );

    return NextResponse.json({
      subscription: activeSubscription || null,
      allSubscriptions: subscriptions || [],
    });
  } catch (error) {
    console.error('Error fetching user subscription:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user subscription' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    const { userId } = await params;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get admin user from request headers
    const adminCognitoId = request.headers.get('x-cognito-id');
    if (!adminCognitoId) {
      return NextResponse.json(
        { error: 'Admin authentication required' },
        { status: 401 }
      );
    }

    // Get user by ID
    console.log('DELETE: Fetching user with ID:', userId);
    const { data: user, errors: userErrors } = await client.models.User.get(
      { id: userId },
      {
        authMode: 'iam',
      }
    );

    if (userErrors) {
      console.error('DELETE: Error fetching user:', userErrors);
      return NextResponse.json(
        { error: 'Error fetching user from database' },
        { status: 500 }
      );
    }

    if (!user) {
      console.log('DELETE: User not found with ID:', userId);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Cancel all active subscriptions for this user
    const { data: activeSubscriptions } =
      await client.models.UserSubscription.list({
        filter: {
          cognitoId: {
            eq: user.cognitoId,
          },
          status: {
            eq: 'ACTIVE',
          },
        },
        authMode: 'iam',
      });

    if (activeSubscriptions && activeSubscriptions.length > 0) {
      for (const subscription of activeSubscriptions) {
        await client.models.UserSubscription.update(
          {
            id: subscription.id,
            status: 'CANCELED' as const,
            canceledAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          {
            authMode: 'iam',
          }
        );
      }
    }

    console.log(
      'Admin canceled subscriptions for user:',
      user.cognitoId,
      'by admin:',
      adminCognitoId
    );

    return NextResponse.json({
      success: true,
      message: 'Successfully canceled user subscriptions',
    });
  } catch (error) {
    console.error('Error canceling user subscription:', error);
    return NextResponse.json(
      { error: 'Failed to cancel user subscription' },
      { status: 500 }
    );
  }
}
