import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function GET(request: NextRequest) {
  try {
    console.log('Admin Transactions API: Request received');

    // Get payment intents from Stripe (these represent transactions)
    const paymentIntents = await stripe.paymentIntents.list({
      limit: 200,
    });

    console.log('Found payment intents:', paymentIntents.data.length);

    // Transform payment intents to match admin page format
    const transformedTransactions = paymentIntents.data.map(intent => ({
      id: intent.id,
      amount: intent.amount / 100, // Convert from cents to dollars
      status: mapPaymentStatus(intent.status),
      date: new Date(intent.created * 1000).toISOString(),
      description:
        intent.description || `Payment for ${intent.amount / 100} USD`,
      currency: intent.currency.toUpperCase(),
    }));

    return NextResponse.json({
      transactions: transformedTransactions,
    });
  } catch (error) {
    console.error('Admin Transactions API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to map Stripe payment status to admin page status
function mapPaymentStatus(stripeStatus: string): string {
  switch (stripeStatus) {
    case 'succeeded':
      return 'Successful';
    case 'failed':
      return 'Failed';
    case 'canceled':
      return 'Canceled';
    case 'processing':
      return 'Processing';
    case 'requires_payment_method':
    case 'requires_confirmation':
    case 'requires_action':
      return 'Pending';
    default:
      return 'Unknown';
  }
}
