import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '../../../../amplify/data/resource';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not defined in environment variables');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

Amplify.configure(outputs, { ssr: true });

// Generate the client with IAM auth mode for guest access
const client = generateClient<Schema>({
  authMode: 'iam',
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'No signature provided' },
        { status: 400 }
      );
    }

    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(
        body as string,
        signature as string,
        process.env.STRIPE_WEBHOOK_SECRET! as string
      );
      console.log('Webhook: Event constructed successfully');
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    console.log('Processing Stripe event:', event.type);

    // Handle different event types
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(
          event.data.object as Stripe.Checkout.Session
        );
        break;
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(
          event.data.object as Stripe.Subscription
        );
        break;
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(
          event.data.object as Stripe.Subscription
        );
        break;
      default:
        console.log('Unhandled event type:', event.type);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to safely convert timestamp to ISO string
const safeTimestampToISO = (timestamp: number | null | undefined): string => {
  if (timestamp === null || timestamp === undefined || isNaN(timestamp)) {
    return new Date().toISOString();
  }
  return new Date(timestamp * 1000).toISOString();
};

async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  console.log('=== CHECKOUT COMPLETED PROCESSING START ===');
  console.log('Processing checkout completed:', session.id);
  console.log('Session metadata:', session.metadata);

  if (
    !session.subscription ||
    !session.metadata?.cognitoId ||
    !session.metadata?.userId
  ) {
    console.log(
      'Missing subscription, cognitoId, or userId in session metadata',
      {
        hasSubscription: !!session.subscription,
        hasCognitoId: !!session.metadata?.cognitoId,
        hasUserId: !!session.metadata?.userId,
        metadata: session.metadata,
      }
    );
    return;
  }

  const cognitoId = session.metadata.cognitoId;
  const userId = session.metadata.userId;
  const plan = session.metadata.plan;
  const amount = plan === 'BASIC' ? 1000 : 2000;

  console.log(
    `Processing subscription for user: ${cognitoId}, plan: ${plan}, amount: ${amount}`
  );

  try {
    // Get subscription details from Stripe
    const subscriptionResponse = await stripe.subscriptions.retrieve(
      session.subscription as string
    );
    const subscription = subscriptionResponse as Stripe.Subscription;

    // Cancel any existing active subscriptions before creating the new one
    console.log(
      'Canceling existing active subscriptions before creating new subscription'
    );
    await cancelExistingActiveSubscriptions(cognitoId);

    const subscriptionData = {
      userId: userId,
      cognitoId: cognitoId,
      stripeCustomerId: session.customer as string,
      stripeSubscriptionId: subscription.id,
      plan: plan as 'BASIC' | 'PRO',
      status: 'ACTIVE' as const,
      amount: amount,
      currency: 'usd',
      currentPeriodStart: safeTimestampToISO(
        (subscription as any).current_period_start
      ),
      currentPeriodEnd: safeTimestampToISO(
        (subscription as any).current_period_end
      ),
      cancelAtPeriodEnd: (subscription as any).cancel_at_period_end || false,
      createdAt: new Date().toISOString(),
    };

    // Create subscription in database
    await client.models.UserSubscription.create(subscriptionData);
    console.log(
      'New subscription created in database for user:',
      cognitoId,
      'Plan:',
      plan
    );
    console.log('=== CHECKOUT COMPLETED PROCESSING END ===');
  } catch (error) {
    console.error('Error creating subscription in database:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    console.log('=== CHECKOUT COMPLETED PROCESSING FAILED ===');
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  if (!(invoice as any).subscription) return;

  const subscriptionResponse = await stripe.subscriptions.retrieve(
    (invoice as any).subscription as string
  );
  const subscription = subscriptionResponse as Stripe.Subscription;
  const cognitoId = subscription.metadata?.cognitoId;

  if (!cognitoId) return;

  console.log('Payment succeeded for user:', cognitoId);

  try {
    // Find and update subscription in database
    const { data: subscriptions } = await client.models.UserSubscription.list({
      filter: {
        stripeSubscriptionId: {
          eq: subscription.id,
        },
      },
    });

    if (subscriptions && subscriptions.length > 0) {
      const userSubscription = subscriptions[0];
      await client.models.UserSubscription.update({
        id: userSubscription.id,
        status: 'ACTIVE' as const,
        currentPeriodStart: safeTimestampToISO(
          (subscription as any).current_period_start
        ),
        currentPeriodEnd: safeTimestampToISO(
          (subscription as any).current_period_end
        ),
        updatedAt: new Date().toISOString(),
      });
      console.log('Subscription updated in database for user:', cognitoId);
    }
  } catch (error) {
    console.error('Error updating subscription in database:', error);
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  const cognitoId = subscription.metadata?.cognitoId;
  if (!cognitoId) return;

  let status:
    | 'ACTIVE'
    | 'INACTIVE'
    | 'PAST_DUE'
    | 'CANCELED'
    | 'INCOMPLETE'
    | 'TRIALING' = 'ACTIVE';
  if (subscription.status === 'past_due') status = 'PAST_DUE';
  else if (subscription.status === 'canceled') status = 'CANCELED';
  else if (
    subscription.status === 'incomplete' ||
    subscription.status === 'incomplete_expired'
  )
    status = 'INCOMPLETE';
  else if (subscription.status === 'trialing') status = 'TRIALING';

  console.log('Subscription updated for user:', cognitoId, 'Status:', status);

  try {
    // Find and update subscription in database
    const { data: subscriptions } = await client.models.UserSubscription.list({
      filter: {
        stripeSubscriptionId: {
          eq: subscription.id,
        },
      },
    });

    if (subscriptions && subscriptions.length > 0) {
      const userSubscription = subscriptions[0];
      const updateData: any = {
        id: userSubscription.id,
        status: status,
        currentPeriodStart: safeTimestampToISO(
          (subscription as any).current_period_start
        ),
        currentPeriodEnd: safeTimestampToISO(
          (subscription as any).current_period_end
        ),
        cancelAtPeriodEnd: (subscription as any).cancel_at_period_end || false,
        updatedAt: new Date().toISOString(),
      };

      if ((subscription as any).canceled_at) {
        updateData.canceledAt = safeTimestampToISO(
          (subscription as any).canceled_at
        );
      }

      await client.models.UserSubscription.update(updateData);
      console.log('Subscription updated in database for user:', cognitoId);
    }
  } catch (error) {
    console.error('Error updating subscription in database:', error);
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const cognitoId = subscription.metadata?.cognitoId;
  if (!cognitoId) return;

  console.log('Subscription deleted for user:', cognitoId);

  try {
    // Find and update subscription status in database
    const { data: subscriptions } = await client.models.UserSubscription.list({
      filter: {
        stripeSubscriptionId: {
          eq: subscription.id,
        },
      },
    });

    if (subscriptions && subscriptions.length > 0) {
      const userSubscription = subscriptions[0];
      await client.models.UserSubscription.update({
        id: userSubscription.id,
        status: 'CANCELED' as const,
        canceledAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
      console.log(
        'Subscription marked as canceled in database for user:',
        cognitoId
      );
    }
  } catch (error) {
    console.error('Error updating subscription in database:', error);
  }
}

// Helper function to cancel existing active subscriptions for a user
async function cancelExistingActiveSubscriptions(cognitoId: string) {
  console.log(
    'Checking for existing active subscriptions for user:',
    cognitoId
  );

  try {
    // Find all active subscriptions for the user
    const { data: existingSubscriptions } =
      await client.models.UserSubscription.list({
        filter: {
          cognitoId: {
            eq: cognitoId,
          },
          status: {
            eq: 'ACTIVE',
          },
        },
      });

    if (!existingSubscriptions || existingSubscriptions.length === 0) {
      console.log(
        'No existing active subscriptions found for user:',
        cognitoId
      );
      return;
    }

    console.log(
      `Found ${existingSubscriptions.length} active subscription(s) to cancel for user:`,
      cognitoId
    );

    // Cancel each existing subscription
    for (const subscription of existingSubscriptions) {
      try {
        console.log(
          `Canceling subscription ${subscription.stripeSubscriptionId} for user:`,
          cognitoId
        );

        // Cancel in Stripe
        await stripe.subscriptions.cancel(subscription.stripeSubscriptionId);
        console.log(
          `Successfully canceled Stripe subscription: ${subscription.stripeSubscriptionId}`
        );

        // Update status in database
        await client.models.UserSubscription.update({
          id: subscription.id,
          status: 'CANCELED' as const,
          canceledAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
        console.log(
          `Successfully updated database status for subscription: ${subscription.id}`
        );
      } catch (error) {
        console.error(
          `Error canceling subscription ${subscription.stripeSubscriptionId}:`,
          error
        );
        // Continue with other subscriptions even if one fails
      }
    }

    console.log(
      'Finished canceling existing subscriptions for user:',
      cognitoId
    );
  } catch (error) {
    console.error('Error finding existing subscriptions:', error);
    throw error; // Re-throw to handle in calling function
  }
}
