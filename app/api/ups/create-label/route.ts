import { NextRequest, NextResponse } from 'next/server';
import { upsClient } from '@/lib/ups/client';
import {
  createShipmentRequest,
  parseShipmentResponse,
  validateAddress,
} from '@/lib/ups/utils';
import { UPSAddress } from '@/app/types/ups';
import { UPS_SERVICE_CODES, getEnvironmentInfo } from '@/lib/ups/config';

interface CreateLabelRequest {
  fromAddress: UPSAddress & { name: string; phone?: string };
  toAddress: UPSAddress & { name: string; phone?: string };
  serviceCode?: string;
  packageOptions?: {
    description?: string;
    weight?: string;
    dimensions?: {
      length?: string;
      width?: string;
      height?: string;
    };
  };
}

export async function POST(request: NextRequest) {
  const requestId = Date.now().toString();

  try {
    console.log(`🚀 [${requestId}] UPS Create Label API called`);

    const body: CreateLabelRequest = await request.json();

    console.log(`📋 [${requestId}] Request payload:`, {
      hasFromAddress: !!body.fromAddress,
      hasToAddress: !!body.toAddress,
      serviceCode: body.serviceCode,
      packageOptions: body.packageOptions,
      fromAddress: body.fromAddress
        ? {
            name: body.fromAddress.name,
            city: body.fromAddress.city,
            stateProvinceCode: body.fromAddress.stateProvinceCode,
            postalCode: body.fromAddress.postalCode,
            countryCode: body.fromAddress.countryCode,
          }
        : null,
      toAddress: body.toAddress
        ? {
            name: body.toAddress.name,
            city: body.toAddress.city,
            stateProvinceCode: body.toAddress.stateProvinceCode,
            postalCode: body.toAddress.postalCode,
            countryCode: body.toAddress.countryCode,
          }
        : null,
    });

    // Validate required fields
    if (!body.fromAddress || !body.toAddress) {
      console.log(`❌ [${requestId}] Validation failed: Missing addresses`);
      return NextResponse.json(
        { error: 'From address and to address are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    console.log(`🔍 [${requestId}] Validating addresses...`);

    const fromValidation = validateAddress(body.fromAddress);
    if (!fromValidation.isValid) {
      console.log(
        `❌ [${requestId}] From address validation failed:`,
        fromValidation.errors
      );
      return NextResponse.json(
        {
          error: 'Invalid from address',
          details: fromValidation.errors,
        },
        { status: 400 }
      );
    }

    const toValidation = validateAddress(body.toAddress);
    if (!toValidation.isValid) {
      console.log(
        `❌ [${requestId}] To address validation failed:`,
        toValidation.errors
      );
      return NextResponse.json(
        {
          error: 'Invalid to address',
          details: toValidation.errors,
        },
        { status: 400 }
      );
    }

    console.log(`✅ [${requestId}] Address validation passed`);

    // Create shipment request
    const serviceCode = body.serviceCode || UPS_SERVICE_CODES.GROUND;
    const packageOptions = body.packageOptions
      ? {
          description: body.packageOptions.description || 'Legal Documents',
          packageWeight: body.packageOptions.weight
            ? {
                unitOfMeasurement: { code: 'LBS', description: 'Pounds' },
                weight: body.packageOptions.weight,
              }
            : undefined,
          dimensions: body.packageOptions.dimensions
            ? {
                unitOfMeasurement: { code: 'IN', description: 'Inches' },
                length: body.packageOptions.dimensions.length || '12',
                width: body.packageOptions.dimensions.width || '9',
                height: body.packageOptions.dimensions.height || '3',
              }
            : undefined,
        }
      : undefined;

    console.log(`📦 [${requestId}] Creating shipment request:`, {
      serviceCode,
      packageOptions: packageOptions
        ? {
            description: packageOptions.description,
            weight: packageOptions.packageWeight?.weight,
            dimensions: packageOptions.dimensions
              ? `${packageOptions.dimensions.length}x${packageOptions.dimensions.width}x${packageOptions.dimensions.height}`
              : 'default',
          }
        : 'default',
    });

    const shipmentRequest = createShipmentRequest(
      body.fromAddress,
      body.toAddress,
      serviceCode,
      packageOptions
    );

    console.log(`🚚 [${requestId}] Calling UPS API to create shipment...`);

    // Create shipment with UPS
    const upsResponse = await upsClient.createShipment(shipmentRequest);

    console.log(`📥 [${requestId}] UPS API response received, parsing...`);

    // Parse response to simplified format
    const labelResult = parseShipmentResponse(upsResponse);

    // Log successful shipment creation
    const envInfo = getEnvironmentInfo();
    console.log(`✅ [${requestId}] UPS Label created successfully:`, {
      trackingNumber: labelResult.trackingNumber,
      cost: labelResult.cost,
      environment: envInfo.environment,
      estimatedDelivery: labelResult.estimatedDelivery,
      labelUrlLength: labelResult.labelUrl?.length || 0,
    });

    console.log(`🎉 [${requestId}] Returning successful response`);

    return NextResponse.json({
      success: true,
      data: labelResult,
      environment: envInfo,
    });
  } catch (error) {
    console.error(`❌ [${requestId}] Create UPS label error:`, {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
    });

    // Handle specific UPS API errors
    if (error instanceof Error) {
      if (error.message.includes('Authentication')) {
        console.log(`🔐 [${requestId}] Authentication error detected`);
        return NextResponse.json(
          { error: 'UPS API authentication failed' },
          { status: 401 }
        );
      }

      if (error.message.includes('Address')) {
        console.log(`📍 [${requestId}] Address validation error detected`);
        return NextResponse.json(
          { error: 'Invalid address information', details: error.message },
          { status: 400 }
        );
      }
    }

    console.log(`💥 [${requestId}] Returning generic error response`);

    return NextResponse.json(
      {
        error: 'Failed to create shipping label',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve available service options
export async function GET() {
  try {
    const serviceOptions = [
      {
        code: UPS_SERVICE_CODES.GROUND,
        name: 'UPS Ground',
        description: 'Standard ground delivery (1-5 business days)',
        estimatedDays: 5,
      },
      {
        code: UPS_SERVICE_CODES.THREE_DAY_SELECT,
        name: 'UPS 3 Day Select',
        description: 'Guaranteed 3 business day delivery',
        estimatedDays: 3,
      },
      {
        code: UPS_SERVICE_CODES.SECOND_DAY_AIR,
        name: 'UPS 2nd Day Air',
        description: 'Guaranteed 2 business day delivery',
        estimatedDays: 2,
      },
      {
        code: UPS_SERVICE_CODES.NEXT_DAY_AIR_SAVER,
        name: 'UPS Next Day Air Saver',
        description: 'Next business day delivery by end of day',
        estimatedDays: 1,
      },
      {
        code: UPS_SERVICE_CODES.NEXT_DAY_AIR,
        name: 'UPS Next Day Air',
        description: 'Next business day delivery by 10:30 AM',
        estimatedDays: 1,
      },
    ];

    return NextResponse.json({
      success: true,
      data: serviceOptions,
    });
  } catch (error) {
    console.error('Get service options error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve service options' },
      { status: 500 }
    );
  }
}
