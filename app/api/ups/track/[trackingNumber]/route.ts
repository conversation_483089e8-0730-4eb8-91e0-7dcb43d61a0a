import { NextRequest, NextResponse } from 'next/server';
import { upsClient } from '@/lib/ups/client';
import { parseTrackingResponse } from '@/lib/ups/utils';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ trackingNumber: string }> }
) {
  const requestId = Date.now().toString();

  try {
    const { trackingNumber } = await params;

    console.log(`🔍 [${requestId}] UPS Track Package API called:`, {
      originalTrackingNumber: trackingNumber,
    });

    // Validate tracking number format
    if (!trackingNumber || trackingNumber.length < 10) {
      console.log(`❌ [${requestId}] Invalid tracking number format:`, {
        trackingNumber,
        length: trackingNumber?.length,
      });
      return NextResponse.json(
        { error: 'Invalid tracking number format' },
        { status: 400 }
      );
    }

    // Clean tracking number (remove spaces and special characters)
    const cleanTrackingNumber = trackingNumber
      .replace(/[^A-Z0-9]/gi, '')
      .toUpperCase();

    console.log(`🧹 [${requestId}] Cleaned tracking number:`, {
      original: trackingNumber,
      cleaned: cleanTrackingNumber,
    });

    console.log(`🚚 [${requestId}] Calling UPS tracking API...`);

    // Track package with UPS
    const upsResponse = await upsClient.trackPackage(cleanTrackingNumber);

    console.log(`📥 [${requestId}] UPS tracking response received, parsing...`);

    // Parse response to simplified format
    const trackingStatus = parseTrackingResponse(upsResponse);

    // Log successful tracking request
    console.log(`✅ [${requestId}] UPS Tracking retrieved successfully:`, {
      trackingNumber: cleanTrackingNumber,
      status: trackingStatus.status,
      statusDescription: trackingStatus.statusDescription,
      lastUpdate: trackingStatus.lastUpdate,
      currentLocation: trackingStatus.currentLocation,
      activitiesCount: trackingStatus.activities?.length || 0,
    });

    console.log(`🎉 [${requestId}] Returning successful tracking response`);

    return NextResponse.json({
      success: true,
      data: trackingStatus,
    });
  } catch (error) {
    console.error(`❌ [${requestId}] Track UPS package error:`, {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
    });

    // Handle specific UPS API errors
    if (error instanceof Error) {
      if (error.message.includes('Authentication')) {
        console.log(`🔐 [${requestId}] Authentication error detected`);
        return NextResponse.json(
          { error: 'UPS API authentication failed' },
          { status: 401 }
        );
      }

      if (
        error.message.includes('not found') ||
        error.message.includes('120001')
      ) {
        console.log(`📦 [${requestId}] Tracking number not found`);
        return NextResponse.json(
          { error: 'Tracking number not found' },
          { status: 404 }
        );
      }

      if (error.message.includes('Invalid tracking number')) {
        console.log(`🔢 [${requestId}] Invalid tracking number format`);
        return NextResponse.json(
          { error: 'Invalid tracking number format' },
          { status: 400 }
        );
      }
    }

    console.log(`💥 [${requestId}] Returning generic tracking error response`);

    return NextResponse.json(
      {
        error: 'Failed to track package',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      },
      { status: 500 }
    );
  }
}
