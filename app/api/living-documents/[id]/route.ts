import { NextRequest, NextResponse } from 'next/server';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

// Generate the client
const client = generateClient<Schema>();

// GET to fetch a specific living document
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id } = await params;

    const { data, errors } = await client.models.LivingDocument.get({
      id: id,
    });

    if (errors) {
      return NextResponse.json({ error: errors }, { status: 400 });
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (data.userId !== user.userId) {
      return NextResponse.json(
        { error: 'Unauthorized access to document' },
        { status: 403 }
      );
    }

    return NextResponse.json({ document: data });
  } catch (error) {
    console.error('Error fetching living document:', error);
    return NextResponse.json(
      { error: 'Failed to fetch living document' },
      { status: 500 }
    );
  }
}

// PUT to update a specific living document
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id } = await params;
    const body = await request.json();

    // First, get the existing document to verify ownership and get current version
    const { data: existingDoc, errors: fetchErrors } =
      await client.models.LivingDocument.get({
        id: id,
      });

    if (fetchErrors) {
      return NextResponse.json({ error: fetchErrors }, { status: 400 });
    }

    if (!existingDoc) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (existingDoc.userId !== user.userId) {
      return NextResponse.json(
        { error: 'Unauthorized access to document' },
        { status: 403 }
      );
    }

    // Calculate next review date if frequency changed
    const calculateNextReviewDate = (frequency: string): Date => {
      const now = new Date();
      switch (frequency) {
        case 'Monthly':
          return new Date(now.setMonth(now.getMonth() + 1));
        case 'Quarterly':
          return new Date(now.setMonth(now.getMonth() + 3));
        case 'SemiAnnually':
          return new Date(now.setMonth(now.getMonth() + 6));
        case 'Annually':
          return new Date(now.setFullYear(now.getFullYear() + 1));
        default:
          return new Date(now.setMonth(now.getMonth() + 6));
      }
    };

    const newVersion = (existingDoc.version || 1) + 1;
    const now = new Date().toISOString();

    // Prepare update data
    const updateData: any = {
      id: id,
      version: newVersion,
      lastReviewDate: now,
      updatedAt: now,
    };

    // Only update fields that are provided
    if (body.title !== undefined) updateData.title = body.title;
    if (body.content !== undefined)
      updateData.content = JSON.stringify(body.content);
    if (body.status !== undefined) updateData.status = body.status;
    if (body.documentType !== undefined)
      updateData.documentType = body.documentType;
    if (body.reminderFrequency !== undefined) {
      updateData.reminderFrequency = body.reminderFrequency;
      updateData.nextReviewDate = calculateNextReviewDate(
        body.reminderFrequency
      ).toISOString();
    }
    if (body.isTemplate !== undefined) updateData.isTemplate = body.isTemplate;
    if (body.templateId !== undefined) updateData.templateId = body.templateId;

    // Update the document
    const { data, errors } =
      await client.models.LivingDocument.update(updateData);

    if (errors) {
      return NextResponse.json({ error: errors }, { status: 400 });
    }

    // Log the update
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: id,
        userId: user.userId,
        changeType: 'Updated',
        changeDescription: `Document "${body.title || existingDoc.title}" updated`,
        previousVersion: existingDoc.version || 1,
        newVersion: newVersion,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document update:', logError);
      // Continue even if logging fails
    }

    return NextResponse.json({ document: data });
  } catch (error) {
    console.error('Error updating living document:', error);
    return NextResponse.json(
      { error: 'Failed to update living document' },
      { status: 500 }
    );
  }
}

// DELETE to archive a specific living document
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await getCurrentUser();
    const { id } = await params;

    // First, get the existing document to verify ownership
    const { data: existingDoc, errors: fetchErrors } =
      await client.models.LivingDocument.get({
        id: id,
      });

    if (fetchErrors) {
      return NextResponse.json({ error: fetchErrors }, { status: 400 });
    }

    if (!existingDoc) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Verify ownership
    if (existingDoc.userId !== user.userId) {
      return NextResponse.json(
        { error: 'Unauthorized access to document' },
        { status: 403 }
      );
    }

    const now = new Date().toISOString();

    // Archive the document instead of deleting it
    const { data, errors } = await client.models.LivingDocument.update({
      id: id,
      status: 'Archived',
      updatedAt: now,
    });

    if (errors) {
      return NextResponse.json({ error: errors }, { status: 400 });
    }

    // Log the archival
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: id,
        userId: user.userId,
        changeType: 'Archived',
        changeDescription: `Document "${existingDoc.title}" archived`,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document archival:', logError);
      // Continue even if logging fails
    }

    return NextResponse.json({
      message: 'Document archived successfully',
      document: data,
    });
  } catch (error) {
    console.error('Error archiving living document:', error);
    return NextResponse.json(
      { error: 'Failed to archive living document' },
      { status: 500 }
    );
  }
}
