import { NextRequest, NextResponse } from 'next/server';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

// Generate the client
const client = generateClient<Schema>();

// GET to fetch all living documents for the current user
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const documentType = searchParams.get('type');

    let filter: any = {
      userId: { eq: user.userId },
    };

    // Add status filter if provided
    if (status && status !== 'all') {
      filter.status = { eq: status };
    } else {
      // By default, exclude archived documents
      filter.status = { ne: 'Archived' };
    }

    // Add document type filter if provided
    if (documentType && documentType !== 'all') {
      filter.documentType = { eq: documentType };
    }

    const { data, errors } = await client.models.LivingDocument.list({
      filter,
    });

    if (errors) {
      return NextResponse.json({ error: errors }, { status: 400 });
    }

    return NextResponse.json({ documents: data });
  } catch (error) {
    console.error('Error fetching living documents:', error);
    return NextResponse.json(
      { error: 'Failed to fetch living documents' },
      { status: 500 }
    );
  }
}

// POST to create a new living document
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.documentType || !body.content) {
      return NextResponse.json(
        { error: 'Title, document type, and content are required' },
        { status: 400 }
      );
    }

    // Calculate next review date based on frequency
    const calculateNextReviewDate = (frequency: string): Date => {
      const now = new Date();
      switch (frequency) {
        case 'Monthly':
          return new Date(now.setMonth(now.getMonth() + 1));
        case 'Quarterly':
          return new Date(now.setMonth(now.getMonth() + 3));
        case 'SemiAnnually':
          return new Date(now.setMonth(now.getMonth() + 6));
        case 'Annually':
          return new Date(now.setFullYear(now.getFullYear() + 1));
        default:
          return new Date(now.setMonth(now.getMonth() + 6));
      }
    };

    const nextReviewDate = calculateNextReviewDate(
      body.reminderFrequency || 'SemiAnnually'
    );
    const now = new Date().toISOString();

    // Create the living document
    const { data, errors } = await client.models.LivingDocument.create({
      userId: user.userId,
      documentType: body.documentType,
      title: body.title,
      content: JSON.stringify(body.content), // Ensure content is stored as JSON string
      version: 1,
      status: body.status || 'Active',
      lastReviewDate: now,
      nextReviewDate: nextReviewDate.toISOString(),
      reminderFrequency: body.reminderFrequency || 'SemiAnnually',
      isTemplate: body.isTemplate || false,
      templateId: body.templateId || null,
      createdAt: now,
      updatedAt: now,
    });

    if (errors) {
      return NextResponse.json({ error: errors }, { status: 400 });
    }

    // Log the creation
    try {
      await client.models.DocumentUpdateLog.create({
        documentId: data!.id!,
        userId: user.userId,
        changeType: 'Created',
        changeDescription: `Document "${body.title}" created`,
        previousVersion: 0,
        newVersion: 1,
        timestamp: now,
      });
    } catch (logError) {
      console.error('Error logging document creation:', logError);
      // Continue even if logging fails
    }

    return NextResponse.json({ document: data }, { status: 201 });
  } catch (error) {
    console.error('Error creating living document:', error);
    return NextResponse.json(
      { error: 'Failed to create living document' },
      { status: 500 }
    );
  }
}
