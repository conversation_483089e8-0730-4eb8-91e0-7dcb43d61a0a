// Living Document Builder Types

export type LivingDocumentQuestionType = 'text' | 'select' | 'radio' | 'file';

export interface LivingDocumentQuestionOption {
  id: string;
  label: string;
  value: string;
}

export interface LivingDocumentQuestion {
  id: string;
  text: string;
  type: LivingDocumentQuestionType;
  required: boolean;
  placeholder?: string;
  helpText?: string;
  options?: LivingDocumentQuestionOption[];
  allowMultiple: boolean; // Allow multiple answers (+ button functionality)
  order: number;
}

export interface LivingDocumentTemplate {
  id: string;
  createdByEmail: string;
  documentType: string;
  title: string;
  description?: string;
  content?: string;
  questions?: LivingDocumentQuestion[];
  version: number;
  status: 'Draft' | 'Active' | 'Archived';
  icon?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Form data types for creating/updating questions
export interface CreateLivingDocumentQuestionRequest {
  text: string;
  type: LivingDocumentQuestionType;
  required: boolean;
  placeholder?: string;
  helpText?: string;
  options?: Omit<LivingDocumentQuestionOption, 'id'>[];
  allowMultiple: boolean;
  order: number;
}

export interface UpdateLivingDocumentQuestionRequest
  extends Partial<CreateLivingDocumentQuestionRequest> {
  id: string;
}

// Answer types for when users fill out the template
export interface LivingDocumentAnswer {
  questionId: string;
  values: string[]; // Array to support multiple answers
}

export interface LivingDocumentResponse {
  templateId: string;
  answers: LivingDocumentAnswer[];
}

// Validation rules
export interface LivingDocumentValidationRule {
  type: 'required' | 'minLength' | 'maxLength' | 'pattern';
  value?: string | number;
  message: string;
}
