'use client';

import {
  Logo,
  Headline,
  Subhead,
  SubheadBold,
  BodyCopy,
  Callout,
  CoreColorPalette,
  SecondaryColorPalette,
} from '../../components/ui/brand';

export default function BrandGuidePage() {
  return (
    <div className='container mx-auto py-12 px-4'>
      <Headline className='mb-8'>Childfree Legacy Brand Guide</Headline>

      <section className='mb-12'>
        <Subhead className='mb-6'>Logo</Subhead>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
          <div className='p-8 bg-black-6c rounded-lg flex items-center justify-center'>
            <Logo variant='large' />
          </div>
          <div className='p-8 bg-gray-6197c rounded-lg flex items-center justify-center'>
            <Logo variant='large' />
          </div>
        </div>
      </section>

      <section className='mb-12'>
        <Subhead className='mb-6'>Typography</Subhead>
        <div className='space-y-6'>
          <div>
            <SubheadBold className='mb-2'>Geologica</SubheadBold>
            <BodyCopy className='mb-4'>
              Primary font used for headlines and subheads.
            </BodyCopy>
            <div className='space-y-2'>
              <div className='p-4 bg-gray-100 rounded-md'>
                <Headline>Geologica Semi Bold (600)</Headline>
              </div>
              <div className='p-4 bg-gray-100 rounded-md'>
                <Subhead>Geologica Light (300)</Subhead>
              </div>
            </div>
          </div>

          <div>
            <SubheadBold className='mb-2'>Source Sans 3</SubheadBold>
            <BodyCopy className='mb-4'>
              Secondary font used for body copy and smaller text elements.
            </BodyCopy>
            <div className='space-y-2'>
              <div className='p-4 bg-gray-100 rounded-md'>
                <SubheadBold>Source Sans 3 Bold (700)</SubheadBold>
              </div>
              <div className='p-4 bg-gray-100 rounded-md'>
                <BodyCopy>Source Sans 3 Regular (400)</BodyCopy>
              </div>
              <div className='p-4 bg-gray-100 rounded-md'>
                <Callout>Source Sans 3 Italic (400)</Callout>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className='mb-12'>
        <Subhead className='mb-6'>Core Colors</Subhead>
        <BodyCopy className='mb-4'>
          These are the primary colors that define the Childfree Legacy brand.
        </BodyCopy>
        <CoreColorPalette />
      </section>

      <section className='mb-12'>
        <Subhead className='mb-6'>Secondary Colors</Subhead>
        <BodyCopy className='mb-4'>
          These colors complement the core palette and are used for accents and
          highlights.
        </BodyCopy>
        <SecondaryColorPalette />
      </section>

      <section>
        <Subhead className='mb-6'>Usage Guidelines</Subhead>
        <div className='space-y-4'>
          <SubheadBold>Logo Usage</SubheadBold>
          <BodyCopy>
            Always maintain proper spacing around the logo. The minimum margin
            should be equivalent to the height of the letter 'E' in the logo.
          </BodyCopy>
          <BodyCopy>
            Use the white logo variant on dark backgrounds (e.g., Black 6C,
            #101820) and the black logo variant on light backgrounds (e.g.,
            6197C, #D6D8D1).
          </BodyCopy>

          <SubheadBold className='mt-6'>Typography Usage</SubheadBold>
          <BodyCopy>
            Use Geologica Semi Bold for headlines, callouts, subheads, and large
            marketing text.
          </BodyCopy>
          <BodyCopy>
            Use Geologica Light for secondary emphasis in larger text (e.g.,
            subheadings).
          </BodyCopy>
          <BodyCopy>
            Use Source Sans 3 Bold for emphasis in body copy or smaller
            subheadings.
          </BodyCopy>
          <BodyCopy>
            Use Source Sans 3 Regular for body copy across all digital media.
          </BodyCopy>
        </div>
      </section>
    </div>
  );
}
