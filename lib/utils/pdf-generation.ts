import type { Document } from '@/types/documents';

// @ts-ignore
import html2pdf from 'html2pdf.js';
// @ts-ignore
import pdfMake from 'pdfmake/build/pdfmake';
// @ts-ignore
import pdfFonts from 'pdfmake/build/vfs_fonts';
// @ts-ignore
import htmlToPdfmake from 'html-to-pdfmake';

export interface PDFGenerationRequest {
  html: string;
  options?: {
    filename?: string;
    format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
    orientation?: 'portrait' | 'landscape';
    margin?: {
      top?: string;
      right?: string;
      bottom?: string;
      left?: string;
    };
    printBackground?: boolean;
    scale?: number;
    displayHeaderFooter?: boolean;
    headerTemplate?: string;
    footerTemplate?: string;
  };
  css?: string; // Optional CSS to inject
}

export interface PDFGenerationResponse {
  error?: string;
}

export interface PDFGenerationOptions {
  filename?: string;
  format?: 'A4' | 'A3' | 'A5' | 'Letter' | 'Legal' | 'Tabloid';
  orientation?: 'portrait' | 'landscape';
  margin?: {
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;
  };
  printBackground?: boolean;
  scale?: number;
  displayHeaderFooter?: boolean;
  headerTemplate?: string;
  footerTemplate?: string;
}

// Helper function to generate PDF from HTML
export async function generatePDF(
  html: string,
  options?: PDFGenerationOptions,
  css?: string
): Promise<Blob> {
  const requestBody: PDFGenerationRequest = {
    html,
    options,
    css,
  };

  const response = await fetch('/api/generate-pdf', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData = await response
      .json()
      .catch(() => ({ error: 'Unknown error' }));
    throw new Error(
      errorData.error || `HTTP error! status: ${response.status}`
    );
  }

  return response.blob();
}

// Helper function to download PDF
export function downloadPDF(blob: Blob, filename: string = 'document.pdf') {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
}

// Helper function to clean HTML content for better PDF conversion
const cleanHtmlForPdf = (html: string): string => {
  if (!html) return '';

  // Remove section markers
  let cleaned = html.replace(/<!-- \w+_SECTION -->/g, '');
  cleaned = cleaned.replace(/<!-- END_\w+_SECTION -->/g, '');

  // Ensure proper paragraph structure
  cleaned = cleaned.replace(/\n\n/g, '</p><p>');

  // Wrap in paragraph tags if not already wrapped
  if (!cleaned.includes('<p>') && !cleaned.includes('<div>')) {
    cleaned = `<p>${cleaned}</p>`;
  }

  return cleaned.trim();
};

// Helper function to create section content for pdfMake
const createSectionContent = (
  sectionHtml: string,
  sectionTitle: string,
  sectionColor: string
) => {
  if (!sectionHtml.trim()) return [];

  try {
    // Clean the HTML content
    const cleanedHtml = cleanHtmlForPdf(sectionHtml);
    console.log(`===> PROCESSING SECTION: ${sectionTitle}`, cleanedHtml);

    const sectionContent = htmlToPdfmake(cleanedHtml);

    return [
      // Section header
      {
        text: sectionTitle,
        style: 'sectionHeader',
        color: sectionColor,
        margin: [0, 20, 0, 10],
      },
      // Section divider line
      {
        canvas: [
          {
            type: 'line',
            x1: 0,
            y1: 0,
            x2: 515,
            y2: 0, // A4 width minus margins
            lineWidth: 2,
            lineColor: sectionColor,
          },
        ],
        margin: [0, 0, 0, 15],
      },
      // Section content
      ...(Array.isArray(sectionContent) ? sectionContent : [sectionContent]),
      // Add some space after section
      { text: '', margin: [0, 0, 0, 20] },
    ];
  } catch (error) {
    console.error(`Error processing section ${sectionTitle}:`, error);
    // Fallback to plain text
    return [
      {
        text: sectionTitle,
        style: 'sectionHeader',
        color: sectionColor,
        margin: [0, 20, 0, 10],
      },
      {
        text: sectionHtml.replace(/<[^>]*>/g, ''), // Strip HTML tags
        margin: [0, 0, 0, 20],
      },
    ];
  }
};

// Helper function to generate and download PDF in one step with section support
export async function generateAndDownloadPDF(
  html: string,
  options?: PDFGenerationOptions,
  css?: string
): Promise<void> {
  try {
    // Set up pdfMake with fonts
    pdfMake.vfs = pdfFonts.vfs;

    // Configure fonts - pdfMake comes with default fonts
    // We'll use the built-in fonts to avoid font definition errors
    console.log('===> SETTING UP PDFMAKE FONTS');

    // Parse the HTML to extract sections
    const sections = parseDocumentSections(html);
    console.log('===> PARSED SECTIONS', sections);

    // Build content array with proper page breaks
    const pdfContent: any[] = [];

    // Add document title if available
    const titleMatch = html.match(/<h1[^>]*>(.*?)<\/h1>/i);
    if (titleMatch) {
      pdfContent.push({
        text: titleMatch[1].replace(/<[^>]*>/g, ''), // Strip HTML tags
        style: 'documentTitle',
        alignment: 'center',
        margin: [0, 0, 0, 30],
      });
    }

    // Add first page section
    if (sections.startPage) {
      console.log('===> ADDING FIRST PAGE SECTION');
      const startPageContent = createSectionContent(
        sections.startPage,
        '📄 First Page',
        '#2563eb' // Blue color
      );
      pdfContent.push(...startPageContent);

      // Add page break after first page
      pdfContent.push({ text: '', pageBreak: 'after' });
    }

    // Add main content section
    if (sections.mainContent) {
      console.log('===> ADDING MAIN CONTENT SECTION');
      const mainContentSection = createSectionContent(
        sections.mainContent,
        '📋 Main Document',
        '#374151' // Gray color
      );
      pdfContent.push(...mainContentSection);

      // Add page break after main content if there's an end page
      if (sections.endPage) {
        pdfContent.push({ text: '', pageBreak: 'after' });
      }
    }

    // Add end page section
    if (sections.endPage) {
      console.log('===> ADDING END PAGE SECTION');
      const endPageContent = createSectionContent(
        sections.endPage,
        '📝 Last Page',
        '#059669' // Green color
      );
      pdfContent.push(...endPageContent);
    }

    // If no sections were found, fall back to converting the entire HTML
    if (pdfContent.length === 0 || (pdfContent.length === 1 && titleMatch)) {
      console.log('===> FALLING BACK TO FULL HTML CONVERSION');
      try {
        const cleanedHtml = cleanHtmlForPdf(html);
        const fallbackContent = htmlToPdfmake(cleanedHtml);
        pdfContent.push(
          ...(Array.isArray(fallbackContent)
            ? fallbackContent
            : [fallbackContent])
        );
      } catch (error) {
        console.error('Error in fallback HTML conversion:', error);
        // Ultimate fallback - plain text
        pdfContent.push({
          text: html.replace(/<[^>]*>/g, ''), // Strip all HTML tags
          style: 'sectionContent',
        });
      }
    }

    console.log('===> FINAL PDF CONTENT', pdfContent);

    // Define document structure with styles
    const docDefinition = {
      content: pdfContent,
      styles: {
        documentTitle: {
          fontSize: 22,
          bold: true,
          color: '#1f2937',
          alignment: 'center',
          margin: [0, 0, 0, 30],
        },
        sectionHeader: {
          fontSize: 18,
          bold: true,
          margin: [0, 20, 0, 10],
          decoration: 'underline',
        },
        sectionContent: {
          fontSize: 12,
          lineHeight: 1.5,
          alignment: 'justify',
        },
      },
      defaultStyle: {
        fontSize: 12,
        lineHeight: 1.5,
        color: '#374151',
      },
      pageMargins: [72, 72, 72, 72], // 1 inch margins (72 points = 1 inch)
      header: function (currentPage: number, pageCount: number) {
        if (currentPage === 1) return null; // No header on first page
        return {
          text: options?.filename?.replace('.pdf', '') || 'Document',
          alignment: 'center',
          fontSize: 10,
          color: '#6b7280',
          margin: [0, 20, 0, 0],
        };
      },
      footer: function (currentPage: number, pageCount: number) {
        return {
          text: `Page ${currentPage} of ${pageCount}`,
          alignment: 'center',
          fontSize: 10,
          color: '#6b7280',
          margin: [0, 0, 0, 20],
        };
      },
      info: {
        title: options?.filename?.replace('.pdf', '') || 'Document',
        author: 'Childfree Legacy',
        subject: 'Legal Document',
        creator: 'Childfree Legacy Platform',
        creationDate: new Date(),
      },
    };

    console.log('===> DOC DEFINITION', docDefinition);
    console.log('===> PDFMAKE FONTS', pdfMake.fonts);

    // Generate and download the PDF
    const filename = options?.filename || 'document.pdf';

    try {
      const pdfDocGenerator = pdfMake.createPdf(docDefinition);
      pdfDocGenerator.download(filename);
      console.log('===> PDF DOWNLOAD INITIATED');
    } catch (pdfError) {
      console.error('Error creating PDF:', pdfError);
      throw new Error(
        `PDF generation failed: ${pdfError instanceof Error ? pdfError.message : 'Unknown error'}`
      );
    }
  } catch (error) {
    console.error('Error generating PDF:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('Font')) {
        throw new Error(
          'PDF generation failed due to font configuration. Please try again.'
        );
      } else if (error.message.includes('htmlToPdfmake')) {
        throw new Error(
          'PDF generation failed due to content formatting. Please check your document content.'
        );
      } else {
        throw new Error(`PDF generation failed: ${error.message}`);
      }
    } else {
      throw new Error(
        'PDF generation failed due to an unknown error. Please try again.'
      );
    }
  }
}

// Parse document content into sections using markers
const parseDocumentSections = (content: string) => {
  if (!content) return { startPage: '', mainContent: '', endPage: '' };

  // Try to parse using section markers first
  const startPageMatch = content.match(
    /<!-- START_PAGE_SECTION -->\n([\s\S]*?)\n<!-- END_START_PAGE_SECTION -->/
  );
  const mainContentMatch = content.match(
    /<!-- MAIN_CONTENT_SECTION -->\n([\s\S]*?)\n<!-- END_MAIN_CONTENT_SECTION -->/
  );
  const endPageMatch = content.match(
    /<!-- END_PAGE_SECTION -->\n([\s\S]*?)\n<!-- END_END_PAGE_SECTION -->/
  );

  if (startPageMatch || mainContentMatch || endPageMatch) {
    return {
      startPage: startPageMatch ? startPageMatch[1].trim() : '',
      mainContent: mainContentMatch ? mainContentMatch[1].trim() : '',
      endPage: endPageMatch ? endPageMatch[1].trim() : '',
    };
  }

  // Fallback: Split by double line breaks (our separator)
  const sections = content.split('\n\n').filter(section => section.trim());

  // If we have 3 sections, assume start + main + end
  if (sections.length === 3) {
    return {
      startPage: sections[0],
      mainContent: sections[1],
      endPage: sections[2],
    };
  }
  // If we have 2 sections, could be start + main OR main + end
  else if (sections.length === 2) {
    return {
      startPage: '',
      mainContent: sections[0],
      endPage: sections[1],
    };
  }
  // If we have 1 section, it's just main content
  else if (sections.length === 1) {
    return {
      startPage: '',
      mainContent: sections[0],
      endPage: '',
    };
  }

  // Fallback: treat everything as main content
  return {
    startPage: '',
    mainContent: content,
    endPage: '',
  };
};

export const buildFullHtml = (document: Document): string => {
  // For the new PDF generation, we'll return the raw content
  // The parsing and sectioning will be handled in generateAndDownloadPDF
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${document.title}</title>
      <style>
        body {
          font-family: 'Times New Roman', serif;
          line-height: 1.6;
          color: #333;
          max-width: 8.5in;
          margin: 0 auto;
          padding: 1in;
        }
        h1 {
          text-align: center;
          margin-bottom: 0.5rem;
          color: #1f2937;
        }
        .document-info {
          text-align: center;
          color: #666;
          font-size: 0.9rem;
          margin-bottom: 2rem;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 1rem;
        }
        .content {
          text-align: justify;
        }
      </style>
    </head>
    <body>
      <h1>${document.title}</h1>
      <div class="document-info">
        Document Type: ${document.type} | Version: ${document.version} |
        Created: ${new Date(document.dateCreated).toLocaleDateString()}
      </div>
      <div class="content">
        ${document.content || ''}
      </div>
    </body>
    </html>
  `;
};

export const generatePdfFilename = (document: Document): string => {
  const sanitizedTitle = document.title.replace(/[^a-zA-Z0-9]/g, '_');
  const currentDate = new Date().toISOString().split('T')[0];
  return `${sanitizedTitle}_${currentDate}.pdf`;
};
