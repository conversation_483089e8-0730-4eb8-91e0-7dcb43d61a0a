import { fetchUserAttributes } from 'aws-amplify/auth';
import { createDocument, CreateDocumentData } from '@/lib/api/documents';
import {
  getMemberAvailableTemplates,
  getMemberInterviewAnswers,
} from '@/lib/api/member-documents';
import { getTemplate } from '@/app/utils/templates';

/**
 * Creates a standardized placeholder map from user attributes
 * @param userAttributes - User attributes from Cognito
 * @returns Record of placeholder keys to values
 */
const createUserAttributePlaceholders = (
  userAttributes: Record<string, any>
): Record<string, string> => {
  const fullName =
    `${userAttributes.given_name || ''} ${userAttributes.family_name || ''}`.trim() ||
    'N/A';
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return {
    // Member information - using given_name and family_name from registration
    '[Member_NAME]': fullName,
    '[MEMBER_NAME]': fullName,
    '[Member_FIRST_NAME]': userAttributes.given_name || 'N/A',
    '[Member_LAST_NAME]': userAttributes.family_name || 'N/A',
    '[FIRST_NAME]': userAttributes.given_name || 'N/A',
    '[LAST_NAME]': userAttributes.family_name || 'N/A',

    // Contact information - using email and phone_number (formatted) from registration
    '[Member_EMAIL]': userAttributes.email || 'N/A',
    '[EMAIL]': userAttributes.email || 'N/A',
    '[Member_PHONE]': userAttributes.phone_number || 'N/A',
    '[PHONE_NUMBER]': userAttributes.phone_number || 'N/A',

    // Address/State information - address contains stateLabel from registration
    '[Member_ADDRESS]': userAttributes.address || 'N/A',
    '[ADDRESS]': userAttributes.address || 'N/A',
    '[Member_STATE]': userAttributes.address || 'N/A', // address field contains state from registration
    '[STATE_LABEL]': userAttributes.address || 'N/A',

    // Personal information - using birthdate (formatted) and gender from registration
    '[Member_DOB]': userAttributes.birthdate || 'N/A',
    '[DATE_OF_BIRTH]': userAttributes.birthdate || 'N/A',
    '[BIRTHDATE]': userAttributes.birthdate || 'N/A',
    '[Member_GENDER]': userAttributes.gender || 'N/A',
    '[GENDER]': userAttributes.gender || 'N/A',

    // Document metadata
    '[TODAY_DATE]': currentDate,
    '[CURRENT_DATE]': currentDate,
  };
};

/**
 * Creates placeholder map from user interview answers
 * @param userAnswers - Array of user interview answers
 * @returns Record of placeholder keys to values
 */
const createUserAnswerPlaceholders = (
  userAnswers: any[]
): Record<string, string> => {
  const placeholders: Record<string, string> = {};

  if (!Array.isArray(userAnswers)) {
    return placeholders;
  }

  userAnswers.forEach(answerData => {
    if (answerData?.questionMapping && answerData?.answer) {
      // Remove brackets from questionMapping if they exist and add them back
      const cleanMapping = answerData.questionMapping.replace(/^\[|\]$/g, '');
      const placeholder = `[${cleanMapping}]`;
      placeholders[placeholder] = answerData.answer;
    }
  });

  return placeholders;
};

/**
 * Replaces placeholders in template content with actual values
 * @param templateContent - The template content with placeholders
 * @param placeholderMap - Map of placeholders to their replacement values
 * @returns Template content with placeholders replaced
 */
const replacePlaceholders = (
  templateContent: string,
  placeholderMap: Record<string, string>
): string => {
  let populatedTemplate = templateContent;

  Object.entries(placeholderMap).forEach(([placeholder, value]) => {
    // Create a regex that matches the placeholder (case-insensitive and global)
    const regex = new RegExp(
      placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
      'gi'
    );
    populatedTemplate = populatedTemplate.replace(regex, value);
  });

  return populatedTemplate;
};

/**
 * Fetches template content by template ID
 * @param templateId - The ID of the template to fetch
 * @returns Promise that resolves to the template content or empty string if not found
 */
const fetchTemplateContent = async (templateId: string): Promise<string> => {
  try {
    const { template, latestVersion } = await getTemplate(templateId);
    return latestVersion?.content || '';
  } catch (error) {
    console.error(
      `Error fetching template content for ID ${templateId}:`,
      error
    );
    return '';
  }
};

/**
 * Creates a document from a template with populated placeholders
 * @param template - The template to create document from
 * @param placeholderMap - Map of placeholders to their replacement values
 * @param userId - The user ID to associate with the document
 * @returns Promise that resolves when document is created
 */
const createDocumentFromTemplate = async (
  template: any,
  placeholderMap: Record<string, string>,
  userId: string
): Promise<void> => {
  const documentType = template.type || 'Legal Document';
  const documentState = template.templateState || 'Unknown State';
  const templateName =
    template.templateName || `${documentState} ${documentType}`;
  const templateContent = template.latestVersion?.content || '';

  // Add template-specific placeholders
  const templatePlaceholders = {
    '[DOCUMENT_TYPE]': documentType,
    '[DOCUMENT_STATE]': documentState,
    '[STATE]': documentState,
  };

  const allPlaceholders = { ...placeholderMap, ...templatePlaceholders };

  // Fetch start page and end page content if they exist
  const startPageContent = template.startPageTemplateId
    ? await fetchTemplateContent(template.startPageTemplateId)
    : '';
  const endPageContent = template.endPageTemplateId
    ? await fetchTemplateContent(template.endPageTemplateId)
    : '';

  // Log template structure for debugging
  console.log(`Creating document for template: ${templateName}`);
  console.log(
    `Start page template ID: ${template.startPageTemplateId || 'None'}`
  );
  console.log(`End page template ID: ${template.endPageTemplateId || 'None'}`);
  console.log(`Start page content length: ${startPageContent.length}`);
  console.log(`Main content length: ${templateContent.length}`);
  console.log(`End page content length: ${endPageContent.length}`);

  // Process placeholders in all content parts
  const populatedMainTemplate = replacePlaceholders(
    templateContent,
    allPlaceholders
  );
  const populatedStartPage = startPageContent
    ? replacePlaceholders(startPageContent, allPlaceholders)
    : '';
  const populatedEndPage = endPageContent
    ? replacePlaceholders(endPageContent, allPlaceholders)
    : '';

  // Combine all content parts with proper spacing and section markers
  const contentParts: string[] = [];

  if (populatedStartPage) {
    contentParts.push(
      `<!-- START_PAGE_SECTION -->\n${populatedStartPage}\n<!-- END_START_PAGE_SECTION -->`
    );
  }

  if (populatedMainTemplate) {
    contentParts.push(
      `<!-- MAIN_CONTENT_SECTION -->\n${populatedMainTemplate}\n<!-- END_MAIN_CONTENT_SECTION -->`
    );
  }

  if (populatedEndPage) {
    contentParts.push(
      `<!-- END_PAGE_SECTION -->\n${populatedEndPage}\n<!-- END_END_PAGE_SECTION -->`
    );
  }

  // Join content parts with double line breaks for proper separation
  const finalContent = contentParts.join('\n\n');

  console.log(`Final document content parts: ${contentParts.length}`);
  console.log(`Final content length: ${finalContent.length}`);

  const documentData: CreateDocumentData = {
    title: templateName || `${documentType} for ${documentState}`,
    type: 'Trust',
    status: 'draft',
    version: '1.0',
    content: finalContent || templateContent || '',
    templateId: template.id,
    templateContent: finalContent || templateContent,
    documentState: documentState,
  };

  await createDocument(documentData, userId);
};

/**
 * Finalizes documents by creating them from available templates with user data
 * @param userId - The user ID to create documents for
 * @returns Promise that resolves when all documents are created
 */
export const finalizeDocuments = async (userId: string): Promise<void> => {
  try {
    // Fetch all required data in parallel for better performance
    const [templates, userAnswers, userAttributes] = await Promise.all([
      getMemberAvailableTemplates(),
      getMemberInterviewAnswers(),
      fetchUserAttributes(),
    ]);

    // Create placeholder maps once
    const userAttributePlaceholders =
      createUserAttributePlaceholders(userAttributes);
    const userAnswerPlaceholders = createUserAnswerPlaceholders(userAnswers);
    const allPlaceholders = {
      ...userAttributePlaceholders,
      ...userAnswerPlaceholders,
    };

    // Process all templates in parallel for better performance
    const documentCreationPromises = templates.map(template =>
      createDocumentFromTemplate(template, allPlaceholders, userId)
    );

    await Promise.all(documentCreationPromises);

    console.log(
      `Successfully created ${templates.length} documents for user ${userId}`
    );
  } catch (error) {
    console.error('Error finalizing documents:', error);
    throw new Error(
      `Failed to finalize documents: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
};
