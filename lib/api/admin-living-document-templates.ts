'use client';

import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

export type LivingDocumentTemplate = Schema['LivingDocumentTemplate']['type'];

export const adminLivingDocumentTemplatesAPI = {
  // List all living document templates
  async listLivingDocumentTemplates(filter?: any) {
    try {
      const { data: templates, errors } =
        await client.models.LivingDocumentTemplate.list();

      if (errors) {
        console.error('Error listing living document templates:', errors);
        throw new Error('Failed to fetch living document templates');
      }

      return templates || [];
    } catch (error) {
      console.error('Error listing living document templates:', error);
      throw error;
    }
  },

  // Get a specific living document template by ID
  async getLivingDocumentTemplate(templateId: string) {
    try {
      const { data: template, errors } =
        await client.models.LivingDocumentTemplate.get({
          id: templateId,
        });

      if (errors) {
        console.error('Error fetching living document template:', errors);
        throw new Error('Failed to fetch living document template');
      }

      if (!template) {
        throw new Error(
          `Living document template not found with ID: ${templateId}`
        );
      }

      return template;
    } catch (error) {
      console.error(
        `Error fetching living document template ${templateId}:`,
        error
      );
      throw error;
    }
  },

  // Create a new living document template
  async createLivingDocumentTemplate(templateData: {
    documentType: string;
    title: string;
    description?: string;
    content?: string;
    status?: 'Draft' | 'Active' | 'Archived';
    icon?: string;
  }) {
    try {
      const user = await getCurrentUser();
      const now = new Date().toISOString();

      const { data: template, errors } =
        await client.models.LivingDocumentTemplate.create({
          createdByEmail: user.signInDetails?.loginId || '',
          documentType: templateData.documentType,
          title: templateData.title,
          icon: templateData.icon || '',
          description: templateData.description || '',
          content: templateData.content || '',
          version: 1,
          status: templateData.status || 'Draft',
          createdAt: now,
          updatedAt: now,
        });

      if (errors) {
        console.error('Error creating living document template:', errors);
        throw new Error('Failed to create living document template');
      }

      return template;
    } catch (error) {
      console.error('Error creating living document template:', error);
      throw error;
    }
  },

  // Update an existing living document template
  async updateLivingDocumentTemplate(
    templateId: string,
    templateData: {
      documentType?: string;
      title?: string;
      description?: string;
      content?: string;
      questions?: any[]; // Array of question objects
      status?: 'Draft' | 'Active' | 'Archived';
      icon?: string;
    }
  ) {
    try {
      // First, get the existing template to retrieve current version
      const { data: existingTemplate, errors: fetchErrors } =
        await client.models.LivingDocumentTemplate.get({
          id: templateId,
        });

      if (fetchErrors) {
        console.error('Error fetching existing template:', fetchErrors);
        throw new Error('Failed to fetch existing template');
      }

      if (!existingTemplate) {
        throw new Error('Template not found');
      }

      const now = new Date().toISOString();
      const newVersion = (existingTemplate.version || 1) + 1;

      const updateData: any = {
        id: templateId,
        version: newVersion,
        updatedAt: now,
      };

      // Only include fields that are provided
      if (templateData.documentType !== undefined)
        updateData.documentType = templateData.documentType;
      if (templateData.title !== undefined)
        updateData.title = templateData.title;
      if (templateData.description !== undefined)
        updateData.description = templateData.description;
      if (templateData.content !== undefined)
        updateData.content = templateData.content;
      if (templateData.questions !== undefined)
        updateData.questions = templateData.questions;
      if (templateData.status !== undefined)
        updateData.status = templateData.status;
      if (templateData.icon !== undefined) updateData.icon = templateData.icon;

      const { data: template, errors } =
        await client.models.LivingDocumentTemplate.update(updateData);

      if (errors) {
        console.error('Error updating living document template:', errors);
        throw new Error('Failed to update living document template');
      }

      return template;
    } catch (error) {
      console.error('Error updating living document template:', error);
      throw error;
    }
  },

  // Delete (archive) a living document template
  async deleteLivingDocumentTemplate(templateId: string) {
    try {
      const { data: template, errors } =
        await client.models.LivingDocumentTemplate.update({
          id: templateId,
          status: 'Archived',
          updatedAt: new Date().toISOString(),
        });

      if (errors) {
        console.error('Error archiving living document template:', errors);
        throw new Error('Failed to archive living document template');
      }

      return template;
    } catch (error) {
      console.error('Error archiving living document template:', error);
      throw error;
    }
  },
};
