'use client';

import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

// TODO: Remove this after tests and get from user profile
const DEFAULT_TEST_STATE = 'California';

// FUNCTION RETURN ALL AVAILABLE FOR MEMBER TEMPLATES
export const getMemberAvailableTemplates = async () => {
  try {
    const { data: templates } = await client.models.Template.list({
      filter: {
        isActive: { eq: true },
        templateState: { eq: DEFAULT_TEST_STATE },
        isDraft: { eq: false },
      },
    });

    const firstTemplate = templates[0];

    if (!firstTemplate) {
      return [];
    }

    // Fetch latest versions for all templates in parallel
    return await Promise.all(
      templates.map(async template => {
        try {
          const { data: versions } = await client.models.TemplateVersion.list({
            filter: {
              templateId: { eq: template.id },
            },
          });

          // Get the latest version
          const latestVersion =
            versions.length > 0
              ? versions.reduce((latest, current) =>
                  current.versionNumber > latest.versionNumber
                    ? current
                    : latest
                )
              : undefined;

          return {
            ...template,
            latestVersion,
          };
        } catch (error) {
          console.error(
            `Error fetching versions for template ${template.id}:`,
            error
          );
          return {
            ...template,
            latestVersion: undefined,
          };
        }
      })
    );
  } catch (error) {
    console.error('Error getting template:', error);
    return [];
  }
};

// FUNCTION RETURN ALL FORMATTED INTERVIEW ANSWERS FOR MEMBER
export async function getMemberInterviewAnswers() {
  try {
    const currentUser = await getCurrentUser();

    // Fetch user data by Cognito ID
    const { data: users } = await client.models.User.list({
      filter: {
        cognitoId: {
          eq: currentUser.userId,
        },
      },
      selectionSet: ['id', 'cognitoId', 'interviewProgress.*'],
    });

    if (!users || users.length === 0) {
      return [];
    }

    const user = users[0];

    const latestInterviewAnswers = user.interviewProgress?.find(
      p => p !== null && p.isCompleted
    );

    if (!latestInterviewAnswers) {
      return [];
    }

    const { data: interviewVersions } =
      await client.models.InterviewVersion.list();

    if (!interviewVersions || interviewVersions.length === 0) {
      console.log('No interview versions found');
      return [];
    }

    // Get the latest interview version by finding the highest version number
    const latestInterviewVersion = interviewVersions.reduce(
      (latest, current) =>
        current.versionNumber > latest.versionNumber ? current : latest
    );

    if (!latestInterviewVersion.questions) {
      console.log('No questions found in latest interview version');
      return [];
    }

    // Build mapping array with user answers and question mappings
    const mappingArray: Array<{
      questionId: string;
      questionMapping: string;
      answer: string;
      answeredAt: string;
    }> = [];

    // Create a map of user answers by questionId for quick lookup
    const userAnswersMap = new Map<
      string,
      { answer: string; answeredAt: string }
    >();
    if (latestInterviewAnswers.answers) {
      latestInterviewAnswers.answers.forEach(answer => {
        if (answer?.questionId && answer?.answer) {
          userAnswersMap.set(answer.questionId, {
            answer: answer.answer,
            answeredAt: answer.answeredAt || new Date().toISOString(),
          });
        }
      });
    }

    // Process each question and match with user answers
    latestInterviewVersion.questions.forEach(question => {
      // Only include questions that have a questionMapping value
      if (question?.questionMapping && question?.questionId) {
        const userAnswer = userAnswersMap.get(question.questionId);

        if (userAnswer) {
          mappingArray.push({
            questionId: question.questionId,
            questionMapping: question.questionMapping,
            answer: userAnswer.answer,
            answeredAt: userAnswer.answeredAt,
          });
        }
      }
    });

    return mappingArray ?? [];
  } catch (error) {
    console.error('Error fetching user answers:', error);
    return [];
  }
}
