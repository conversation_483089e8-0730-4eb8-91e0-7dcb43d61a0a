'use client';

import { generateClient } from 'aws-amplify/api';
import type { Schema } from '@/amplify/data/resource';
import { getCurrentUser } from 'aws-amplify/auth';

const client = generateClient<Schema>();

export type LivingDocumentTemplate = Schema['LivingDocumentTemplate']['type'];

export const livingDocumentTemplatesAPI = {
  async getLivingDocumentTemplatesForSidebar(): Promise<
    {
      id: string;
      title: string;
      description?: string | null;
      icon?: string | null;
    }[]
  > {
    try {
      const { data: livingDocumentsTemplates, errors } =
        await client.models.LivingDocumentTemplate.list({
          filter: {
            status: { eq: 'Active' },
          },
          selectionSet: ['id', 'title', 'icon', 'description'],
        });

      if (errors) {
        console.error('Error fetching living document template:', errors);
        throw new Error('Failed to fetch living document template');
      }

      return livingDocumentsTemplates;
    } catch (error) {
      console.error(`Error fetching living document templates`, error);
      throw error;
    }
  },

  async getLivingDocumentTemplate(templateId?: string) {
    try {
      if (!templateId) {
        throw new Error(
          `Living document template not found with ID: ${templateId}`
        );
      }

      const { data: template, errors } =
        await client.models.LivingDocumentTemplate.get(
          {
            id: templateId,
          },
          {
            selectionSet: ['id', 'title', 'icon', 'questions.*', 'description'],
          }
        );

      if (errors) {
        console.error('Error fetching living document template:', errors);
        throw new Error('Failed to fetch living document template');
      }

      if (!template) {
        throw new Error(
          `Living document template not found with ID: ${templateId}`
        );
      }

      return template;
    } catch (error) {
      console.error(
        `Error fetching living document template ${templateId}:`,
        error
      );
      throw error;
    }
  },
};
