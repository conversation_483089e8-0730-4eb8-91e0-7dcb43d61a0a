import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  UPSConfig,
  UPSShipmentRequest,
  UPSShipmentResponse,
  UPSTrackingResponse,
  UPSError,
} from '@/app/types/ups';
import {
  UPS_CONFIG,
  UPS_ENDPOINTS,
  validateUPSConfig,
  UPS_ERROR_MESSAGES,
} from './config';

export class UPSClient {
  private client: AxiosInstance;
  private accessToken: string | null = null;
  private tokenExpiry: Date | null = null;

  /**
   * Sanitize sensitive data for logging
   */
  private sanitizeLogData(data: any): any {
    if (!data) return data;

    const sanitized = JSON.parse(JSON.stringify(data));

    // Remove sensitive information
    if (sanitized.access_token) {
      sanitized.access_token = '[REDACTED]';
    }

    // Truncate long base64 strings (like label images)
    if (typeof sanitized === 'object') {
      Object.keys(sanitized).forEach(key => {
        if (typeof sanitized[key] === 'string' && sanitized[key].length > 100) {
          if (
            sanitized[key].startsWith('data:') ||
            sanitized[key].match(/^[A-Za-z0-9+/=]+$/)
          ) {
            sanitized[key] = `[BASE64_DATA_${sanitized[key].length}_CHARS]`;
          }
        } else if (typeof sanitized[key] === 'object') {
          sanitized[key] = this.sanitizeLogData(sanitized[key]);
        }
      });
    }

    return sanitized;
  }

  constructor(private config: UPSConfig = UPS_CONFIG) {
    if (!validateUPSConfig()) {
      throw new Error(UPS_ERROR_MESSAGES.INVALID_CONFIG);
    }

    console.log('🚀 UPS Client initialized with config:', {
      baseUrl: this.config.baseUrl,
      clientId: this.config.clientId?.substring(0, 10) + '...',
      accountNumber: this.config.accountNumber,
      environment: this.config.baseUrl.includes('onlinetools')
        ? 'PRODUCTION'
        : 'SANDBOX',
    });

    this.client = axios.create({
      baseURL: this.config.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });

    // Add request interceptor for detailed logging
    this.client.interceptors.request.use(
      config => {
        const requestId = Date.now().toString();
        console.log(`📤 UPS API Request [${requestId}]:`, {
          method: config.method?.toUpperCase(),
          url: config.url,
          baseURL: config.baseURL,
          headers: {
            ...config.headers,
            Authorization: config.headers?.Authorization
              ? '[REDACTED]'
              : undefined,
          },
          data: config.data ? this.sanitizeLogData(config.data) : undefined,
        });
        // Store requestId in headers for tracking
        config.headers['X-Request-ID'] = requestId;
        return config;
      },
      error => {
        console.error('❌ UPS API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for detailed logging and error handling
    this.client.interceptors.response.use(
      response => {
        const requestId =
          response.config.headers?.['X-Request-ID'] || 'unknown';
        console.log(`📥 UPS API Response [${requestId}]:`, {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: this.sanitizeLogData(response.data),
        });
        return response;
      },
      error => {
        const requestId = error.config?.headers?.['X-Request-ID'] || 'unknown';
        console.error(`❌ UPS API Error [${requestId}]:`, {
          status: error.response?.status,
          statusText: error.response?.statusText,
          headers: error.response?.headers,
          data: error.response?.data,
          message: error.message,
        });
        return Promise.reject(this.handleError(error));
      }
    );
  }

  /**
   * Authenticate with UPS API and get access token
   */
  private async authenticate(): Promise<string> {
    try {
      console.log('🔐 Starting UPS Authentication...', {
        clientId: this.config.clientId?.substring(0, 10) + '...',
        baseUrl: this.config.baseUrl,
        endpoint: UPS_ENDPOINTS.OAUTH,
      });

      const authString = Buffer.from(
        `${this.config.clientId}:${this.config.clientSecret}`
      ).toString('base64');

      const response = await this.client.post(
        UPS_ENDPOINTS.OAUTH,
        'grant_type=client_credentials',
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-merchant-id': this.config.clientId,
            Authorization: `Basic ${authString}`,
          },
        }
      );

      const { access_token, expires_in, token_type } = response.data;
      this.accessToken = access_token;

      // Set token expiry (subtract 5 minutes for safety)
      this.tokenExpiry = new Date(Date.now() + (expires_in - 300) * 1000);

      console.log('✅ UPS Authentication successful:', {
        tokenType: token_type,
        expiresIn: expires_in,
        expiresAt: this.tokenExpiry.toISOString(),
        tokenPreview: access_token?.substring(0, 20) + '...',
      });

      return access_token;
    } catch (error: any) {
      console.error('❌ UPS Authentication failed:', {
        error: error.response?.data || error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        clientId: this.config.clientId?.substring(0, 10) + '...',
        baseUrl: this.config.baseUrl,
        endpoint: UPS_ENDPOINTS.OAUTH,
      });
      throw new Error(UPS_ERROR_MESSAGES.AUTH_FAILED);
    }
  }

  /**
   * Get valid access token (authenticate if needed)
   */
  private async getAccessToken(): Promise<string> {
    if (
      !this.accessToken ||
      !this.tokenExpiry ||
      new Date() >= this.tokenExpiry
    ) {
      await this.authenticate();
    }
    return this.accessToken!;
  }

  /**
   * Make authenticated request to UPS API
   */
  private async makeAuthenticatedRequest<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any,
    additionalHeaders?: Record<string, string>
  ): Promise<T> {
    const transactionId = Date.now().toString();

    console.log(`🔑 Making authenticated ${method} request:`, {
      endpoint,
      transactionId,
      hasData: !!data,
      additionalHeaders: additionalHeaders
        ? Object.keys(additionalHeaders)
        : [],
    });

    const token = await this.getAccessToken();

    console.log(`📋 Request headers prepared:`, {
      transId: transactionId,
      transactionSrc: 'childfree-trust',
      hasAuth: !!token,
      tokenPreview: token?.substring(0, 20) + '...',
      additionalHeaders: additionalHeaders || {},
    });

    const response: AxiosResponse<T> = await this.client.request({
      method,
      url: endpoint,
      data,
      headers: {
        Authorization: `Bearer ${token}`,
        transId: transactionId,
        transactionSrc: 'childfree-trust',
        ...additionalHeaders,
      },
    });

    console.log(`✅ Authenticated request completed:`, {
      transactionId,
      status: response.status,
      hasResponseData: !!response.data,
    });

    return response.data;
  }

  /**
   * Create a shipment and generate shipping label
   */
  async createShipment(
    shipmentRequest: UPSShipmentRequest
  ): Promise<UPSShipmentResponse> {
    try {
      const endpoint = `${UPS_ENDPOINTS.SHIP}?additionaladdressvalidation=string`;

      const requestData = {
        ShipmentRequest: {
          Request: {
            SubVersion: '1801',
            RequestOption: 'nonvalidate',
          },
          Shipment: {
            Description: 'Package Shipment',
            Shipper: {
              Name: shipmentRequest.shipper.name,
              AttentionName: shipmentRequest.shipper.attentionName,
              TaxIdentificationNumber: this.config.accountNumber,
              Phone: {
                Number: shipmentRequest.shipper.phone || '',
              },
              ShipperNumber: this.config.accountNumber,
              Address: {
                AddressLine: [
                  shipmentRequest.shipper.address.addressLine1,
                  shipmentRequest.shipper.address.addressLine2,
                  shipmentRequest.shipper.address.addressLine3,
                ].filter(Boolean),
                City: shipmentRequest.shipper.address.city,
                StateProvinceCode:
                  shipmentRequest.shipper.address.stateProvinceCode,
                PostalCode: shipmentRequest.shipper.address.postalCode,
                CountryCode: shipmentRequest.shipper.address.countryCode,
              },
            },
            ShipTo: {
              Name: shipmentRequest.shipTo.name,
              AttentionName: shipmentRequest.shipTo.attentionName,
              Phone: {
                Number: shipmentRequest.shipTo.phone || '',
              },
              Address: {
                AddressLine: [
                  shipmentRequest.shipTo.address.addressLine1,
                  shipmentRequest.shipTo.address.addressLine2,
                  shipmentRequest.shipTo.address.addressLine3,
                ].filter(Boolean),
                City: shipmentRequest.shipTo.address.city,
                StateProvinceCode:
                  shipmentRequest.shipTo.address.stateProvinceCode,
                PostalCode: shipmentRequest.shipTo.address.postalCode,
                CountryCode: shipmentRequest.shipTo.address.countryCode,
              },
            },
            Service: {
              Code: shipmentRequest.service.code,
              Description: shipmentRequest.service.description,
            },
            PaymentInformation: {
              ShipmentCharge: {
                Type: '01', // Transportation
                BillShipper: {
                  AccountNumber: this.config.accountNumber,
                },
              },
            },
            Package: shipmentRequest.package.map(pkg => ({
              Description: pkg.description,
              Packaging: {
                Code: pkg.packaging.code,
                Description: pkg.packaging.description,
              },
              Dimensions: {
                UnitOfMeasurement: {
                  Code: pkg.dimensions.unitOfMeasurement.code,
                  Description: pkg.dimensions.unitOfMeasurement.description,
                },
                Length: pkg.dimensions.length,
                Width: pkg.dimensions.width,
                Height: pkg.dimensions.height,
              },
              PackageWeight: {
                UnitOfMeasurement: {
                  Code: pkg.packageWeight.unitOfMeasurement.code,
                  Description: pkg.packageWeight.unitOfMeasurement.description,
                },
                Weight: pkg.packageWeight.weight,
              },
            })),
          },
          LabelSpecification: {
            LabelImageFormat: {
              Code: 'GIF',
              Description: 'GIF',
            },
            HTTPUserAgent: 'Mozilla/4.0',
            LabelStockSize: {
              Height: '6',
              Width: '4',
            },
          },
        },
      };

      return await this.makeAuthenticatedRequest<UPSShipmentResponse>(
        'POST',
        endpoint,
        requestData
      );
    } catch (error) {
      console.error('Create shipment failed:', error);
      throw error;
    }
  }

  /**
   * Track a package by tracking number
   */
  async trackPackage(trackingNumber: string): Promise<UPSTrackingResponse> {
    try {
      const endpoint = `${UPS_ENDPOINTS.TRACK}/${trackingNumber}`;

      return await this.makeAuthenticatedRequest<UPSTrackingResponse>(
        'GET',
        endpoint
      );
    } catch (error) {
      console.error('Track package failed:', error);
      throw error;
    }
  }

  /**
   * Handle API errors
   */
  private handleError(error: any): Error {
    if (error.response?.data?.response?.errors) {
      const upsError = error.response.data as UPSError;
      const errorMessage = upsError.response.errors
        .map(err => `${err.code}: ${err.message}`)
        .join(', ');
      return new Error(errorMessage);
    }

    if (error.code === 'ECONNABORTED') {
      return new Error(UPS_ERROR_MESSAGES.NETWORK_ERROR);
    }

    return new Error(error.message || UPS_ERROR_MESSAGES.UNKNOWN_ERROR);
  }
}

// Export singleton instance
export const upsClient = new UPSClient();
