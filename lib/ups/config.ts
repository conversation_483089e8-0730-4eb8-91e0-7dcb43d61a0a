import { UPSConfig } from '@/app/types/ups';

// UPS API Configuration
export const UPS_CONFIG: UPSConfig = {
  clientId: process.env.UPS_CLIENT_ID || '',
  clientSecret: process.env.UPS_CLIENT_SECRET || '',
  accountNumber: process.env.UPS_ACCOUNT_NUMBER || '',
  baseUrl:
    process.env.UPS_BASE_URL ||
    (process.env.NODE_ENV === 'production'
      ? 'https://onlinetools.ups.com' // Production URL
      : 'https://wwwcie.ups.com'), // Sandbox URL
  version: 'v1',
};

// UPS API Endpoints
export const UPS_ENDPOINTS = {
  OAUTH: '/security/v1/oauth/token',
  SHIP: '/api/shipments/v1/ship',
  TRACK: '/api/track/v1/details',
  VOID: '/api/shipments/v1/void/cancel',
} as const;

// UPS Service Codes
export const UPS_SERVICE_CODES = {
  GROUND: '03',
  NEXT_DAY_AIR: '01',
  NEXT_DAY_AIR_SAVER: '13',
  NEXT_DAY_AIR_EARLY: '14',
  SECOND_DAY_AIR: '02',
  SECOND_DAY_AIR_AM: '59',
  THREE_DAY_SELECT: '12',
  STANDARD: '11',
} as const;

// UPS Package Types
export const UPS_PACKAGE_TYPES = {
  CUSTOMER_SUPPLIED: '02',
  UPS_LETTER: '01',
  UPS_TUBE: '03',
  UPS_PAK: '04',
  UPS_EXPRESS_BOX: '21',
  UPS_25KG_BOX: '24',
  UPS_10KG_BOX: '25',
} as const;

// UPS Label Image Formats
export const UPS_LABEL_FORMATS = {
  GIF: 'GIF',
  PNG: 'PNG',
  PDF: 'PDF',
} as const;

// Default package dimensions (in inches)
export const DEFAULT_PACKAGE_DIMENSIONS = {
  length: '12',
  width: '9',
  height: '3',
  weight: '1', // in pounds
};

// Validation functions
export const validateUPSConfig = (): boolean => {
  const requiredFields = ['clientId', 'clientSecret', 'accountNumber'];
  return requiredFields.every(
    field =>
      UPS_CONFIG[field as keyof UPSConfig] &&
      UPS_CONFIG[field as keyof UPSConfig].length > 0
  );
};

export const isProduction = (): boolean => {
  return UPS_CONFIG.baseUrl.includes('onlinetools.ups.com');
};

export const getEnvironmentInfo = () => {
  const isProd = isProduction();
  return {
    environment: isProd ? 'production' : 'sandbox',
    baseUrl: UPS_CONFIG.baseUrl,
    willGenerateRealLabels: isProd,
    note: isProd
      ? 'Real labels will be generated and charged to your UPS account'
      : 'Demo labels with placeholders will be generated (no charges)',
  };
};

// Error messages
export const UPS_ERROR_MESSAGES = {
  INVALID_CONFIG:
    'UPS API configuration is invalid. Please check environment variables.',
  AUTH_FAILED: 'UPS API authentication failed.',
  INVALID_ADDRESS: 'Invalid shipping address provided.',
  INVALID_PACKAGE: 'Invalid package information provided.',
  TRACKING_NOT_FOUND: 'Tracking number not found.',
  NETWORK_ERROR: 'Network error occurred while communicating with UPS API.',
  UNKNOWN_ERROR: 'An unknown error occurred.',
} as const;
